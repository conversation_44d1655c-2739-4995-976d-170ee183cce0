# DataHub 现代化重构项目

## 项目概述

本项目是对原有 DataHub 模块的完整现代化重构，采用 C++20 标准和现代软件架构设计模式。

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API 接口层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  REST API   │  │ Python API  │  │  C++ API    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   服务层 (Service Layer)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 行情服务     │  │ 历史数据服务 │  │ 指标计算服务 │          │
│  │ QuoteService│  │HistoryService│  │IndicatorSvc │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 证券信息服务 │  │ 板块管理服务 │  │ 事件总线     │          │
│  │SecurityService│ │ BlockService │  │ EventBus    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Layer)                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 数据仓库     │  │ 缓存管理     │  │ 数据提供者   │          │
│  │ Repository  │  │ CacheManager │  │ DataProvider │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   存储层 (Storage Layer)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   SQLite    │  │    HDF5     │  │   LevelDB   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则

1. **SOLID 原则**: 单一职责、开闭原则、里氏替换、接口隔离、依赖倒置
2. **现代 C++**: 使用 C++20 特性（概念、协程、模块、范围等）
3. **异步编程**: 基于协程的异步 I/O 和并发处理
4. **内存安全**: 智能指针、RAII、异常安全
5. **可测试性**: 依赖注入、接口抽象、模拟对象

### 技术栈

- **C++20**: 核心语言标准
- **CMake**: 构建系统
- **Catch2**: 单元测试框架
- **spdlog**: 日志库
- **nlohmann/json**: JSON 处理
- **fmt**: 格式化库
- **libtorch**: 机器学习支持

## 模块结构

```
DataHub_Modern/
├── include/
│   ├── core/           # 核心数据结构和接口
│   ├── services/       # 服务层接口
│   ├── data/          # 数据访问层接口
│   └── api/           # API 接口定义
├── src/
│   ├── core/          # 核心实现
│   ├── services/      # 服务层实现
│   ├── data/          # 数据访问层实现
│   └── api/           # API 实现
├── tests/             # 单元测试
├── examples/          # 示例代码
├── docs/              # 文档
└── CMakeLists.txt     # 构建配置
```

## 核心特性

### 1. 现代数据结构
- 使用 `std::variant` 替代 union
- 使用 `std::optional` 处理可选值
- 使用 `std::span` 进行安全的数组访问
- 使用 `std::chrono` 进行时间处理

### 2. 异步编程模型
- 基于 C++20 协程的异步 API
- 非阻塞 I/O 操作
- 并发数据处理

### 3. 类型安全
- 强类型枚举
- 概念约束
- 编译时检查

### 4. 内存管理
- 智能指针管理资源
- RAII 模式
- 异常安全保证

## 性能优化

1. **零拷贝数据传输**: 使用 `std::span` 和移动语义
2. **内存池**: 预分配内存减少动态分配
3. **SIMD 优化**: 向量化计算
4. **缓存友好**: 数据结构布局优化

## 兼容性

- 提供 C 风格 API 兼容层
- 支持现有数据格式
- 渐进式迁移支持

## 开发计划

1. ? 架构设计和规划
2. ? 核心数据结构实现
3. ? 数据存储层实现
4. ? 服务层实现
5. ? API 接口层实现
6. ? 单元测试编写
7. ? 性能测试和优化
8. ? 文档编写

## 构建说明

```bash
mkdir build && cd build
cmake .. -DCMAKE_CXX_STANDARD=20
make -j$(nproc)
```

## 测试运行

```bash
cd build
ctest --verbose
```
