#include "providers/IMarketDataProvider.h"
#include <chrono>
#include <algorithm>

namespace DataHub::Providers {

MarketDataProviderBase::MarketDataProviderBase(ProviderType type, std::string name)
    : type_(type), name_(std::move(name)) {
}

Core::Result<void> MarketDataProviderBase::initialize(std::shared_ptr<ProviderConfig> config) {
    if (!config) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Config cannot be null");
    }
    
    if (config->type != type_) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, 
            "Config type mismatch");
    }
    
    std::unique_lock lock(mutex_);
    config_ = std::move(config);
    initialized_ = true;
    
    return Core::make_success();
}

Core::Result<void> MarketDataProviderBase::start() {
    if (!initialized_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, 
            "Provider not initialized");
    }
    
    if (running_) {
        return Core::make_success(); // Already running
    }
    
    auto result = do_start();
    if (result.is_success()) {
        running_ = true;
    }
    
    return result;
}

Core::Result<void> MarketDataProviderBase::stop() {
    if (!running_) {
        return Core::make_success(); // Already stopped
    }
    
    auto result = do_stop();
    if (result.is_success()) {
        running_ = false;
    }
    
    return result;
}

Core::Result<void> MarketDataProviderBase::shutdown() {
    auto stop_result = stop();
    if (!stop_result.is_success()) {
        return stop_result;
    }
    
    std::unique_lock lock(mutex_);
    
    // 清理资源
    subscriptions_.clear();
    quote_callback_ = nullptr;
    bar_callback_ = nullptr;
    tick_callback_ = nullptr;
    error_callback_ = nullptr;
    status_callback_ = nullptr;
    
    initialized_ = false;
    
    return Core::make_success();
}

bool MarketDataProviderBase::is_connected() const noexcept {
    std::shared_lock lock(mutex_);
    return connection_info_.status == ConnectionStatus::Connected;
}

ConnectionInfo MarketDataProviderBase::get_connection_info() const {
    std::shared_lock lock(mutex_);
    return connection_info_;
}

std::vector<SubscriptionInfo> MarketDataProviderBase::get_subscriptions() const {
    std::shared_lock lock(mutex_);
    std::vector<SubscriptionInfo> result;
    result.reserve(subscriptions_.size());
    
    for (const auto& [symbol, info] : subscriptions_) {
        result.push_back(info);
    }
    
    return result;
}

SubscriptionInfo MarketDataProviderBase::get_subscription_info(const Core::Symbol& symbol) const {
    std::shared_lock lock(mutex_);
    auto it = subscriptions_.find(symbol);
    if (it != subscriptions_.end()) {
        return it->second;
    }
    
    // 返回未订阅状态
    SubscriptionInfo info;
    info.symbol = symbol;
    info.status = SubscriptionStatus::Unsubscribed;
    return info;
}

void MarketDataProviderBase::set_quote_callback(QuoteCallback callback) {
    std::unique_lock lock(mutex_);
    quote_callback_ = std::move(callback);
}

void MarketDataProviderBase::set_bar_callback(BarCallback callback) {
    std::unique_lock lock(mutex_);
    bar_callback_ = std::move(callback);
}

void MarketDataProviderBase::set_tick_callback(TickCallback callback) {
    std::unique_lock lock(mutex_);
    tick_callback_ = std::move(callback);
}

void MarketDataProviderBase::set_error_callback(ErrorCallback callback) {
    std::unique_lock lock(mutex_);
    error_callback_ = std::move(callback);
}

void MarketDataProviderBase::set_status_callback(StatusCallback callback) {
    std::unique_lock lock(mutex_);
    status_callback_ = std::move(callback);
}

ProviderType MarketDataProviderBase::get_type() const noexcept {
    return type_;
}

std::string MarketDataProviderBase::get_name() const noexcept {
    return name_;
}

std::string MarketDataProviderBase::get_version() const noexcept {
    return "1.0.0";
}

std::size_t MarketDataProviderBase::get_quote_count() const noexcept {
    return quote_count_;
}

std::size_t MarketDataProviderBase::get_error_count() const noexcept {
    return error_count_;
}

std::chrono::milliseconds MarketDataProviderBase::get_average_latency() const noexcept {
    return average_latency_;
}

void MarketDataProviderBase::update_connection_status(ConnectionStatus status, 
                                                     const std::string& error) {
    {
        std::unique_lock lock(mutex_);
        connection_info_.status = status;
        connection_info_.error_message = error;
        
        if (status == ConnectionStatus::Connected) {
            connection_info_.connect_time = std::chrono::system_clock::now();
            connection_info_.retry_count = 0;
        } else if (status == ConnectionStatus::Error || status == ConnectionStatus::Disconnected) {
            connection_info_.retry_count++;
        }
        
        connection_info_.last_heartbeat = std::chrono::system_clock::now();
    }
    
    // 通知状态变化
    if (status_callback_) {
        try {
            status_callback_(status);
        } catch (const std::exception& e) {
            // 记录回调异常，但不影响主流程
            if (error_callback_) {
                error_callback_("Status callback exception: " + std::string(e.what()));
            }
        }
    }
}

void MarketDataProviderBase::update_subscription_status(const Core::Symbol& symbol, 
                                                        SubscriptionStatus status,
                                                        const std::string& error) {
    std::unique_lock lock(mutex_);
    
    auto& info = subscriptions_[symbol];
    info.symbol = symbol;
    info.status = status;
    info.error_message = error;
    
    if (status == SubscriptionStatus::Subscribed) {
        info.subscribe_time = std::chrono::system_clock::now();
    }
}

void MarketDataProviderBase::notify_quote(const Core::QuoteData& quote) {
    increment_quote_count();
    
    if (quote_callback_) {
        try {
            quote_callback_(quote);
        } catch (const std::exception& e) {
            increment_error_count();
            if (error_callback_) {
                error_callback_("Quote callback exception: " + std::string(e.what()));
            }
        }
    }
}

void MarketDataProviderBase::notify_bar(const Core::BarData& bar) {
    if (bar_callback_) {
        try {
            bar_callback_(bar);
        } catch (const std::exception& e) {
            increment_error_count();
            if (error_callback_) {
                error_callback_("Bar callback exception: " + std::string(e.what()));
            }
        }
    }
}

void MarketDataProviderBase::notify_tick(const Core::TickData& tick) {
    if (tick_callback_) {
        try {
            tick_callback_(tick);
        } catch (const std::exception& e) {
            increment_error_count();
            if (error_callback_) {
                error_callback_("Tick callback exception: " + std::string(e.what()));
            }
        }
    }
}

void MarketDataProviderBase::notify_error(const std::string& error) {
    increment_error_count();
    
    if (error_callback_) {
        try {
            error_callback_(error);
        } catch (const std::exception& e) {
            // 避免递归错误
        }
    }
}

void MarketDataProviderBase::increment_quote_count() {
    quote_count_++;
}

void MarketDataProviderBase::increment_error_count() {
    error_count_++;
}

void MarketDataProviderBase::update_latency(std::chrono::milliseconds latency) {
    // 简单的移动平均
    auto current = average_latency_.load();
    auto new_avg = std::chrono::milliseconds(
        (current.count() * 9 + latency.count()) / 10);
    average_latency_ = new_avg;
}

} // namespace DataHub::Providers
