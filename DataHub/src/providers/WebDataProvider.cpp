#include "providers/WebDataProvider.h"
#include "core/Logging.h"
#include <algorithm>
#include <future>
#include <thread>

namespace DataHub::Providers {

// 外部函数声明
std::unique_ptr<IHttpClient> create_http_client();

WebDataProvider::WebDataProvider()
    : MarketDataProviderBase(ProviderType::WebData_Stock, "WebData_Stock_Provider")
    , stop_update_(false) {
}

WebDataProvider::~WebDataProvider() {
    stop();
}

Core::Result<void> WebDataProvider::connect() {
    return do_connect();
}

Core::Result<void> WebDataProvider::disconnect() {
    return do_disconnect();
}

Core::Result<void> WebDataProvider::do_connect() {
    try {
        // 确保配置已设置
        if (!web_config_) {
            web_config_ = std::static_pointer_cast<WebDataConfig>(config_);
            if (!web_config_) {
                return Core::make_error<void>(Core::ErrorCode::NotInitialized,
                    "WebData config not set");
            }
        }

        // 创建HTTP客户端
        http_client_ = create_http_client();
        if (!http_client_) {
            return Core::make_error<void>(Core::ErrorCode::InitializationFailed,
                "Failed to create HTTP client");
        }

        // 设置HTTP客户端配置
        if (web_config_) {
            http_client_->set_user_agent(web_config_->user_agent);
            http_client_->set_headers(web_config_->headers);
        }
        
        // 创建数据解析器
        switch (web_config_->data_source) {
            case WebDataSource::Sina:
                data_parser_ = std::make_unique<SinaDataParser>();
                break;
            case WebDataSource::Tencent:
                data_parser_ = std::make_unique<TencentDataParser>();
                break;
            default:
                data_parser_ = std::make_unique<SinaDataParser>();
                break;
        }
        
        if (!data_parser_) {
            return Core::make_error<void>(Core::ErrorCode::InitializationFailed,
                "Failed to create data parser");
        }
        
        update_connection_status(ConnectionStatus::Connected);
        
        Core::log_info("WebData", "Connected to web data source");
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ConnectionFailed,
            "WebData connect failed: " + std::string(e.what()));
    }
}

Core::Result<void> WebDataProvider::do_disconnect() {
    try {
        http_client_.reset();
        data_parser_.reset();
        
        update_connection_status(ConnectionStatus::Disconnected);
        
        Core::log_info("WebData", "Disconnected from web data source");
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::DisconnectionFailed,
            "WebData disconnect failed: " + std::string(e.what()));
    }
}

Core::Result<void> WebDataProvider::do_start() {
    start_data_update();
    return Core::make_success();
}

Core::Result<void> WebDataProvider::do_stop() {
    stop_data_update();
    return Core::make_success();
}

Core::Result<void> WebDataProvider::subscribe_quote(const Core::Symbol& symbol) {
    return do_subscribe(symbol);
}

Core::Result<void> WebDataProvider::subscribe_quotes(const std::vector<Core::Symbol>& symbols) {
    for (const auto& symbol : symbols) {
        auto result = subscribe_quote(symbol);
        if (!result.is_success()) {
            return result;
        }
    }
    return Core::make_success();
}

Core::Result<void> WebDataProvider::unsubscribe_quote(const Core::Symbol& symbol) {
    return do_unsubscribe(symbol);
}

Core::Result<void> WebDataProvider::unsubscribe_all() {
    std::unique_lock lock(symbols_mutex_);
    subscribed_symbols_.clear();
    lock.unlock();
    
    // 清理所有订阅状态
    auto subscriptions = get_subscriptions();
    for (const auto& sub : subscriptions) {
        update_subscription_status(sub.symbol, SubscriptionStatus::Unsubscribed);
    }
    
    return Core::make_success();
}

Core::Result<void> WebDataProvider::do_subscribe(const Core::Symbol& symbol) {
    {
        std::unique_lock lock(symbols_mutex_);
        subscribed_symbols_.insert(symbol);
    }
    
    update_subscription_status(symbol, SubscriptionStatus::Subscribed);
    
    Core::log_info("WebData", "Subscribed to: {}", symbol);
    
    return Core::make_success();
}

Core::Result<void> WebDataProvider::do_unsubscribe(const Core::Symbol& symbol) {
    {
        std::unique_lock lock(symbols_mutex_);
        subscribed_symbols_.erase(symbol);
    }
    
    update_subscription_status(symbol, SubscriptionStatus::Unsubscribed);
    
    Core::log_info("WebData", "Unsubscribed from: {}", symbol);
    
    return Core::make_success();
}

Core::Result<Core::QuoteData> WebDataProvider::get_latest_quote(const Core::Symbol& symbol) {
    // 首先检查缓存
    auto cached = get_from_cache(symbol);
    if (cached.has_value()) {
        cache_hit_count_++;
        return Core::make_result<Core::QuoteData>(cached.value());
    }
    
    cache_miss_count_++;
    
    // 从网络获取数据
    if (!http_client_ || !data_parser_) {
        return Core::make_error<Core::QuoteData>(Core::ErrorCode::NotConnected,
            "WebData provider not connected");
    }
    
    try {
        std::vector<Core::Symbol> symbols = {symbol};
        std::string url = data_parser_->build_quote_url(symbols);
        
        auto response = request_with_retry(url);
        if (!response.is_success()) {
            return Core::make_error<Core::QuoteData>(response.error().code(),
                "Failed to fetch quote data: " + response.error().message());
        }
        
        auto quotes_result = data_parser_->parse_quotes(response.value(), symbols);
        if (!quotes_result.is_success()) {
            return Core::make_error<Core::QuoteData>(quotes_result.error().code(),
                "Failed to parse quote data: " + quotes_result.error().message());
        }
        
        auto quotes = quotes_result.value();
        if (quotes.empty()) {
            return Core::make_error<Core::QuoteData>(Core::ErrorCode::DataNotFound,
                "No quote data found for symbol: " + symbol);
        }
        
        // 更新缓存
        update_cache(symbol, quotes[0]);
        
        return Core::make_result<Core::QuoteData>(quotes[0]);
        
    } catch (const std::exception& e) {
        return Core::make_error<Core::QuoteData>(Core::ErrorCode::NetworkError,
            "Get quote failed: " + std::string(e.what()));
    }
}

Core::Result<std::vector<Core::QuoteData>> WebDataProvider::get_quotes(
    const std::vector<Core::Symbol>& symbols) {
    
    if (!http_client_ || !data_parser_) {
        return Core::make_error<std::vector<Core::QuoteData>>(Core::ErrorCode::NotConnected,
            "WebData provider not connected");
    }
    
    try {
        std::vector<Core::QuoteData> all_quotes;
        
        // 分批处理符号
        auto batches = split_symbols_into_batches(symbols);
        
        for (const auto& batch : batches) {
            std::string url = data_parser_->build_quote_url(batch);
            
            auto response = request_with_retry(url);
            if (!response.is_success()) {
                Core::log_warn("WebData", "Failed to fetch batch: {}", response.error().message());
                continue;
            }
            
            auto quotes_result = data_parser_->parse_quotes(response.value(), batch);
            if (!quotes_result.is_success()) {
                Core::log_warn("WebData", "Failed to parse batch: {}", quotes_result.error().message());
                continue;
            }
            
            auto quotes = quotes_result.value();
            
            // 更新缓存并添加到结果
            for (const auto& quote : quotes) {
                update_cache(quote.symbol, quote);
                all_quotes.push_back(quote);
            }
        }
        
        return Core::make_result<std::vector<Core::QuoteData>>(std::move(all_quotes));
        
    } catch (const std::exception& e) {
        return Core::make_error<std::vector<Core::QuoteData>>(Core::ErrorCode::NetworkError,
            "Get quotes failed: " + std::string(e.what()));
    }
}

std::future<Core::Result<Core::QuoteData>> WebDataProvider::get_latest_quote_async(
    const Core::Symbol& symbol) {
    
    return std::async(std::launch::async, [this, symbol]() {
        return get_latest_quote(symbol);
    });
}

std::future<Core::Result<std::vector<Core::QuoteData>>> WebDataProvider::get_quotes_async(
    const std::vector<Core::Symbol>& symbols) {
    
    return std::async(std::launch::async, [this, symbols]() {
        return get_quotes(symbols);
    });
}

Core::Result<bool> WebDataProvider::health_check() {
    if (!http_client_ || !data_parser_) {
        return Core::make_result<bool>(false);
    }
    
    // 尝试获取一个测试股票的数据
    try {
        std::vector<Core::Symbol> test_symbols = {"000001.SZ"};
        std::string url = data_parser_->build_quote_url(test_symbols);
        
        auto response = http_client_->get(url, std::chrono::milliseconds(5000));
        return Core::make_result<bool>(response.is_success());
        
    } catch (const std::exception&) {
        return Core::make_result<bool>(false);
    }
}

std::string WebDataProvider::get_version() const noexcept {
    return "1.0.0";
}

double WebDataProvider::get_cache_hit_ratio() const noexcept {
    auto hits = cache_hit_count_.load();
    auto misses = cache_miss_count_.load();
    auto total = hits + misses;

    if (total == 0) return 0.0;
    return static_cast<double>(hits) / total;
}

void WebDataProvider::start_data_update() {
    stop_update_ = false;

    update_thread_ = std::thread([this]() {
        Core::log_info("WebData", "Data update thread started");

        while (!stop_update_) {
            try {
                update_quotes();

                // 等待下次更新
                auto interval = web_config_ ? web_config_->update_interval :
                               std::chrono::milliseconds(3000);

                std::this_thread::sleep_for(interval);

            } catch (const std::exception& e) {
                Core::log_error("WebData", "Update thread error: {}", e.what());
                notify_error("Update thread error: " + std::string(e.what()));

                // 出错后等待一段时间再继续
                std::this_thread::sleep_for(std::chrono::seconds(5));
            }
        }

        Core::log_info("WebData", "Data update thread stopped");
    });
}

void WebDataProvider::stop_data_update() {
    stop_update_ = true;

    if (update_thread_.joinable()) {
        update_thread_.join();
    }
}

void WebDataProvider::update_quotes() {
    std::shared_lock lock(symbols_mutex_);
    if (subscribed_symbols_.empty()) {
        return;
    }

    std::vector<Core::Symbol> symbols(subscribed_symbols_.begin(), subscribed_symbols_.end());
    lock.unlock();

    // 分批更新
    auto batches = split_symbols_into_batches(symbols);

    for (const auto& batch : batches) {
        update_quotes_batch(batch);
    }
}

void WebDataProvider::update_quotes_batch(const std::vector<Core::Symbol>& symbols) {
    if (!http_client_ || !data_parser_) {
        return;
    }

    try {
        std::string url = data_parser_->build_quote_url(symbols);

        auto response = request_with_retry(url);
        if (!response.is_success()) {
            Core::log_warn("WebData", "Failed to update batch: {}", response.error().message());
            return;
        }

        auto quotes_result = data_parser_->parse_quotes(response.value(), symbols);
        if (!quotes_result.is_success()) {
            Core::log_warn("WebData", "Failed to parse batch: {}", quotes_result.error().message());
            return;
        }

        auto quotes = quotes_result.value();

        // 通知新的行情数据
        for (const auto& quote : quotes) {
            update_cache(quote.symbol, quote);
            notify_quote(quote);
        }

    } catch (const std::exception& e) {
        Core::log_error("WebData", "Update batch error: {}", e.what());
        notify_error("Update batch error: " + std::string(e.what()));
    }
}

bool WebDataProvider::is_cache_valid(const CachedQuote& cached) const {
    if (!web_config_ || !web_config_->enable_cache) {
        return false;
    }

    auto now = std::chrono::system_clock::now();
    auto age = std::chrono::duration_cast<std::chrono::seconds>(now - cached.timestamp);

    return age < web_config_->cache_ttl;
}

void WebDataProvider::update_cache(const Core::Symbol& symbol, const Core::QuoteData& quote) {
    if (!web_config_ || !web_config_->enable_cache) {
        return;
    }

    std::unique_lock lock(cache_mutex_);

    CachedQuote cached;
    cached.quote = quote;
    cached.timestamp = std::chrono::system_clock::now();

    quote_cache_[symbol] = cached;

    // 限制缓存大小
    if (quote_cache_.size() > 10000) {  // 简单的缓存清理
        auto oldest = quote_cache_.begin();
        for (auto it = quote_cache_.begin(); it != quote_cache_.end(); ++it) {
            if (it->second.timestamp < oldest->second.timestamp) {
                oldest = it;
            }
        }
        quote_cache_.erase(oldest);
    }
}

std::optional<Core::QuoteData> WebDataProvider::get_from_cache(const Core::Symbol& symbol) const {
    if (!web_config_ || !web_config_->enable_cache) {
        return std::nullopt;
    }

    std::shared_lock lock(cache_mutex_);

    auto it = quote_cache_.find(symbol);
    if (it != quote_cache_.end() && is_cache_valid(it->second)) {
        return it->second.quote;
    }

    return std::nullopt;
}

std::vector<std::vector<Core::Symbol>> WebDataProvider::split_symbols_into_batches(
    const std::vector<Core::Symbol>& symbols) const {

    std::vector<std::vector<Core::Symbol>> batches;

    std::size_t batch_size = web_config_ ? web_config_->max_symbols_per_request : 100;

    for (std::size_t i = 0; i < symbols.size(); i += batch_size) {
        std::size_t end = std::min(i + batch_size, symbols.size());
        batches.emplace_back(symbols.begin() + i, symbols.begin() + end);
    }

    return batches;
}

Core::Result<std::string> WebDataProvider::request_with_retry(const std::string& url, int max_retries) {
    for (int retry = 0; retry <= max_retries; ++retry) {
        try {
            request_count_++;

            auto timeout = web_config_ ? web_config_->request_timeout :
                          std::chrono::milliseconds(5000);

            auto response = http_client_->get(url, timeout);

            if (response.is_success()) {
                return response;
            }

            if (retry < max_retries) {
                Core::log_warn("WebData", "Request failed (retry {}/{}): {}",
                              retry + 1, max_retries, response.error().message());

                // 等待一段时间后重试
                std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (retry + 1)));
            } else {
                return response;
            }

        } catch (const std::exception& e) {
            if (retry < max_retries) {
                Core::log_warn("WebData", "Request exception (retry {}/{}): {}",
                              retry + 1, max_retries, e.what());
                std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (retry + 1)));
            } else {
                return Core::make_error<std::string>(Core::ErrorCode::NetworkError,
                    "Request failed after retries: " + std::string(e.what()));
            }
        }
    }

    return Core::make_error<std::string>(Core::ErrorCode::NetworkError, "Max retries exceeded");
}

// WebData特定方法实现
Core::Result<std::vector<Core::BarData>> WebDataProvider::get_history_bars(
    const Core::Symbol& symbol,
    Core::BarSize bar_size,
    const Core::Timestamp& start_time,
    const Core::Timestamp& end_time) {

    if (!http_client_ || !data_parser_) {
        return Core::make_error<std::vector<Core::BarData>>(Core::ErrorCode::NotConnected,
            "WebData provider not connected");
    }

    try {
        std::string url = data_parser_->build_history_url(symbol, bar_size, start_time, end_time);

        auto response = request_with_retry(url);
        if (!response.is_success()) {
            return Core::make_error<std::vector<Core::BarData>>(response.error().code(),
                "Failed to fetch history data: " + response.error().message());
        }

        return data_parser_->parse_bars(response.value(), symbol, bar_size);

    } catch (const std::exception& e) {
        return Core::make_error<std::vector<Core::BarData>>(Core::ErrorCode::NetworkError,
            "Get history bars failed: " + std::string(e.what()));
    }
}

Core::Result<void> WebDataProvider::set_data_source(WebDataSource source) {
    if (!web_config_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Config not set");
    }

    web_config_->data_source = source;

    // 重新创建解析器
    switch (source) {
        case WebDataSource::Sina:
            data_parser_ = std::make_unique<SinaDataParser>();
            break;
        case WebDataSource::Tencent:
            data_parser_ = std::make_unique<TencentDataParser>();
            break;
        default:
            data_parser_ = std::make_unique<SinaDataParser>();
            break;
    }

    return Core::make_success();
}

Core::Result<void> WebDataProvider::set_update_interval(std::chrono::milliseconds interval) {
    if (!web_config_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Config not set");
    }

    web_config_->update_interval = interval;
    return Core::make_success();
}

} // namespace DataHub::Providers
