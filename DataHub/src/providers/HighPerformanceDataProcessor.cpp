#include "providers/HighPerformanceDataProcessor.h"
#include "core/Logging.h"
#include <algorithm>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#include <processthreadsapi.h>
#else
#include <pthread.h>
#include <sched.h>
#include <unistd.h>
#endif

namespace DataHub::Providers {

HighPerformanceDataProcessor::HighPerformanceDataProcessor(DataProcessorConfig config)
    : config_(std::move(config))
    , task_queue_(config_.queue_capacity) {
    
    // 验证配置
    if (config_.worker_thread_count == 0) {
        config_.worker_thread_count = std::thread::hardware_concurrency();
    }
    
    if (config_.batch_size == 0) {
        config_.batch_size = 100;
    }
    
    Core::log_info("DataProcessor", "Created with {} worker threads, batch size {}, queue capacity {}",
                  config_.worker_thread_count, config_.batch_size, config_.queue_capacity);
}

HighPerformanceDataProcessor::~HighPerformanceDataProcessor() {
    stop();
}

bool HighPerformanceDataProcessor::start() {
    if (running_.load(std::memory_order_acquire)) {
        return true;
    }
    
    try {
        stop_requested_.store(false, std::memory_order_release);
        running_.store(true, std::memory_order_release);
        
        // 创建工作线程
        worker_threads_.reserve(config_.worker_thread_count);
        for (std::size_t i = 0; i < config_.worker_thread_count; ++i) {
            worker_threads_.emplace_back([this, i]() {
                worker_thread_function(static_cast<int>(i));
            });
            
            // 设置CPU亲和性
            if (config_.enable_affinity && i < config_.cpu_cores.size()) {
                set_thread_affinity(static_cast<int>(i));
            }
        }
        
        Core::log_info("DataProcessor", "Started with {} worker threads", worker_threads_.size());
        return true;
        
    } catch (const std::exception& e) {
        running_.store(false, std::memory_order_release);
        handle_error("Failed to start data processor: " + std::string(e.what()));
        return false;
    }
}

void HighPerformanceDataProcessor::stop() {
    if (!running_.load(std::memory_order_acquire)) {
        return;
    }
    
    Core::log_info("DataProcessor", "Stopping data processor...");
    
    stop_requested_.store(true, std::memory_order_release);
    
    // 等待所有工作线程完成
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    worker_threads_.clear();
    running_.store(false, std::memory_order_release);
    
    Core::log_info("DataProcessor", "Data processor stopped");
}

void HighPerformanceDataProcessor::worker_thread_function(int thread_id) {
    Core::log_debug("DataProcessor", "Worker thread {} started", thread_id);
    
    // 批处理缓冲区
    std::vector<DataTask> batch_buffer;
    batch_buffer.reserve(config_.batch_size);
    
    while (!stop_requested_.load(std::memory_order_acquire)) {
        batch_buffer.clear();
        
        // 尝试批量出队
        std::size_t dequeued = task_queue_.try_dequeue_bulk(
            std::back_inserter(batch_buffer), config_.batch_size);
        
        if (dequeued > 0) {
            // 处理批量任务
            process_task_batch(batch_buffer.data(), dequeued, thread_id);
        } else {
            // 没有任务时短暂休眠
            std::this_thread::sleep_for(config_.spin_wait_time);
        }
    }
    
    // 处理剩余任务
    batch_buffer.clear();
    std::size_t remaining = task_queue_.try_dequeue_bulk(
        std::back_inserter(batch_buffer), config_.batch_size);
    
    if (remaining > 0) {
        process_task_batch(batch_buffer.data(), remaining, thread_id);
    }
    
    Core::log_debug("DataProcessor", "Worker thread {} stopped", thread_id);
}

void HighPerformanceDataProcessor::process_task_batch(DataTask* tasks, std::size_t count, int thread_id) {
    for (std::size_t i = 0; i < count; ++i) {
        auto& task = tasks[i];
        
        try {
            auto start_time = std::chrono::high_resolution_clock::now();
            
            // 执行任务
            task.processor();
            
            auto end_time = std::chrono::high_resolution_clock::now();
            auto processing_time = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            
            // 更新统计信息
            stats_.processed_tasks.fetch_add(1, std::memory_order_relaxed);
            update_processing_time_stats(static_cast<std::uint64_t>(processing_time));
            
        } catch (const std::exception& e) {
            stats_.failed_tasks.fetch_add(1, std::memory_order_relaxed);
            handle_error("Task processing failed in thread " + std::to_string(thread_id) + 
                        ": " + std::string(e.what()));
        }
    }
}

void HighPerformanceDataProcessor::set_thread_affinity(int thread_id) {
    if (!config_.enable_affinity || thread_id >= static_cast<int>(config_.cpu_cores.size())) {
        return;
    }
    
    int cpu_core = config_.cpu_cores[thread_id];
    
#ifdef _WIN32
    DWORD_PTR mask = 1ULL << cpu_core;
    if (SetThreadAffinityMask(worker_threads_[thread_id].native_handle(), mask) == 0) {
        Core::log_warn("DataProcessor", "Failed to set CPU affinity for thread {} to core {}", 
                      thread_id, cpu_core);
    } else {
        Core::log_debug("DataProcessor", "Set CPU affinity for thread {} to core {}", 
                       thread_id, cpu_core);
    }
#else
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(cpu_core, &cpuset);
    
    if (pthread_setaffinity_np(worker_threads_[thread_id].native_handle(), 
                              sizeof(cpu_set_t), &cpuset) != 0) {
        Core::log_warn("DataProcessor", "Failed to set CPU affinity for thread {} to core {}", 
                      thread_id, cpu_core);
    } else {
        Core::log_debug("DataProcessor", "Set CPU affinity for thread {} to core {}", 
                       thread_id, cpu_core);
    }
#endif
}

void HighPerformanceDataProcessor::handle_error(const std::string& error) {
    Core::log_error("DataProcessor", error);
    
    if (error_callback_) {
        try {
            error_callback_(error);
        } catch (const std::exception& e) {
            Core::log_error("DataProcessor", "Error callback failed: {}", e.what());
        }
    }
}

void HighPerformanceDataProcessor::update_processing_time_stats(std::uint64_t processing_time_ns) {
    stats_.total_processing_time_ns.fetch_add(processing_time_ns, std::memory_order_relaxed);
    
    // 更新最大处理时间
    auto current_max = stats_.max_processing_time_ns.load(std::memory_order_relaxed);
    while (processing_time_ns > current_max) {
        if (stats_.max_processing_time_ns.compare_exchange_weak(
                current_max, processing_time_ns, std::memory_order_relaxed)) {
            break;
        }
    }
    
    // 更新最小处理时间
    auto current_min = stats_.min_processing_time_ns.load(std::memory_order_relaxed);
    while (processing_time_ns < current_min) {
        if (stats_.min_processing_time_ns.compare_exchange_weak(
                current_min, processing_time_ns, std::memory_order_relaxed)) {
            break;
        }
    }
}

void HighPerformanceDataProcessor::reset_statistics() noexcept {
    stats_.total_tasks.store(0, std::memory_order_relaxed);
    stats_.processed_tasks.store(0, std::memory_order_relaxed);
    stats_.failed_tasks.store(0, std::memory_order_relaxed);
    stats_.queue_full_count.store(0, std::memory_order_relaxed);
    stats_.total_processing_time_ns.store(0, std::memory_order_relaxed);
    stats_.max_processing_time_ns.store(0, std::memory_order_relaxed);
    stats_.min_processing_time_ns.store(UINT64_MAX, std::memory_order_relaxed);
}

// QuoteDataProcessor实现
QuoteDataProcessor::QuoteDataProcessor(DataProcessorConfig config)
    : processor_(std::move(config)) {
}

// 便利函数实现
namespace Utils {

std::unique_ptr<HighPerformanceDataProcessor> create_high_performance_processor(
    std::size_t worker_threads, std::size_t batch_size, std::size_t queue_capacity) {
    
    DataProcessorConfig config;
    config.worker_thread_count = worker_threads;
    config.batch_size = batch_size;
    config.queue_capacity = queue_capacity;
    
    return std::make_unique<HighPerformanceDataProcessor>(config);
}

std::unique_ptr<QuoteDataProcessor> create_quote_processor(std::size_t worker_threads) {
    DataProcessorConfig config;
    config.worker_thread_count = worker_threads;
    config.batch_size = 50;  // 行情数据批量处理较小
    config.queue_capacity = 5000;
    
    return std::make_unique<QuoteDataProcessor>(config);
}

DataProcessorConfig get_optimal_config() {
    DataProcessorConfig config;
    
    // 根据CPU核心数设置工作线程数
    auto cpu_count = std::thread::hardware_concurrency();
    config.worker_thread_count = std::max(2u, cpu_count / 2);  // 使用一半的CPU核心
    
    // 根据内存大小调整队列容量
    config.queue_capacity = 10000;
    config.batch_size = 100;
    config.spin_wait_time = std::chrono::microseconds(50);
    
    // 启用CPU亲和性（如果有足够的核心）
    if (cpu_count >= 4) {
        config.enable_affinity = true;
        config.cpu_cores = get_available_cpu_cores();
    }
    
    return config;
}

bool set_cpu_affinity(std::thread& thread, int cpu_core) {
#ifdef _WIN32
    DWORD_PTR mask = 1ULL << cpu_core;
    return SetThreadAffinityMask(thread.native_handle(), mask) != 0;
#else
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(cpu_core, &cpuset);
    return pthread_setaffinity_np(thread.native_handle(), sizeof(cpu_set_t), &cpuset) == 0;
#endif
}

std::vector<int> get_available_cpu_cores() {
    std::vector<int> cores;
    auto cpu_count = std::thread::hardware_concurrency();
    
    for (unsigned int i = 0; i < cpu_count; ++i) {
        cores.push_back(static_cast<int>(i));
    }
    
    return cores;
}

} // namespace Utils

} // namespace DataHub::Providers
