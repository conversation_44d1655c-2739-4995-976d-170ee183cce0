#include "providers/CtpMarketDataProvider.h"
#include "providers/WebDataProvider.h"
#include "core/Logging.h"

namespace DataHub::Providers {

// CTP提供者工厂实现
std::unique_ptr<IMarketDataProvider> CtpProviderFactory::create_provider(
    ProviderType type, 
    std::shared_ptr<ProviderConfig> config) {
    
    if (type != ProviderType::CTP_Futures) {
        return nullptr;
    }
    
    auto ctp_config = std::dynamic_pointer_cast<CtpConfig>(config);
    if (!ctp_config) {
        Core::log_error("CtpFactory", "Invalid config type for CTP provider");
        return nullptr;
    }
    
    return create_ctp_provider(ctp_config);
}

std::vector<ProviderType> CtpProviderFactory::get_supported_types() const {
    return {ProviderType::CTP_Futures};
}

bool CtpProviderFactory::supports_type(ProviderType type) const {
    return type == ProviderType::CTP_Futures;
}

std::unique_ptr<CtpMarketDataProvider> CtpProviderFactory::create_ctp_provider(
    std::shared_ptr<CtpConfig> config) {
    
    try {
        auto provider = std::make_unique<CtpMarketDataProvider>();
        
        auto init_result = provider->initialize(config);
        if (!init_result.is_success()) {
            Core::log_error("CtpFactory", "Failed to initialize CTP provider: {}", 
                           init_result.error().message());
            return nullptr;
        }
        
        return provider;
        
    } catch (const std::exception& e) {
        Core::log_error("CtpFactory", "Exception creating CTP provider: {}", e.what());
        return nullptr;
    }
}

// WebData提供者工厂实现
std::unique_ptr<IMarketDataProvider> WebDataProviderFactory::create_provider(
    ProviderType type, 
    std::shared_ptr<ProviderConfig> config) {
    
    if (type != ProviderType::WebData_Stock) {
        return nullptr;
    }
    
    auto web_config = std::dynamic_pointer_cast<WebDataConfig>(config);
    if (!web_config) {
        Core::log_error("WebDataFactory", "Invalid config type for WebData provider");
        return nullptr;
    }
    
    return create_web_provider(web_config);
}

std::vector<ProviderType> WebDataProviderFactory::get_supported_types() const {
    return {ProviderType::WebData_Stock};
}

bool WebDataProviderFactory::supports_type(ProviderType type) const {
    return type == ProviderType::WebData_Stock;
}

std::unique_ptr<WebDataProvider> WebDataProviderFactory::create_web_provider(
    std::shared_ptr<WebDataConfig> config) {
    
    try {
        auto provider = std::make_unique<WebDataProvider>();
        
        auto init_result = provider->initialize(config);
        if (!init_result.is_success()) {
            Core::log_error("WebDataFactory", "Failed to initialize WebData provider: {}", 
                           init_result.error().message());
            return nullptr;
        }
        
        return provider;
        
    } catch (const std::exception& e) {
        Core::log_error("WebDataFactory", "Exception creating WebData provider: {}", e.what());
        return nullptr;
    }
}

// 统一提供者工厂
class UnifiedProviderFactory : public IProviderFactory {
public:
    UnifiedProviderFactory() {
        // 注册各种提供者工厂
        factories_[ProviderType::CTP_Futures] = std::make_unique<CtpProviderFactory>();
        factories_[ProviderType::WebData_Stock] = std::make_unique<WebDataProviderFactory>();
    }
    
    std::unique_ptr<IMarketDataProvider> create_provider(
        ProviderType type, 
        std::shared_ptr<ProviderConfig> config) override {
        
        auto it = factories_.find(type);
        if (it != factories_.end()) {
            return it->second->create_provider(type, config);
        }
        
        Core::log_error("UnifiedFactory", "Unsupported provider type: {}", 
                       static_cast<int>(type));
        return nullptr;
    }
    
    std::vector<ProviderType> get_supported_types() const override {
        std::vector<ProviderType> types;
        
        for (const auto& [type, factory] : factories_) {
            auto factory_types = factory->get_supported_types();
            types.insert(types.end(), factory_types.begin(), factory_types.end());
        }
        
        return types;
    }
    
    bool supports_type(ProviderType type) const override {
        auto it = factories_.find(type);
        return it != factories_.end() && it->second->supports_type(type);
    }

private:
    std::unordered_map<ProviderType, std::unique_ptr<IProviderFactory>> factories_;
};

// 全局工厂实例
std::unique_ptr<IProviderFactory> create_provider_factory() {
    return std::make_unique<UnifiedProviderFactory>();
}

// 便利函数
std::unique_ptr<IMarketDataProvider> create_ctp_provider(
    const std::string& front_address,
    const std::string& broker_id,
    const std::string& user_id,
    const std::string& password,
    const std::string& flow_path) {
    
    auto config = std::make_shared<CtpConfig>();
    config->front_address = front_address;
    config->broker_id = broker_id;
    config->user_id = user_id;
    config->password = password;
    config->flow_path = flow_path;
    
    CtpProviderFactory factory;
    return factory.create_provider(ProviderType::CTP_Futures, config);
}

std::unique_ptr<IMarketDataProvider> create_webdata_provider(
    WebDataSource data_source,
    std::chrono::milliseconds update_interval) {
    
    auto config = std::make_shared<WebDataConfig>();
    config->data_source = data_source;
    config->update_interval = update_interval;
    
    WebDataProviderFactory factory;
    return factory.create_provider(ProviderType::WebData_Stock, config);
}

std::unique_ptr<IMarketDataProvider> create_sina_provider(
    std::chrono::milliseconds update_interval) {
    
    return create_webdata_provider(WebDataSource::Sina, update_interval);
}

std::unique_ptr<IMarketDataProvider> create_tencent_provider(
    std::chrono::milliseconds update_interval) {
    
    return create_webdata_provider(WebDataSource::Tencent, update_interval);
}

// 配置构建器
class CtpConfigBuilder {
public:
    CtpConfigBuilder& front_address(const std::string& address) {
        config_->front_address = address;
        return *this;
    }
    
    CtpConfigBuilder& broker_id(const std::string& id) {
        config_->broker_id = id;
        return *this;
    }
    
    CtpConfigBuilder& user_credentials(const std::string& user_id, const std::string& password) {
        config_->user_id = user_id;
        config_->password = password;
        return *this;
    }
    
    CtpConfigBuilder& flow_path(const std::string& path) {
        config_->flow_path = path;
        return *this;
    }
    
    CtpConfigBuilder& use_udp(bool enable, std::size_t buffer_size = 64) {
        config_->using_udp = enable;
        config_->buffer_size = buffer_size;
        return *this;
    }
    
    CtpConfigBuilder& auto_reconnect(bool enable, std::chrono::seconds interval = std::chrono::seconds(30)) {
        config_->auto_reconnect = enable;
        config_->reconnect_interval = interval;
        return *this;
    }
    
    std::shared_ptr<CtpConfig> build() {
        return config_;
    }

private:
    std::shared_ptr<CtpConfig> config_ = std::make_shared<CtpConfig>();
};

class WebDataConfigBuilder {
public:
    WebDataConfigBuilder& data_source(WebDataSource source) {
        config_->data_source = source;
        return *this;
    }
    
    WebDataConfigBuilder& update_interval(std::chrono::milliseconds interval) {
        config_->update_interval = interval;
        return *this;
    }
    
    WebDataConfigBuilder& request_timeout(std::chrono::milliseconds timeout) {
        config_->request_timeout = timeout;
        return *this;
    }
    
    WebDataConfigBuilder& batch_size(std::size_t size) {
        config_->max_symbols_per_request = size;
        return *this;
    }
    
    WebDataConfigBuilder& cache_settings(bool enable, std::chrono::seconds ttl = std::chrono::seconds(30)) {
        config_->enable_cache = enable;
        config_->cache_ttl = ttl;
        return *this;
    }
    
    WebDataConfigBuilder& user_agent(const std::string& agent) {
        config_->user_agent = agent;
        return *this;
    }
    
    std::shared_ptr<WebDataConfig> build() {
        return config_;
    }

private:
    std::shared_ptr<WebDataConfig> config_ = std::make_shared<WebDataConfig>();
};

// 构建器工厂函数
CtpConfigBuilder ctp_config() {
    return CtpConfigBuilder();
}

WebDataConfigBuilder webdata_config() {
    return WebDataConfigBuilder();
}

} // namespace DataHub::Providers
