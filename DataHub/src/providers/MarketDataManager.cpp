#include "providers/MarketDataManager.h"
#include "core/Logging.h"
#include <algorithm>
#include <future>

namespace DataHub::Providers {

MarketDataManager::MarketDataManager(MarketDataManagerConfig config)
    : config_(std::move(config)), running_(false) {
}

MarketDataManager::~MarketDataManager() {
    stop();
}

Core::Result<void> MarketDataManager::start() {
    if (running_) {
        return Core::make_success();
    }
    
    try {
        // 启动所有提供者
        std::shared_lock lock(providers_mutex_);
        for (auto& provider : providers_) {
            auto result = provider->start();
            if (!result.is_success()) {
                Core::log_warn("MarketDataManager", "Failed to start provider {}: {}", 
                              provider->get_name(), result.error().message());
            }
        }
        lock.unlock();
        
        running_ = true;
        
        Core::log_info("MarketDataManager", "Market data manager started");
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::StartupFailed,
            "Failed to start market data manager: " + std::string(e.what()));
    }
}

Core::Result<void> MarketDataManager::stop() {
    if (!running_) {
        return Core::make_success();
    }
    
    try {
        // 停止所有提供者
        std::shared_lock lock(providers_mutex_);
        for (auto& provider : providers_) {
            auto result = provider->stop();
            if (!result.is_success()) {
                Core::log_warn("MarketDataManager", "Failed to stop provider {}: {}", 
                              provider->get_name(), result.error().message());
            }
        }
        lock.unlock();
        
        running_ = false;
        
        Core::log_info("MarketDataManager", "Market data manager stopped");
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ShutdownFailed,
            "Failed to stop market data manager: " + std::string(e.what()));
    }
}

bool MarketDataManager::is_running() const noexcept {
    return running_;
}

void MarketDataManager::subscribe(Services::EventType type, Services::EventHandler handler) {
    std::unique_lock lock(handlers_mutex_);
    event_handlers_[type] = std::move(handler);
}

void MarketDataManager::unsubscribe(Services::EventType type) {
    std::unique_lock lock(handlers_mutex_);
    event_handlers_.erase(type);
}

void MarketDataManager::publish_event(const Services::Event& event) {
    std::shared_lock lock(handlers_mutex_);
    auto it = event_handlers_.find(event.type);
    if (it != event_handlers_.end() && it->second) {
        try {
            it->second(event);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataManager", "Event handler exception: {}", e.what());
        }
    }
}

Core::Result<bool> MarketDataManager::health_check() {
    std::shared_lock lock(providers_mutex_);
    
    bool all_healthy = true;
    for (const auto& provider : providers_) {
        auto health_result = provider->health_check();
        if (!health_result.is_success() || !health_result.value()) {
            all_healthy = false;
            Core::log_warn("MarketDataManager", "Provider {} health check failed", 
                          provider->get_name());
        }
    }
    
    return Core::make_result<bool>(all_healthy);
}

Core::Result<void> MarketDataManager::add_provider(
    std::unique_ptr<IMarketDataProvider> provider, 
    ProviderPriority priority) {
    
    if (!provider) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Provider cannot be null");
    }
    
    try {
        std::unique_lock lock(providers_mutex_);
        
        // 检查是否已存在相同类型的提供者
        auto type = provider->get_type();
        if (provider_by_type_.find(type) != provider_by_type_.end()) {
            return Core::make_error<void>(Core::ErrorCode::AlreadyExists,
                "Provider of type already exists");
        }
        
        // 设置回调
        setup_provider_callbacks(provider.get());
        
        // 添加到映射表
        provider_by_type_[type] = provider.get();
        provider_by_name_[provider->get_name()] = provider.get();
        
        // 设置优先级
        if (priority.type == ProviderType::CTP_Futures) {  // 默认值检查
            priority.type = type;
        }
        provider_priorities_.push_back(priority);
        
        // 排序优先级列表
        std::sort(provider_priorities_.begin(), provider_priorities_.end(),
                 [](const ProviderPriority& a, const ProviderPriority& b) {
                     return a.priority < b.priority;
                 });
        
        // 添加到提供者列表
        providers_.push_back(std::move(provider));
        
        Core::log_info("MarketDataManager", "Added provider: {} (type: {})", 
                      providers_.back()->get_name(), static_cast<int>(type));
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::AddProviderFailed,
            "Failed to add provider: " + std::string(e.what()));
    }
}

Core::Result<void> MarketDataManager::remove_provider(ProviderType type) {
    std::unique_lock lock(providers_mutex_);
    
    auto it = provider_by_type_.find(type);
    if (it == provider_by_type_.end()) {
        return Core::make_error<void>(Core::ErrorCode::NotFound, "Provider type not found");
    }
    
    auto provider_ptr = it->second;
    
    // 从映射表中移除
    provider_by_type_.erase(it);
    provider_by_name_.erase(provider_ptr->get_name());
    
    // 从优先级列表中移除
    provider_priorities_.erase(
        std::remove_if(provider_priorities_.begin(), provider_priorities_.end(),
                      [type](const ProviderPriority& p) { return p.type == type; }),
        provider_priorities_.end());
    
    // 从提供者列表中移除
    providers_.erase(
        std::remove_if(providers_.begin(), providers_.end(),
                      [provider_ptr](const std::unique_ptr<IMarketDataProvider>& p) {
                          return p.get() == provider_ptr;
                      }),
        providers_.end());
    
    Core::log_info("MarketDataManager", "Removed provider type: {}", static_cast<int>(type));
    
    return Core::make_success();
}

Core::Result<void> MarketDataManager::remove_provider(const std::string& name) {
    std::unique_lock lock(providers_mutex_);
    
    auto it = provider_by_name_.find(name);
    if (it == provider_by_name_.end()) {
        return Core::make_error<void>(Core::ErrorCode::NotFound, "Provider name not found");
    }
    
    auto provider_ptr = it->second;
    auto type = provider_ptr->get_type();
    
    // 从映射表中移除
    provider_by_name_.erase(it);
    provider_by_type_.erase(type);
    
    // 从优先级列表中移除
    provider_priorities_.erase(
        std::remove_if(provider_priorities_.begin(), provider_priorities_.end(),
                      [type](const ProviderPriority& p) { return p.type == type; }),
        provider_priorities_.end());
    
    // 从提供者列表中移除
    providers_.erase(
        std::remove_if(providers_.begin(), providers_.end(),
                      [provider_ptr](const std::unique_ptr<IMarketDataProvider>& p) {
                          return p.get() == provider_ptr;
                      }),
        providers_.end());
    
    Core::log_info("MarketDataManager", "Removed provider: {}", name);
    
    return Core::make_success();
}

std::vector<IMarketDataProvider*> MarketDataManager::get_providers() const {
    std::shared_lock lock(providers_mutex_);
    
    std::vector<IMarketDataProvider*> result;
    result.reserve(providers_.size());
    
    for (const auto& provider : providers_) {
        result.push_back(provider.get());
    }
    
    return result;
}

IMarketDataProvider* MarketDataManager::get_provider(ProviderType type) const {
    std::shared_lock lock(providers_mutex_);
    
    auto it = provider_by_type_.find(type);
    return (it != provider_by_type_.end()) ? it->second : nullptr;
}

IMarketDataProvider* MarketDataManager::get_provider(const std::string& name) const {
    std::shared_lock lock(providers_mutex_);
    
    auto it = provider_by_name_.find(name);
    return (it != provider_by_name_.end()) ? it->second : nullptr;
}

Core::Result<void> MarketDataManager::subscribe_quote(const Core::Symbol& symbol) {
    auto provider = select_provider_for_symbol(symbol);
    if (!provider) {
        return Core::make_error<void>(Core::ErrorCode::NoProviderAvailable,
            "No provider available for symbol: " + symbol);
    }
    
    auto result = provider->subscribe_quote(symbol);
    if (result.is_success()) {
        std::unique_lock lock(subscriptions_mutex_);
        subscribed_symbols_.insert(symbol);
    }
    
    return result;
}

Core::Result<void> MarketDataManager::subscribe_quotes(const std::vector<Core::Symbol>& symbols) {
    for (const auto& symbol : symbols) {
        auto result = subscribe_quote(symbol);
        if (!result.is_success()) {
            Core::log_warn("MarketDataManager", "Failed to subscribe to {}: {}", 
                          symbol, result.error().message());
        }
    }
    
    return Core::make_success();
}

Core::Result<void> MarketDataManager::unsubscribe_quote(const Core::Symbol& symbol) {
    auto provider = select_provider_for_symbol(symbol);
    if (!provider) {
        return Core::make_error<void>(Core::ErrorCode::NoProviderAvailable,
            "No provider available for symbol: " + symbol);
    }
    
    auto result = provider->unsubscribe_quote(symbol);
    if (result.is_success()) {
        std::unique_lock lock(subscriptions_mutex_);
        subscribed_symbols_.erase(symbol);
    }
    
    return result;
}

Core::Result<void> MarketDataManager::unsubscribe_all() {
    std::shared_lock lock(providers_mutex_);
    
    for (auto& provider : providers_) {
        auto result = provider->unsubscribe_all();
        if (!result.is_success()) {
            Core::log_warn("MarketDataManager", "Failed to unsubscribe all from {}: {}", 
                          provider->get_name(), result.error().message());
        }
    }
    
    lock.unlock();
    
    std::unique_lock sub_lock(subscriptions_mutex_);
    subscribed_symbols_.clear();
    
    return Core::make_success();
}

// 数据查询方法
Core::Result<Core::QuoteData> MarketDataManager::get_latest_quote(const Core::Symbol& symbol) {
    // 首先检查缓存
    auto cached = get_from_cache(symbol);
    if (cached.has_value()) {
        std::unique_lock lock(stats_mutex_);
        stats_.cache_hits++;
        lock.unlock();
        return Core::make_result<Core::QuoteData>(cached.value());
    }

    std::unique_lock lock(stats_mutex_);
    stats_.cache_misses++;
    lock.unlock();

    // 从提供者获取数据
    auto provider = select_provider_for_symbol(symbol);
    if (!provider) {
        return Core::make_error<Core::QuoteData>(Core::ErrorCode::NoProviderAvailable,
            "No provider available for symbol: " + symbol);
    }

    auto result = provider->get_latest_quote(symbol);
    if (result.is_success()) {
        update_cache(symbol, result.value(), provider->get_type());
    }

    return result;
}

Core::Result<std::vector<Core::QuoteData>> MarketDataManager::get_quotes(
    const std::vector<Core::Symbol>& symbols) {

    std::vector<Core::QuoteData> all_quotes;

    // 按提供者分组符号
    std::unordered_map<IMarketDataProvider*, std::vector<Core::Symbol>> provider_symbols;

    for (const auto& symbol : symbols) {
        auto provider = select_provider_for_symbol(symbol);
        if (provider) {
            provider_symbols[provider].push_back(symbol);
        }
    }

    // 并行查询各个提供者
    std::vector<std::future<Core::Result<std::vector<Core::QuoteData>>>> futures;

    for (const auto& [provider, provider_symbol_list] : provider_symbols) {
        futures.push_back(std::async(std::launch::async, [provider, provider_symbol_list]() {
            return provider->get_quotes(provider_symbol_list);
        }));
    }

    // 收集结果
    for (auto& future : futures) {
        try {
            auto result = future.get();
            if (result.is_success()) {
                auto quotes = result.value();
                all_quotes.insert(all_quotes.end(), quotes.begin(), quotes.end());
            }
        } catch (const std::exception& e) {
            Core::log_warn("MarketDataManager", "Future exception: {}", e.what());
        }
    }

    return Core::make_result<std::vector<Core::QuoteData>>(std::move(all_quotes));
}

std::future<Core::Result<Core::QuoteData>> MarketDataManager::get_latest_quote_async(
    const Core::Symbol& symbol) {

    return std::async(std::launch::async, [this, symbol]() {
        return get_latest_quote(symbol);
    });
}

std::future<Core::Result<std::vector<Core::QuoteData>>> MarketDataManager::get_quotes_async(
    const std::vector<Core::Symbol>& symbols) {

    return std::async(std::launch::async, [this, symbols]() {
        return get_quotes(symbols);
    });
}

// 路由管理
Core::Result<void> MarketDataManager::set_symbol_route(const Core::Symbol& symbol, ProviderType provider) {
    std::unique_lock lock(routes_mutex_);

    DataRoute route;
    route.symbol = symbol;
    route.provider_type = provider;
    route.last_update = std::chrono::system_clock::now();

    auto provider_ptr = get_provider(provider);
    if (provider_ptr) {
        route.provider_name = provider_ptr->get_name();
        route.is_primary = true;  // 手动设置的路由视为主要路由
    }

    symbol_routes_[symbol] = route;

    return Core::make_success();
}

Core::Result<void> MarketDataManager::remove_symbol_route(const Core::Symbol& symbol) {
    std::unique_lock lock(routes_mutex_);
    symbol_routes_.erase(symbol);
    return Core::make_success();
}

std::optional<DataRoute> MarketDataManager::get_symbol_route(const Core::Symbol& symbol) const {
    std::shared_lock lock(routes_mutex_);

    auto it = symbol_routes_.find(symbol);
    if (it != symbol_routes_.end()) {
        return it->second;
    }

    return std::nullopt;
}

// 配置管理
Core::Result<void> MarketDataManager::set_routing_strategy(RoutingStrategy strategy) {
    config_.routing_strategy = strategy;
    return Core::make_success();
}

RoutingStrategy MarketDataManager::get_routing_strategy() const noexcept {
    return config_.routing_strategy;
}

Core::Result<void> MarketDataManager::update_provider_priority(ProviderType type, int priority) {
    std::unique_lock lock(providers_mutex_);

    auto it = std::find_if(provider_priorities_.begin(), provider_priorities_.end(),
                          [type](const ProviderPriority& p) { return p.type == type; });

    if (it != provider_priorities_.end()) {
        it->priority = priority;

        // 重新排序
        std::sort(provider_priorities_.begin(), provider_priorities_.end(),
                 [](const ProviderPriority& a, const ProviderPriority& b) {
                     return a.priority < b.priority;
                 });

        return Core::make_success();
    }

    return Core::make_error<void>(Core::ErrorCode::NotFound, "Provider type not found");
}

std::vector<ProviderPriority> MarketDataManager::get_provider_priorities() const {
    std::shared_lock lock(providers_mutex_);
    return provider_priorities_;
}

// 统计信息
MarketDataStats MarketDataManager::get_statistics() const {
    std::shared_lock lock(stats_mutex_);
    return stats_;
}

void MarketDataManager::reset_statistics() {
    std::unique_lock lock(stats_mutex_);
    stats_ = MarketDataStats{};
}

// 缓存管理
Core::Result<void> MarketDataManager::clear_cache() {
    std::unique_lock lock(cache_mutex_);
    quote_cache_.clear();
    return Core::make_success();
}

Core::Result<void> MarketDataManager::clear_cache(const Core::Symbol& symbol) {
    std::unique_lock lock(cache_mutex_);
    quote_cache_.erase(symbol);
    return Core::make_success();
}

std::size_t MarketDataManager::get_cache_size() const {
    std::shared_lock lock(cache_mutex_);
    return quote_cache_.size();
}

// 回调注册
void MarketDataManager::set_quote_callback(QuoteUpdateCallback callback) {
    quote_callback_ = std::move(callback);
}

void MarketDataManager::set_provider_status_callback(ProviderStatusCallback callback) {
    provider_status_callback_ = std::move(callback);
}

void MarketDataManager::set_error_callback(ErrorCallback callback) {
    error_callback_ = std::move(callback);
}

// 内部方法实现
IMarketDataProvider* MarketDataManager::select_provider_for_symbol(const Core::Symbol& symbol) const {
    // 首先检查是否有特定的路由设置
    auto route = get_symbol_route(symbol);
    if (route.has_value()) {
        auto provider = get_provider(route->provider_type);
        if (provider && provider->is_connected()) {
            return provider;
        }
    }

    // 根据路由策略选择提供者
    switch (config_.routing_strategy) {
        case RoutingStrategy::Primary:
            return get_providers_by_priority().empty() ? nullptr : get_providers_by_priority()[0];

        case RoutingStrategy::LoadBalance:
            return select_provider_round_robin();

        case RoutingStrategy::Failover:
            // 选择第一个可用的提供者
            for (auto provider : get_providers_by_priority()) {
                if (provider->is_connected()) {
                    return provider;
                }
            }
            return nullptr;

        case RoutingStrategy::Broadcast:
            // 广播模式返回第一个提供者（实际应该广播到所有提供者）
            return get_providers_by_priority().empty() ? nullptr : get_providers_by_priority()[0];

        default:
            return nullptr;
    }
}

std::vector<IMarketDataProvider*> MarketDataManager::get_providers_by_priority() const {
    std::shared_lock lock(providers_mutex_);

    std::vector<IMarketDataProvider*> sorted_providers;

    for (const auto& priority : provider_priorities_) {
        auto it = provider_by_type_.find(priority.type);
        if (it != provider_by_type_.end()) {
            sorted_providers.push_back(it->second);
        }
    }

    return sorted_providers;
}

bool MarketDataManager::is_cache_valid(const CachedQuote& cached) const {
    auto now = std::chrono::system_clock::now();
    auto age = std::chrono::duration_cast<std::chrono::minutes>(now - cached.timestamp);
    return age < config_.cache_ttl;
}

void MarketDataManager::update_cache(const Core::Symbol& symbol, const Core::QuoteData& quote,
                                   ProviderType provider) {
    std::unique_lock lock(cache_mutex_);

    CachedQuote cached;
    cached.quote = quote;
    cached.timestamp = std::chrono::system_clock::now();
    cached.source_provider = provider;

    quote_cache_[symbol] = cached;

    // 限制缓存大小
    if (quote_cache_.size() > config_.max_cache_size) {
        auto oldest = quote_cache_.begin();
        for (auto it = quote_cache_.begin(); it != quote_cache_.end(); ++it) {
            if (it->second.timestamp < oldest->second.timestamp) {
                oldest = it;
            }
        }
        quote_cache_.erase(oldest);
    }
}

std::optional<Core::QuoteData> MarketDataManager::get_from_cache(const Core::Symbol& symbol) const {
    std::shared_lock lock(cache_mutex_);

    auto it = quote_cache_.find(symbol);
    if (it != quote_cache_.end() && is_cache_valid(it->second)) {
        return it->second.quote;
    }

    return std::nullopt;
}

bool MarketDataManager::is_duplicate_quote(const Core::QuoteData& quote) const {
    if (!config_.enable_duplicate_detection) {
        return false;
    }

    std::shared_lock lock(duplicates_mutex_);

    auto it = last_quotes_.find(quote.symbol);
    if (it != last_quotes_.end()) {
        const auto& last = it->second;

        // 检查是否为重复数据
        if (last.symbol == quote.symbol &&
            std::abs(last.last_price - quote.last_price) < 0.0001 &&
            std::abs(std::chrono::duration_cast<std::chrono::milliseconds>(
                quote.timestamp - last.timestamp).count()) < config_.duplicate_window.count()) {
            return true;
        }
    }

    return false;
}

void MarketDataManager::update_duplicate_detection(const Core::QuoteData& quote) {
    if (!config_.enable_duplicate_detection) {
        return;
    }

    std::unique_lock lock(duplicates_mutex_);

    QuoteSignature signature;
    signature.symbol = quote.symbol;
    signature.last_price = quote.last_price;
    signature.timestamp = quote.timestamp;

    last_quotes_[quote.symbol] = signature;
}

bool MarketDataManager::validate_quote_data(const Core::QuoteData& quote) const {
    if (!config_.enable_data_validation) {
        return true;
    }

    // 基本验证
    if (quote.symbol.empty()) {
        return false;
    }

    if (quote.last_price <= 0) {
        return false;
    }

    // 价格合理性检查
    if (quote.high_price > 0 && quote.low_price > 0) {
        if (quote.high_price < quote.low_price) {
            return false;
        }

        if (quote.last_price > quote.high_price || quote.last_price < quote.low_price) {
            return false;
        }
    }

    return true;
}

void MarketDataManager::handle_provider_quote(const Core::QuoteData& quote, ProviderType provider_type) {
    // 数据验证
    if (!validate_quote_data(quote)) {
        Core::log_warn("MarketDataManager", "Invalid quote data for symbol: {}", quote.symbol);
        return;
    }

    // 重复检测
    if (is_duplicate_quote(quote)) {
        return;
    }

    // 更新缓存和重复检测
    update_cache(quote.symbol, quote, provider_type);
    update_duplicate_detection(quote);

    // 更新统计
    update_statistics(quote, provider_type);

    // 通知回调
    if (quote_callback_) {
        try {
            quote_callback_(quote);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataManager", "Quote callback exception: {}", e.what());
        }
    }
}

void MarketDataManager::handle_provider_error(const std::string& error, ProviderType provider_type) {
    update_error_statistics(provider_type);

    if (error_callback_) {
        try {
            error_callback_(error);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataManager", "Error callback exception: {}", e.what());
        }
    }
}

void MarketDataManager::handle_provider_status_change(ProviderType provider_type, ConnectionStatus status) {
    if (provider_status_callback_) {
        try {
            provider_status_callback_(provider_type, status);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataManager", "Status callback exception: {}", e.what());
        }
    }

    // 处理故障转移
    if (status == ConnectionStatus::Disconnected || status == ConnectionStatus::Error) {
        if (config_.enable_failover) {
            handle_provider_failure(provider_type);
        }
    }
}

void MarketDataManager::update_statistics(const Core::QuoteData& quote, ProviderType provider_type) {
    std::unique_lock lock(stats_mutex_);

    stats_.total_quotes++;
    stats_.provider_quotes[provider_type]++;

    // 简单的延迟计算（基于当前时间）
    auto now = std::chrono::system_clock::now();
    auto latency = std::chrono::duration_cast<std::chrono::milliseconds>(now - quote.timestamp);

    // 更新平均延迟
    auto current_avg = stats_.average_latency.count();
    auto new_avg = (current_avg * 9 + latency.count()) / 10;
    stats_.average_latency = std::chrono::milliseconds(new_avg);
}

void MarketDataManager::update_error_statistics(ProviderType provider_type) {
    std::unique_lock lock(stats_mutex_);

    stats_.total_errors++;
    stats_.provider_errors[provider_type]++;
}

void MarketDataManager::setup_provider_callbacks(IMarketDataProvider* provider) {
    if (!provider) return;

    auto provider_type = provider->get_type();

    // 设置行情回调
    provider->set_quote_callback([this, provider_type](const Core::QuoteData& quote) {
        handle_provider_quote(quote, provider_type);
    });

    // 设置错误回调
    provider->set_error_callback([this, provider_type](const std::string& error) {
        handle_provider_error(error, provider_type);
    });

    // 设置状态回调
    provider->set_status_callback([this, provider_type](ConnectionStatus status) {
        handle_provider_status_change(provider_type, status);
    });
}

void MarketDataManager::cleanup_provider_callbacks(IMarketDataProvider* provider) {
    if (!provider) return;

    provider->set_quote_callback(nullptr);
    provider->set_error_callback(nullptr);
    provider->set_status_callback(nullptr);
}

void MarketDataManager::handle_provider_failure(ProviderType failed_provider) {
    Core::log_warn("MarketDataManager", "Handling failure for provider type: {}",
                  static_cast<int>(failed_provider));

    // 查找备用提供者
    auto backup = find_backup_provider(failed_provider);
    if (backup && !backup->is_connected()) {
        auto connect_result = backup->connect();
        if (connect_result.is_success()) {
            Core::log_info("MarketDataManager", "Switched to backup provider: {}",
                          backup->get_name());
        }
    }
}

IMarketDataProvider* MarketDataManager::find_backup_provider(ProviderType failed_provider) const {
    std::shared_lock lock(providers_mutex_);

    for (const auto& priority : provider_priorities_) {
        if (priority.type != failed_provider && priority.is_backup) {
            auto it = provider_by_type_.find(priority.type);
            if (it != provider_by_type_.end()) {
                return it->second;
            }
        }
    }

    return nullptr;
}

IMarketDataProvider* MarketDataManager::select_provider_round_robin() const {
    auto providers = get_providers_by_priority();
    if (providers.empty()) {
        return nullptr;
    }

    auto index = round_robin_index_.fetch_add(1) % providers.size();
    return providers[index];
}

// MarketDataManagerFactory实现
std::unique_ptr<MarketDataManager> MarketDataManagerFactory::create(
    MarketDataManagerConfig config) {

    return std::make_unique<MarketDataManager>(std::move(config));
}

std::unique_ptr<MarketDataManager> MarketDataManagerFactory::create_with_providers(
    std::vector<std::unique_ptr<IMarketDataProvider>> providers,
    MarketDataManagerConfig config) {

    auto manager = std::make_unique<MarketDataManager>(std::move(config));

    for (auto& provider : providers) {
        ProviderPriority priority;
        priority.type = provider->get_type();
        priority.priority = static_cast<int>(priority.type);  // 默认优先级
        priority.is_primary = true;

        auto result = manager->add_provider(std::move(provider), priority);
        if (!result.is_success()) {
            Core::log_warn("MarketDataManagerFactory", "Failed to add provider: {}",
                          result.error().message());
        }
    }

    return manager;
}

} // namespace DataHub::Providers
