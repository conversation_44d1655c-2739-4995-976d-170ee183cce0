#include "providers/WebDataProvider.h"
#include "core/Logging.h"
#include <curl/curl.h>
#include <sstream>
#include <memory>

namespace DataHub::Providers {

// CURL HTTP客户端实现
class CurlHttpClient : public IHttpClient {
public:
    CurlHttpClient() {
        curl_global_init(CURL_GLOBAL_DEFAULT);
        curl_ = curl_easy_init();
        if (!curl_) {
            throw std::runtime_error("Failed to initialize CURL");
        }
        
        // 设置基本选项
        curl_easy_setopt(curl_, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl_, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl_, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl_, CURLOPT_SSL_VERIFYHOST, 0L);
        curl_easy_setopt(curl_, CURLOPT_USERAGENT, "DataHub/1.0");
    }
    
    ~CurlHttpClient() {
        if (curl_) {
            curl_easy_cleanup(curl_);
        }
        curl_global_cleanup();
    }
    
    Core::Result<std::string> get(const std::string& url, 
                                 std::chrono::milliseconds timeout) override {
        if (!curl_) {
            return Core::make_error<std::string>(Core::ErrorCode::InitializationFailed,
                "CURL not initialized");
        }
        
        try {
            std::string response_data;
            
            // 设置URL
            curl_easy_setopt(curl_, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl_, CURLOPT_HTTPGET, 1L);
            
            // 设置超时
            curl_easy_setopt(curl_, CURLOPT_TIMEOUT_MS, timeout.count());
            
            // 设置写入回调
            curl_easy_setopt(curl_, CURLOPT_WRITEDATA, &response_data);
            
            // 设置请求头
            struct curl_slist* headers = nullptr;
            for (const auto& [key, value] : headers_) {
                std::string header = key + ": " + value;
                headers = curl_slist_append(headers, header.c_str());
            }
            if (headers) {
                curl_easy_setopt(curl_, CURLOPT_HTTPHEADER, headers);
            }
            
            // 执行请求
            CURLcode res = curl_easy_perform(curl_);
            
            // 清理请求头
            if (headers) {
                curl_slist_free_all(headers);
            }
            
            if (res != CURLE_OK) {
                return Core::make_error<std::string>(Core::ErrorCode::NetworkError,
                    "CURL request failed: " + std::string(curl_easy_strerror(res)));
            }
            
            // 检查HTTP状态码
            long response_code;
            curl_easy_getinfo(curl_, CURLINFO_RESPONSE_CODE, &response_code);
            
            if (response_code >= 400) {
                return Core::make_error<std::string>(Core::ErrorCode::HttpError,
                    "HTTP error: " + std::to_string(response_code));
            }
            
            return Core::make_result<std::string>(std::move(response_data));
            
        } catch (const std::exception& e) {
            return Core::make_error<std::string>(Core::ErrorCode::NetworkError,
                "HTTP GET failed: " + std::string(e.what()));
        }
    }
    
    Core::Result<std::string> post(const std::string& url, 
                                  const std::string& data,
                                  std::chrono::milliseconds timeout) override {
        if (!curl_) {
            return Core::make_error<std::string>(Core::ErrorCode::InitializationFailed,
                "CURL not initialized");
        }
        
        try {
            std::string response_data;
            
            // 设置URL和POST数据
            curl_easy_setopt(curl_, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl_, CURLOPT_POSTFIELDS, data.c_str());
            curl_easy_setopt(curl_, CURLOPT_POSTFIELDSIZE, data.length());
            
            // 设置超时
            curl_easy_setopt(curl_, CURLOPT_TIMEOUT_MS, timeout.count());
            
            // 设置写入回调
            curl_easy_setopt(curl_, CURLOPT_WRITEDATA, &response_data);
            
            // 设置请求头
            struct curl_slist* headers = nullptr;
            for (const auto& [key, value] : headers_) {
                std::string header = key + ": " + value;
                headers = curl_slist_append(headers, header.c_str());
            }
            if (headers) {
                curl_easy_setopt(curl_, CURLOPT_HTTPHEADER, headers);
            }
            
            // 执行请求
            CURLcode res = curl_easy_perform(curl_);
            
            // 清理请求头
            if (headers) {
                curl_slist_free_all(headers);
            }
            
            if (res != CURLE_OK) {
                return Core::make_error<std::string>(Core::ErrorCode::NetworkError,
                    "CURL request failed: " + std::string(curl_easy_strerror(res)));
            }
            
            // 检查HTTP状态码
            long response_code;
            curl_easy_getinfo(curl_, CURLINFO_RESPONSE_CODE, &response_code);
            
            if (response_code >= 400) {
                return Core::make_error<std::string>(Core::ErrorCode::HttpError,
                    "HTTP error: " + std::to_string(response_code));
            }
            
            return Core::make_result<std::string>(std::move(response_data));
            
        } catch (const std::exception& e) {
            return Core::make_error<std::string>(Core::ErrorCode::NetworkError,
                "HTTP POST failed: " + std::string(e.what()));
        }
    }
    
    void set_headers(const std::unordered_map<std::string, std::string>& headers) override {
        headers_ = headers;
    }
    
    void set_user_agent(const std::string& user_agent) override {
        if (curl_) {
            curl_easy_setopt(curl_, CURLOPT_USERAGENT, user_agent.c_str());
        }
    }

private:
    CURL* curl_;
    std::unordered_map<std::string, std::string> headers_;
    
    // CURL写入回调函数
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
        size_t total_size = size * nmemb;
        userp->append(static_cast<char*>(contents), total_size);
        return total_size;
    }
};

// HTTP客户端工厂
std::unique_ptr<IHttpClient> create_http_client() {
    return std::make_unique<CurlHttpClient>();
}

} // namespace DataHub::Providers
