#include "providers/WebDataProvider.h"
#include "core/Logging.h"
#include <sstream>
#include <regex>
#include <iomanip>
#include <algorithm>

namespace DataHub::Providers {

Core::Result<std::vector<Core::QuoteData>> SinaDataParser::parse_quotes(
    const std::string& data, 
    const std::vector<Core::Symbol>& symbols) {
    
    try {
        std::vector<Core::QuoteData> quotes;
        std::istringstream stream(data);
        std::string line;
        
        while (std::getline(stream, line)) {
            if (line.empty()) continue;
            
            try {
                auto quote = parse_sina_quote_line(line);
                if (!quote.symbol.empty()) {
                    quotes.push_back(quote);
                }
            } catch (const std::exception& e) {
                Core::log_warn("SinaParser", "Failed to parse line: {}, error: {}", 
                              line, e.what());
                continue;
            }
        }
        
        return Core::make_result<std::vector<Core::QuoteData>>(std::move(quotes));
        
    } catch (const std::exception& e) {
        return Core::make_error<std::vector<Core::QuoteData>>(
            Core::ErrorCode::ParseError,
            "Failed to parse Sina data: " + std::string(e.what()));
    }
}

Core::QuoteData SinaDataParser::parse_sina_quote_line(const std::string& line) const {
    // 新浪数据格式：var hq_str_sh000001="上证指数,3094.668,3095.375,3086.423,3096.289,3086.423,0,0,22114607,24081764,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2023-12-08,15:00:00,00,";
    
    Core::QuoteData quote;
    
    // 提取股票代码
    std::regex code_regex(R"(var hq_str_([^=]+)=)");
    std::smatch code_match;
    if (!std::regex_search(line, code_match, code_regex)) {
        throw std::runtime_error("Cannot extract stock code from line");
    }
    
    std::string sina_code = code_match[1].str();
    quote.symbol = sina_code_to_symbol(sina_code);
    
    // 提取数据部分
    std::regex data_regex(R"("([^"]+)")");
    std::smatch data_match;
    if (!std::regex_search(line, data_match, data_regex)) {
        throw std::runtime_error("Cannot extract data from line");
    }
    
    std::string data_str = data_match[1].str();
    
    // 分割数据字段
    std::vector<std::string> fields;
    std::istringstream field_stream(data_str);
    std::string field;
    
    while (std::getline(field_stream, field, ',')) {
        fields.push_back(field);
    }
    
    if (fields.size() < 32) {
        throw std::runtime_error("Insufficient data fields: " + std::to_string(fields.size()));
    }
    
    try {
        // 解析各个字段
        quote.name = fields[0];                                    // 股票名称
        quote.open_price = std::stod(fields[1]);                  // 今日开盘价
        quote.pre_close = std::stod(fields[2]);                   // 昨日收盘价
        quote.last_price = std::stod(fields[3]);                  // 当前价格
        quote.high_price = std::stod(fields[4]);                  // 今日最高价
        quote.low_price = std::stod(fields[5]);                   // 今日最低价
        
        // 买卖盘信息
        quote.bid_prices[0] = std::stod(fields[6]);               // 竞买价
        quote.ask_prices[0] = std::stod(fields[7]);               // 竞卖价
        
        quote.volume = static_cast<Core::Volume>(std::stoll(fields[8]));     // 成交量
        quote.amount = std::stod(fields[9]);                      // 成交金额
        
        // 五档买盘
        for (int i = 0; i < 5 && (10 + i * 2) < fields.size(); ++i) {
            quote.bid_volumes[i] = static_cast<Core::Volume>(std::stoll(fields[10 + i * 2]));
            quote.bid_prices[i] = std::stod(fields[11 + i * 2]);
        }
        
        // 五档卖盘
        for (int i = 0; i < 5 && (20 + i * 2) < fields.size(); ++i) {
            quote.ask_volumes[i] = static_cast<Core::Volume>(std::stoll(fields[20 + i * 2]));
            quote.ask_prices[i] = std::stod(fields[21 + i * 2]);
        }
        
        // 时间信息
        if (fields.size() > 30) {
            std::string date_str = fields[30];  // 日期
            std::string time_str = fields[31];  // 时间
            
            // 解析时间戳（简化实现）
            quote.timestamp = std::chrono::system_clock::now();
        }
        
        // 计算涨跌幅
        if (quote.pre_close > 0) {
            quote.change = quote.last_price - quote.pre_close;
            quote.change_percent = (quote.change / quote.pre_close) * 100.0;
        }
        
    } catch (const std::exception& e) {
        throw std::runtime_error("Failed to parse numeric fields: " + std::string(e.what()));
    }
    
    return quote;
}

Core::Result<std::vector<Core::BarData>> SinaDataParser::parse_bars(
    const std::string& data,
    const Core::Symbol& symbol,
    Core::BarSize bar_size) {
    
    // 新浪历史数据解析（简化实现）
    try {
        std::vector<Core::BarData> bars;
        std::istringstream stream(data);
        std::string line;
        
        // 跳过标题行
        if (std::getline(stream, line)) {
            // 处理数据行
            while (std::getline(stream, line)) {
                if (line.empty()) continue;
                
                try {
                    Core::BarData bar;
                    bar.symbol = symbol;
                    bar.bar_size = bar_size;
                    
                    // 解析CSV格式的历史数据
                    std::vector<std::string> fields;
                    std::istringstream field_stream(line);
                    std::string field;
                    
                    while (std::getline(field_stream, field, ',')) {
                        fields.push_back(field);
                    }
                    
                    if (fields.size() >= 6) {
                        // 假设格式：日期,开盘,最高,最低,收盘,成交量
                        bar.timestamp = std::chrono::system_clock::now(); // 简化时间解析
                        bar.open_price = std::stod(fields[1]);
                        bar.high_price = std::stod(fields[2]);
                        bar.low_price = std::stod(fields[3]);
                        bar.close_price = std::stod(fields[4]);
                        bar.volume = static_cast<Core::Volume>(std::stoll(fields[5]));
                        
                        bars.push_back(bar);
                    }
                    
                } catch (const std::exception& e) {
                    Core::log_warn("SinaParser", "Failed to parse bar line: {}", line);
                    continue;
                }
            }
        }
        
        return Core::make_result<std::vector<Core::BarData>>(std::move(bars));
        
    } catch (const std::exception& e) {
        return Core::make_error<std::vector<Core::BarData>>(
            Core::ErrorCode::ParseError,
            "Failed to parse Sina bar data: " + std::string(e.what()));
    }
}

std::string SinaDataParser::build_quote_url(const std::vector<Core::Symbol>& symbols) {
    std::ostringstream url;
    url << "http://hq.sinajs.cn/list=";
    
    for (size_t i = 0; i < symbols.size(); ++i) {
        if (i > 0) url << ",";
        url << symbol_to_sina_code(symbols[i]);
    }
    
    return url.str();
}

std::string SinaDataParser::build_history_url(const Core::Symbol& symbol, 
                                             Core::BarSize bar_size,
                                             const Core::Timestamp& start_time,
                                             const Core::Timestamp& end_time) {
    
    std::string sina_code = symbol_to_sina_code(symbol);
    
    // 新浪历史数据URL（简化实现）
    std::ostringstream url;
    url << "http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData";
    url << "?symbol=" << sina_code;
    url << "&scale=" << static_cast<int>(bar_size);
    url << "&ma=no&datalen=1000";
    
    return url.str();
}

std::string SinaDataParser::symbol_to_sina_code(const Core::Symbol& symbol) const {
    // 符号转换：SH000001 -> sh000001, SZ000001 -> sz000001
    std::string code = symbol;
    std::transform(code.begin(), code.end(), code.begin(), ::tolower);
    
    // 处理不同的符号格式
    if (code.find('.') != std::string::npos) {
        // 格式：000001.SZ -> sz000001
        auto pos = code.find('.');
        std::string stock_code = code.substr(0, pos);
        std::string exchange = code.substr(pos + 1);
        
        if (exchange == "sz") {
            return "sz" + stock_code;
        } else if (exchange == "sh") {
            return "sh" + stock_code;
        }
    }
    
    // 默认处理
    if (code.length() == 6) {
        // 根据代码判断交易所
        if (code[0] == '0' || code[0] == '3') {
            return "sz" + code;  // 深交所
        } else if (code[0] == '6') {
            return "sh" + code;  // 上交所
        }
    }
    
    return code;
}

Core::Symbol SinaDataParser::sina_code_to_symbol(const std::string& sina_code) const {
    // 新浪代码转换：sh000001 -> 000001.SH, sz000001 -> 000001.SZ
    if (sina_code.length() >= 8) {
        std::string exchange = sina_code.substr(0, 2);
        std::string code = sina_code.substr(2);
        
        if (exchange == "sh") {
            return code + ".SH";
        } else if (exchange == "sz") {
            return code + ".SZ";
        }
    }
    
    return sina_code;
}

} // namespace DataHub::Providers
