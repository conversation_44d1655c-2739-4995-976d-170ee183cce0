#include "services/MarketDataService.h"
#include "config/MarketDataConfig.h"
#include "core/Logging.h"
#include <iostream>
#include <memory>
#include <csignal>
#include <thread>
#include <chrono>

using namespace DataHub;
using namespace DataHub::Services;
using namespace DataHub::Config;
using namespace DataHub::Core;

// 全局服务实例
std::unique_ptr<MarketDataService> g_market_data_service;
std::unique_ptr<ConfigManager> g_config_manager;

// 信号处理
volatile std::sig_atomic_t g_signal_received = 0;

void signal_handler(int signal) {
    g_signal_received = signal;
    std::cout << "\nReceived signal " << signal << ", shutting down gracefully..." << std::endl;
}

void setup_signal_handlers() {
    std::signal(SIGINT, signal_handler);   // Ctrl+C
    std::signal(SIGTERM, signal_handler);  // Termination request
#ifdef _WIN32
    std::signal(SIGBR<PERSON>K, signal_handler); // Ctrl+Break on Windows
#else
    std::signal(SIGH<PERSON>, signal_handler);   // Hangup signal on Unix
#endif
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]\n"
              << "Options:\n"
              << "  -c, --config <file>    Configuration file path (default: config/market_data_config.json)\n"
              << "  -h, --help            Show this help message\n"
              << "  -v, --version         Show version information\n"
              << "  --generate-config     Generate default configuration file\n"
              << "  --validate-config     Validate configuration file\n"
              << "  --test-mode           Run in test mode with mock data\n"
              << "  --log-level <level>   Set log level (debug, info, warn, error)\n"
              << std::endl;
}

void print_version() {
    std::cout << "DataHub_Modern Market Data Service v1.0.0\n"
              << "Built with C++20 support\n"
              << "Copyright (c) 2024 RoboQuant Project\n"
              << std::endl;
}

bool parse_arguments(int argc, char* argv[], std::string& config_file, bool& test_mode) {
    config_file = "config/market_data_config.json";
    test_mode = false;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            print_usage(argv[0]);
            return false;
        }
        else if (arg == "-v" || arg == "--version") {
            print_version();
            return false;
        }
        else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                config_file = argv[++i];
            } else {
                std::cerr << "Error: --config requires a file path" << std::endl;
                return false;
            }
        }
        else if (arg == "--generate-config") {
            auto result = ConfigManager::generate_default_config_file("market_data_config.json");
            if (result.is_success()) {
                std::cout << "Default configuration file generated: market_data_config.json" << std::endl;
            } else {
                std::cerr << "Failed to generate config file: " << result.error().message() << std::endl;
            }
            return false;
        }
        else if (arg == "--validate-config") {
            if (i + 1 < argc) {
                std::string validate_file = argv[++i];
                auto result = Utils::validate_config_file(validate_file);
                if (result.is_success()) {
                    std::cout << "Configuration file is valid: " << validate_file << std::endl;
                } else {
                    std::cerr << "Configuration validation failed: " << result.error().message() << std::endl;
                }
            } else {
                auto result = Utils::validate_config_file(config_file);
                if (result.is_success()) {
                    std::cout << "Configuration file is valid: " << config_file << std::endl;
                } else {
                    std::cerr << "Configuration validation failed: " << result.error().message() << std::endl;
                }
            }
            return false;
        }
        else if (arg == "--test-mode") {
            test_mode = true;
        }
        else if (arg == "--log-level") {
            if (i + 1 < argc) {
                std::string level = argv[++i];
                if (level == "debug") {
                    set_log_level(LogLevel::Debug);
                } else if (level == "info") {
                    set_log_level(LogLevel::Info);
                } else if (level == "warn") {
                    set_log_level(LogLevel::Warn);
                } else if (level == "error") {
                    set_log_level(LogLevel::Error);
                } else {
                    std::cerr << "Invalid log level: " << level << std::endl;
                    return false;
                }
            } else {
                std::cerr << "Error: --log-level requires a level (debug, info, warn, error)" << std::endl;
                return false;
            }
        }
        else {
            std::cerr << "Unknown argument: " << arg << std::endl;
            print_usage(argv[0]);
            return false;
        }
    }
    
    return true;
}

bool initialize_service(const std::string& config_file, bool test_mode) {
    try {
        // 初始化日志
        log_info("Main", "Initializing DataHub_Modern Market Data Service...");
        
        // 加载配置
        g_config_manager = std::make_unique<ConfigManager>(config_file);
        auto load_result = g_config_manager->load_config();
        if (!load_result.is_success()) {
            log_error("Main", "Failed to load configuration: {}", load_result.error().message());
            return false;
        }
        
        log_info("Main", "Configuration loaded successfully from: {}", config_file);
        
        // 验证配置
        auto validate_result = g_config_manager->validate_all_configs();
        if (!validate_result.is_success()) {
            log_error("Main", "Configuration validation failed: {}", validate_result.error().message());
            return false;
        }
        
        // 创建市场数据服务
        auto service_config = g_config_manager->get_service_config();
        if (test_mode) {
            log_info("Main", "Running in test mode");
            // 在测试模式下修改配置
            service_config.service_name += "_TestMode";
        }
        
        g_market_data_service = std::make_unique<MarketDataService>(service_config);
        
        // 添加提供者
        if (auto ctp_config = g_config_manager->get_ctp_config()) {
            if (!test_mode) {  // 测试模式下不启用CTP
                auto add_result = g_market_data_service->add_ctp_provider(ctp_config);
                if (add_result.is_success()) {
                    log_info("Main", "CTP provider added successfully");
                } else {
                    log_warn("Main", "Failed to add CTP provider: {}", add_result.error().message());
                }
            }
        }
        
        if (auto webdata_config = g_config_manager->get_webdata_config()) {
            auto add_result = g_market_data_service->add_webdata_provider(webdata_config);
            if (add_result.is_success()) {
                log_info("Main", "WebData provider added successfully");
            } else {
                log_warn("Main", "Failed to add WebData provider: {}", add_result.error().message());
            }
        }
        
        // 设置回调
        g_market_data_service->set_quote_callback([](const Core::QuoteData& quote) {
            log_debug("Quote", "Received quote: {} = {}", quote.symbol, quote.last_price);
        });
        
        g_market_data_service->set_provider_status_callback([](Providers::ProviderType type, Providers::ConnectionStatus status) {
            log_info("Provider", "Provider {} status changed to {}", 
                    static_cast<int>(type), static_cast<int>(status));
        });
        
        g_market_data_service->set_error_callback([](const std::string& error) {
            log_error("Service", "Service error: {}", error);
        });
        
        // 启动服务
        auto start_result = g_market_data_service->start();
        if (!start_result.is_success()) {
            log_error("Main", "Failed to start market data service: {}", start_result.error().message());
            return false;
        }
        
        log_info("Main", "Market data service started successfully");
        
        // 订阅默认符号（如果配置中有）
        // 这里可以从配置文件中读取默认订阅列表
        std::vector<Core::Symbol> default_symbols = {"000001.SZ", "600000.SH"};
        if (!test_mode && !default_symbols.empty()) {
            auto subscribe_result = g_market_data_service->subscribe_quotes(default_symbols);
            if (subscribe_result.is_success()) {
                log_info("Main", "Subscribed to {} default symbols", default_symbols.size());
            } else {
                log_warn("Main", "Failed to subscribe to default symbols: {}", 
                        subscribe_result.error().message());
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        log_error("Main", "Exception during initialization: {}", e.what());
        return false;
    }
}

void cleanup_service() {
    try {
        log_info("Main", "Cleaning up services...");
        
        if (g_market_data_service) {
            auto stop_result = g_market_data_service->stop();
            if (!stop_result.is_success()) {
                log_warn("Main", "Failed to stop market data service gracefully: {}", 
                        stop_result.error().message());
            }
            g_market_data_service.reset();
        }
        
        g_config_manager.reset();
        
        log_info("Main", "Cleanup completed");
        
    } catch (const std::exception& e) {
        log_error("Main", "Exception during cleanup: {}", e.what());
    }
}

void print_service_status() {
    if (!g_market_data_service) {
        std::cout << "Service not initialized" << std::endl;
        return;
    }
    
    std::cout << "\n=== Market Data Service Status ===" << std::endl;
    
    // 服务状态
    std::cout << "Service Running: " << (g_market_data_service->is_running() ? "Yes" : "No") << std::endl;
    
    // 提供者状态
    auto provider_status = g_market_data_service->get_provider_status();
    std::cout << "Providers: " << provider_status.providers.size() << std::endl;
    
    for (const auto& provider_info : provider_status.providers) {
        std::cout << "  - " << provider_info.name 
                  << " (Type: " << static_cast<int>(provider_info.type) << ")"
                  << " Status: " << static_cast<int>(provider_info.status)
                  << " Quotes: " << provider_info.quote_count
                  << " Errors: " << provider_info.error_count << std::endl;
    }
    
    // 统计信息
    auto stats = g_market_data_service->get_statistics();
    std::cout << "Statistics:" << std::endl;
    std::cout << "  Total Quotes: " << stats.total_quotes << std::endl;
    std::cout << "  Total Errors: " << stats.total_errors << std::endl;
    std::cout << "  Cache Hits: " << stats.cache_hits << std::endl;
    std::cout << "  Cache Misses: " << stats.cache_misses << std::endl;
    std::cout << "  Average Latency: " << stats.average_latency.count() << "ms" << std::endl;
    
    // 缓存信息
    std::cout << "Cache Size: " << g_market_data_service->get_cache_size() << std::endl;
    
    std::cout << "=================================" << std::endl;
}

int main(int argc, char* argv[]) {
    // 解析命令行参数
    std::string config_file;
    bool test_mode = false;
    
    if (!parse_arguments(argc, argv, config_file, test_mode)) {
        return 0;  // 帮助信息或错误，正常退出
    }
    
    // 设置信号处理
    setup_signal_handlers();
    
    // 初始化服务
    if (!initialize_service(config_file, test_mode)) {
        std::cerr << "Failed to initialize service" << std::endl;
        return 1;
    }
    
    std::cout << "DataHub_Modern Market Data Service is running..." << std::endl;
    std::cout << "Press Ctrl+C to stop, or type 'status' + Enter to show status" << std::endl;
    
    // 主循环
    while (g_signal_received == 0) {
        // 检查是否有用户输入
        std::string input;
        if (std::getline(std::cin, input)) {
            if (input == "status") {
                print_service_status();
            } else if (input == "quit" || input == "exit") {
                break;
            } else if (input == "help") {
                std::cout << "Available commands:" << std::endl;
                std::cout << "  status - Show service status" << std::endl;
                std::cout << "  quit/exit - Stop the service" << std::endl;
                std::cout << "  help - Show this help" << std::endl;
            } else if (!input.empty()) {
                std::cout << "Unknown command: " << input << std::endl;
                std::cout << "Type 'help' for available commands" << std::endl;
            }
        }
        
        // 短暂休眠避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 清理资源
    cleanup_service();
    
    std::cout << "DataHub_Modern Market Data Service stopped." << std::endl;
    return 0;
}
