#include "services/MarketDataService.h"
#include "providers/ProviderFactory.h"
#include "core/Logging.h"
#include <algorithm>
#include <future>

namespace DataHub::Services {

// 外部函数声明
namespace Providers {
    std::unique_ptr<IProviderFactory> create_provider_factory();
}

MarketDataService::MarketDataService(MarketDataServiceConfig config)
    : config_(std::move(config)), running_(false) {
    
    // 创建提供者工厂
    provider_factory_ = Providers::create_provider_factory();
    
    // 创建市场数据管理器
    market_data_manager_ = std::make_unique<Providers::MarketDataManager>(config_.manager_config);
}

MarketDataService::~MarketDataService() {
    stop();
}

Core::Result<void> MarketDataService::start() {
    if (running_) {
        return Core::make_success();
    }
    
    try {
        log_service_event("Starting MarketDataService");
        
        // 验证配置
        auto config_result = validate_config(config_);
        if (!config_result.is_success()) {
            return config_result;
        }
        
        // 初始化提供者
        auto provider_result = initialize_providers();
        if (!provider_result.is_success()) {
            return provider_result;
        }
        
        // 设置管理器回调
        setup_manager_callbacks();
        
        // 启动市场数据管理器
        auto manager_result = market_data_manager_->start();
        if (!manager_result.is_success()) {
            return manager_result;
        }
        
        // 初始化REST API（如果启用）
        if (config_.enable_rest_api) {
            auto api_result = initialize_rest_api();
            if (!api_result.is_success()) {
                Core::log_warn("MarketDataService", "Failed to initialize REST API: {}", 
                              api_result.error().message());
            }
        }
        
        running_ = true;
        
        log_service_event("MarketDataService started successfully");
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::StartupFailed,
            "Failed to start MarketDataService: " + std::string(e.what()));
    }
}

Core::Result<void> MarketDataService::stop() {
    if (!running_) {
        return Core::make_success();
    }
    
    try {
        log_service_event("Stopping MarketDataService");
        
        // 停止REST API
        if (rest_api_server_) {
            auto api_result = shutdown_rest_api();
            if (!api_result.is_success()) {
                Core::log_warn("MarketDataService", "Failed to shutdown REST API: {}", 
                              api_result.error().message());
            }
        }
        
        // 停止市场数据管理器
        if (market_data_manager_) {
            auto manager_result = market_data_manager_->stop();
            if (!manager_result.is_success()) {
                Core::log_warn("MarketDataService", "Failed to stop market data manager: {}", 
                              manager_result.error().message());
            }
        }
        
        // 清理回调
        cleanup_manager_callbacks();
        
        running_ = false;
        
        log_service_event("MarketDataService stopped");
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ShutdownFailed,
            "Failed to stop MarketDataService: " + std::string(e.what()));
    }
}

bool MarketDataService::is_running() const noexcept {
    return running_;
}

void MarketDataService::subscribe(EventType type, EventHandler handler) {
    std::unique_lock lock(handlers_mutex_);
    event_handlers_[type] = std::move(handler);
}

void MarketDataService::unsubscribe(EventType type) {
    std::unique_lock lock(handlers_mutex_);
    event_handlers_.erase(type);
}

void MarketDataService::publish_event(const Event& event) {
    std::shared_lock lock(handlers_mutex_);
    auto it = event_handlers_.find(event.type);
    if (it != event_handlers_.end() && it->second) {
        try {
            it->second(event);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataService", "Event handler exception: {}", e.what());
        }
    }
}

Core::Result<bool> MarketDataService::health_check() {
    if (!market_data_manager_) {
        return Core::make_result<bool>(false);
    }
    
    return market_data_manager_->health_check();
}

// 提供者管理
Core::Result<void> MarketDataService::add_ctp_provider(std::shared_ptr<Providers::CtpConfig> config) {
    if (!provider_factory_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Provider factory not initialized");
    }
    
    auto provider = provider_factory_->create_provider(Providers::ProviderType::CTP_Futures, config);
    if (!provider) {
        return Core::make_error<void>(Core::ErrorCode::CreationFailed, "Failed to create CTP provider");
    }
    
    Providers::ProviderPriority priority;
    priority.type = Providers::ProviderType::CTP_Futures;
    priority.priority = 1;  // 高优先级
    priority.is_primary = true;
    
    return market_data_manager_->add_provider(std::move(provider), priority);
}

Core::Result<void> MarketDataService::add_webdata_provider(std::shared_ptr<Providers::WebDataConfig> config) {
    if (!provider_factory_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Provider factory not initialized");
    }
    
    auto provider = provider_factory_->create_provider(Providers::ProviderType::WebData_Stock, config);
    if (!provider) {
        return Core::make_error<void>(Core::ErrorCode::CreationFailed, "Failed to create WebData provider");
    }
    
    Providers::ProviderPriority priority;
    priority.type = Providers::ProviderType::WebData_Stock;
    priority.priority = 2;  // 较低优先级
    priority.is_primary = false;
    priority.is_backup = true;
    
    return market_data_manager_->add_provider(std::move(provider), priority);
}

Core::Result<void> MarketDataService::remove_provider(Providers::ProviderType type) {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }
    
    return market_data_manager_->remove_provider(type);
}

Core::Result<void> MarketDataService::remove_provider(const std::string& name) {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }
    
    return market_data_manager_->remove_provider(name);
}

std::vector<Providers::IMarketDataProvider*> MarketDataService::get_providers() const {
    if (!market_data_manager_) {
        return {};
    }
    
    return market_data_manager_->get_providers();
}

Providers::IMarketDataProvider* MarketDataService::get_provider(Providers::ProviderType type) const {
    if (!market_data_manager_) {
        return nullptr;
    }
    
    return market_data_manager_->get_provider(type);
}

Providers::IMarketDataProvider* MarketDataService::get_provider(const std::string& name) const {
    if (!market_data_manager_) {
        return nullptr;
    }
    
    return market_data_manager_->get_provider(name);
}

// 订阅管理
Core::Result<void> MarketDataService::subscribe_quotes(const std::vector<Core::Symbol>& symbols) {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }
    
    auto result = market_data_manager_->subscribe_quotes(symbols);
    
    if (result.is_success()) {
        log_service_event("Subscribed to quotes", 
                         "Symbols: " + std::to_string(symbols.size()));
    }
    
    return result;
}

Core::Result<void> MarketDataService::unsubscribe_quotes(const std::vector<Core::Symbol>& symbols) {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }
    
    for (const auto& symbol : symbols) {
        auto result = market_data_manager_->unsubscribe_quote(symbol);
        if (!result.is_success()) {
            Core::log_warn("MarketDataService", "Failed to unsubscribe from {}: {}", 
                          symbol, result.error().message());
        }
    }
    
    return Core::make_success();
}

Core::Result<void> MarketDataService::unsubscribe_all() {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }
    
    auto result = market_data_manager_->unsubscribe_all();
    
    if (result.is_success()) {
        log_service_event("Unsubscribed from all quotes");
    }
    
    return result;
}

std::vector<Core::Symbol> MarketDataService::get_subscribed_symbols() const {
    // 这里需要从市场数据管理器获取订阅的符号列表
    // 简化实现，返回空列表
    return {};
}

// 数据查询
Core::Result<Core::QuoteData> MarketDataService::get_quote(const Core::Symbol& symbol) {
    if (!market_data_manager_) {
        return Core::make_error<Core::QuoteData>(Core::ErrorCode::NotInitialized, 
            "Market data manager not initialized");
    }
    
    return market_data_manager_->get_latest_quote(symbol);
}

Core::Result<std::vector<Core::QuoteData>> MarketDataService::get_quotes(
    const std::vector<Core::Symbol>& symbols) {
    
    if (!market_data_manager_) {
        return Core::make_error<std::vector<Core::QuoteData>>(Core::ErrorCode::NotInitialized, 
            "Market data manager not initialized");
    }
    
    return market_data_manager_->get_quotes(symbols);
}

std::future<Core::Result<Core::QuoteData>> MarketDataService::get_quote_async(const Core::Symbol& symbol) {
    if (!market_data_manager_) {
        std::promise<Core::Result<Core::QuoteData>> promise;
        promise.set_value(Core::make_error<Core::QuoteData>(Core::ErrorCode::NotInitialized, 
            "Market data manager not initialized"));
        return promise.get_future();
    }
    
    return market_data_manager_->get_latest_quote_async(symbol);
}

std::future<Core::Result<std::vector<Core::QuoteData>>> MarketDataService::get_quotes_async(
    const std::vector<Core::Symbol>& symbols) {
    
    if (!market_data_manager_) {
        std::promise<Core::Result<std::vector<Core::QuoteData>>> promise;
        promise.set_value(Core::make_error<std::vector<Core::QuoteData>>(Core::ErrorCode::NotInitialized, 
            "Market data manager not initialized"));
        return promise.get_future();
    }
    
    return market_data_manager_->get_quotes_async(symbols);
}

// 历史数据查询
Core::Result<std::vector<Core::BarData>> MarketDataService::get_history_bars(
    const Core::Symbol& symbol,
    Core::BarSize bar_size,
    const Core::Timestamp& start_time,
    const Core::Timestamp& end_time) {

    // 查找支持历史数据的提供者（通常是WebData提供者）
    auto webdata_provider = get_provider(Providers::ProviderType::WebData_Stock);
    if (!webdata_provider) {
        return Core::make_error<std::vector<Core::BarData>>(Core::ErrorCode::NoProviderAvailable,
            "No WebData provider available for history data");
    }

    // 尝试转换为WebDataProvider并调用历史数据方法
    auto* web_provider = dynamic_cast<Providers::WebDataProvider*>(webdata_provider);
    if (!web_provider) {
        return Core::make_error<std::vector<Core::BarData>>(Core::ErrorCode::TypeMismatch,
            "Provider is not a WebDataProvider");
    }

    return web_provider->get_history_bars(symbol, bar_size, start_time, end_time);
}

// 配置管理
Core::Result<void> MarketDataService::set_routing_strategy(Providers::RoutingStrategy strategy) {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }

    return market_data_manager_->set_routing_strategy(strategy);
}

Providers::RoutingStrategy MarketDataService::get_routing_strategy() const {
    if (!market_data_manager_) {
        return Providers::RoutingStrategy::Primary;
    }

    return market_data_manager_->get_routing_strategy();
}

Core::Result<void> MarketDataService::update_provider_priority(Providers::ProviderType type, int priority) {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }

    return market_data_manager_->update_provider_priority(type, priority);
}

std::vector<Providers::ProviderPriority> MarketDataService::get_provider_priorities() const {
    if (!market_data_manager_) {
        return {};
    }

    return market_data_manager_->get_provider_priorities();
}

// 统计和监控
Providers::MarketDataStats MarketDataService::get_statistics() const {
    if (!market_data_manager_) {
        return {};
    }

    return market_data_manager_->get_statistics();
}

void MarketDataService::reset_statistics() {
    if (market_data_manager_) {
        market_data_manager_->reset_statistics();
    }
}

ProviderStatusResponse MarketDataService::get_provider_status() const {
    ProviderStatusResponse response;

    if (!market_data_manager_) {
        return response;
    }

    auto providers = get_providers();
    response.providers.reserve(providers.size());

    for (auto* provider : providers) {
        ProviderStatusResponse::ProviderInfo info;
        info.name = provider->get_name();
        info.type = provider->get_type();
        info.is_connected = provider->is_connected();
        info.quote_count = provider->get_quote_count();
        info.error_count = provider->get_error_count();
        info.average_latency = provider->get_average_latency();

        auto connection_info = provider->get_connection_info();
        info.status = connection_info.status;

        response.providers.push_back(info);
    }

    response.overall_stats = get_statistics();

    return response;
}

// 缓存管理
Core::Result<void> MarketDataService::clear_cache() {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }

    return market_data_manager_->clear_cache();
}

Core::Result<void> MarketDataService::clear_cache(const Core::Symbol& symbol) {
    if (!market_data_manager_) {
        return Core::make_error<void>(Core::ErrorCode::NotInitialized, "Market data manager not initialized");
    }

    return market_data_manager_->clear_cache(symbol);
}

std::size_t MarketDataService::get_cache_size() const {
    if (!market_data_manager_) {
        return 0;
    }

    return market_data_manager_->get_cache_size();
}

// 回调注册
void MarketDataService::set_quote_callback(QuoteUpdateCallback callback) {
    quote_callback_ = std::move(callback);
}

void MarketDataService::set_provider_status_callback(ProviderStatusCallback callback) {
    provider_status_callback_ = std::move(callback);
}

void MarketDataService::set_error_callback(ServiceErrorCallback callback) {
    error_callback_ = std::move(callback);
}

// REST API接口
Core::Result<QuoteResponse> MarketDataService::handle_quote_request(const QuoteRequest& request) {
    QuoteResponse response;
    response.timestamp = std::chrono::system_clock::now();

    try {
        auto result = get_quotes(request.symbols);
        if (result.is_success()) {
            response.quotes = result.value();
            response.success = true;
        } else {
            response.error_message = result.error().message();
            response.success = false;
        }

        return Core::make_result<QuoteResponse>(std::move(response));

    } catch (const std::exception& e) {
        response.error_message = "Exception: " + std::string(e.what());
        response.success = false;
        return Core::make_result<QuoteResponse>(std::move(response));
    }
}

Core::Result<SubscriptionResponse> MarketDataService::handle_subscription_request(const SubscriptionRequest& request) {
    SubscriptionResponse response;

    try {
        for (const auto& symbol : request.symbols) {
            Core::Result<void> result;

            if (request.subscribe) {
                result = market_data_manager_->subscribe_quote(symbol);
            } else {
                result = market_data_manager_->unsubscribe_quote(symbol);
            }

            if (result.is_success()) {
                response.successful_symbols.push_back(symbol);
            } else {
                response.failed_symbols.emplace_back(symbol, result.error().message());
            }
        }

        response.success = !response.successful_symbols.empty();

        return Core::make_result<SubscriptionResponse>(std::move(response));

    } catch (const std::exception& e) {
        response.success = false;
        for (const auto& symbol : request.symbols) {
            response.failed_symbols.emplace_back(symbol, "Exception: " + std::string(e.what()));
        }
        return Core::make_result<SubscriptionResponse>(std::move(response));
    }
}

Core::Result<ProviderStatusResponse> MarketDataService::handle_status_request() {
    try {
        auto response = get_provider_status();
        return Core::make_result<ProviderStatusResponse>(std::move(response));

    } catch (const std::exception& e) {
        return Core::make_error<ProviderStatusResponse>(Core::ErrorCode::InternalError,
            "Failed to get provider status: " + std::string(e.what()));
    }
}

// 配置热重载
Core::Result<void> MarketDataService::reload_config(const MarketDataServiceConfig& new_config) {
    try {
        // 验证新配置
        auto validation_result = validate_config(new_config);
        if (!validation_result.is_success()) {
            return validation_result;
        }

        // 保存旧配置以便回滚
        auto old_config = config_;
        config_ = new_config;

        // 重新初始化组件（简化实现）
        log_service_event("Config reloaded");

        return Core::make_success();

    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ConfigurationError,
            "Failed to reload config: " + std::string(e.what()));
    }
}

MarketDataServiceConfig MarketDataService::get_config() const {
    return config_;
}

// 内部方法实现
Core::Result<void> MarketDataService::initialize_providers() {
    try {
        for (const auto& provider_config : config_.provider_configs) {
            if (!provider_config) continue;

            auto provider = provider_factory_->create_provider(provider_config->type, provider_config);
            if (!provider) {
                Core::log_warn("MarketDataService", "Failed to create provider of type: {}",
                              static_cast<int>(provider_config->type));
                continue;
            }

            Providers::ProviderPriority priority;
            priority.type = provider_config->type;
            priority.priority = static_cast<int>(provider_config->type);  // 默认优先级
            priority.is_primary = (provider_config->type == Providers::ProviderType::CTP_Futures);
            priority.is_backup = (provider_config->type == Providers::ProviderType::WebData_Stock);

            auto add_result = market_data_manager_->add_provider(std::move(provider), priority);
            if (!add_result.is_success()) {
                Core::log_warn("MarketDataService", "Failed to add provider: {}",
                              add_result.error().message());
            }
        }

        return Core::make_success();

    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::InitializationFailed,
            "Failed to initialize providers: " + std::string(e.what()));
    }
}

Core::Result<void> MarketDataService::initialize_rest_api() {
    // REST API实现留待后续扩展
    Core::log_info("MarketDataService", "REST API initialization skipped (not implemented)");
    return Core::make_success();
}

Core::Result<void> MarketDataService::shutdown_rest_api() {
    // REST API关闭实现留待后续扩展
    Core::log_info("MarketDataService", "REST API shutdown skipped (not implemented)");
    return Core::make_success();
}

void MarketDataService::setup_manager_callbacks() {
    if (!market_data_manager_) return;

    // 设置行情更新回调
    market_data_manager_->set_quote_callback([this](const Core::QuoteData& quote) {
        handle_quote_update(quote);
    });

    // 设置提供者状态回调
    market_data_manager_->set_provider_status_callback(
        [this](Providers::ProviderType type, Providers::ConnectionStatus status) {
            handle_provider_status_change(type, status);
        });

    // 设置错误回调
    market_data_manager_->set_error_callback([this](const std::string& error) {
        handle_service_error(error);
    });
}

void MarketDataService::cleanup_manager_callbacks() {
    if (!market_data_manager_) return;

    market_data_manager_->set_quote_callback(nullptr);
    market_data_manager_->set_provider_status_callback(nullptr);
    market_data_manager_->set_error_callback(nullptr);
}

void MarketDataService::handle_quote_update(const Core::QuoteData& quote) {
    // 发布行情事件
    publish_quote_event(quote);

    // 调用用户回调
    if (quote_callback_) {
        try {
            quote_callback_(quote);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataService", "Quote callback exception: {}", e.what());
        }
    }

    // 收集指标
    if (config_.enable_metrics) {
        collect_metrics();
    }
}

void MarketDataService::handle_provider_status_change(Providers::ProviderType type,
                                                     Providers::ConnectionStatus status) {
    // 发布状态变化事件
    publish_provider_status_event(type, status);

    // 调用用户回调
    if (provider_status_callback_) {
        try {
            provider_status_callback_(type, status);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataService", "Provider status callback exception: {}", e.what());
        }
    }

    // 记录状态变化
    log_service_event("Provider status changed",
                     "Type: " + std::to_string(static_cast<int>(type)) +
                     ", Status: " + std::to_string(static_cast<int>(status)));
}

void MarketDataService::handle_service_error(const std::string& error) {
    // 发布错误事件
    publish_error_event(error);

    // 调用用户回调
    if (error_callback_) {
        try {
            error_callback_(error);
        } catch (const std::exception& e) {
            Core::log_error("MarketDataService", "Error callback exception: {}", e.what());
        }
    }

    // 记录错误
    log_service_event("Service error", error);
}

void MarketDataService::publish_quote_event(const Core::QuoteData& quote) {
    Event event;
    event.type = EventType::QuoteUpdate;
    event.timestamp = std::chrono::system_clock::now();
    event.data = quote.symbol;  // 简化实现，只传递符号

    publish_event(event);
}

void MarketDataService::publish_provider_status_event(Providers::ProviderType type,
                                                     Providers::ConnectionStatus status) {
    Event event;
    event.type = EventType::ProviderStatusChange;
    event.timestamp = std::chrono::system_clock::now();
    event.data = std::to_string(static_cast<int>(type)) + ":" + std::to_string(static_cast<int>(status));

    publish_event(event);
}

void MarketDataService::publish_error_event(const std::string& error) {
    Event event;
    event.type = EventType::Error;
    event.timestamp = std::chrono::system_clock::now();
    event.data = error;

    publish_event(event);
}

Core::Result<void> MarketDataService::validate_config(const MarketDataServiceConfig& config) const {
    // 基本配置验证
    if (config.service_name.empty()) {
        return Core::make_error<void>(Core::ErrorCode::ConfigurationError, "Service name cannot be empty");
    }

    if (config.enable_rest_api) {
        if (config.rest_api_port == 0) {
            return Core::make_error<void>(Core::ErrorCode::ConfigurationError, "Invalid REST API port");
        }
        if (config.rest_api_host.empty()) {
            return Core::make_error<void>(Core::ErrorCode::ConfigurationError, "REST API host cannot be empty");
        }
    }

    // 提供者配置验证
    if (config.provider_configs.empty()) {
        Core::log_warn("MarketDataService", "No provider configs specified");
    }

    return Core::make_success();
}

void MarketDataService::collect_metrics() {
    // 指标收集实现留待后续扩展
    // 可以收集如：行情更新频率、延迟、错误率等指标
}

void MarketDataService::log_service_event(const std::string& event, const std::string& details) const {
    if (config_.enable_logging) {
        if (details.empty()) {
            Core::log_info("MarketDataService", "{}", event);
        } else {
            Core::log_info("MarketDataService", "{}: {}", event, details);
        }
    }
}

// MarketDataServiceFactory实现
std::unique_ptr<MarketDataService> MarketDataServiceFactory::create(MarketDataServiceConfig config) {
    return std::make_unique<MarketDataService>(std::move(config));
}

std::unique_ptr<MarketDataService> MarketDataServiceFactory::create_with_ctp(
    std::shared_ptr<Providers::CtpConfig> ctp_config,
    MarketDataServiceConfig service_config) {

    service_config.provider_configs.push_back(ctp_config);
    return create(std::move(service_config));
}

std::unique_ptr<MarketDataService> MarketDataServiceFactory::create_with_webdata(
    std::shared_ptr<Providers::WebDataConfig> webdata_config,
    MarketDataServiceConfig service_config) {

    service_config.provider_configs.push_back(webdata_config);
    return create(std::move(service_config));
}

std::unique_ptr<MarketDataService> MarketDataServiceFactory::create_with_both(
    std::shared_ptr<Providers::CtpConfig> ctp_config,
    std::shared_ptr<Providers::WebDataConfig> webdata_config,
    MarketDataServiceConfig service_config) {

    service_config.provider_configs.push_back(ctp_config);
    service_config.provider_configs.push_back(webdata_config);
    return create(std::move(service_config));
}

// 便利函数
std::unique_ptr<MarketDataService> create_market_data_service(
    const std::string& ctp_front_address,
    const std::string& ctp_broker_id,
    const std::string& ctp_user_id,
    const std::string& ctp_password,
    Providers::WebDataSource web_source) {

    MarketDataServiceConfig service_config;

    // 创建CTP配置（如果提供了参数）
    if (!ctp_front_address.empty() && !ctp_broker_id.empty()) {
        auto ctp_config = std::make_shared<Providers::CtpConfig>();
        ctp_config->front_address = ctp_front_address;
        ctp_config->broker_id = ctp_broker_id;
        ctp_config->user_id = ctp_user_id;
        ctp_config->password = ctp_password;
        ctp_config->flow_path = "./ctp_flow/";

        service_config.provider_configs.push_back(ctp_config);
    }

    // 创建WebData配置
    auto webdata_config = std::make_shared<Providers::WebDataConfig>();
    webdata_config->data_source = web_source;
    webdata_config->update_interval = std::chrono::milliseconds(3000);
    webdata_config->enable_cache = true;

    service_config.provider_configs.push_back(webdata_config);

    return MarketDataServiceFactory::create(std::move(service_config));
}

} // namespace DataHub::Services
