#include "config/MarketDataConfig.h"
#include "core/Logging.h"
#include <fstream>
#include <filesystem>
#include <thread>

namespace DataHub::Config {

// ConfigLoader实现
Core::Result<nlohmann::json> ConfigLoader::load_from_file(const std::string& file_path) {
    try {
        if (!std::filesystem::exists(file_path)) {
            return Core::make_error<nlohmann::json>(Core::ErrorCode::FileNotFound,
                "Config file not found: " + file_path);
        }
        
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return Core::make_error<nlohmann::json>(Core::ErrorCode::FileOpenFailed,
                "Failed to open config file: " + file_path);
        }
        
        nlohmann::json config;
        file >> config;
        
        auto validation_result = validate_config(config);
        if (!validation_result.is_success()) {
            return Core::make_error<nlohmann::json>(validation_result.error().code(),
                "Config validation failed: " + validation_result.error().message());
        }
        
        return Core::make_result<nlohmann::json>(std::move(config));
        
    } catch (const nlohmann::json::exception& e) {
        return Core::make_error<nlohmann::json>(Core::ErrorCode::ParseError,
            "JSON parse error: " + std::string(e.what()));
    } catch (const std::exception& e) {
        return Core::make_error<nlohmann::json>(Core::ErrorCode::InternalError,
            "Failed to load config: " + std::string(e.what()));
    }
}

Core::Result<nlohmann::json> ConfigLoader::load_from_string(const std::string& json_str) {
    try {
        auto config = nlohmann::json::parse(json_str);
        
        auto validation_result = validate_config(config);
        if (!validation_result.is_success()) {
            return Core::make_error<nlohmann::json>(validation_result.error().code(),
                "Config validation failed: " + validation_result.error().message());
        }
        
        return Core::make_result<nlohmann::json>(std::move(config));
        
    } catch (const nlohmann::json::exception& e) {
        return Core::make_error<nlohmann::json>(Core::ErrorCode::ParseError,
            "JSON parse error: " + std::string(e.what()));
    }
}

Core::Result<void> ConfigLoader::save_to_file(const nlohmann::json& config, const std::string& file_path) {
    try {
        // 创建目录（如果不存在）
        auto parent_path = std::filesystem::path(file_path).parent_path();
        if (!parent_path.empty() && !std::filesystem::exists(parent_path)) {
            std::filesystem::create_directories(parent_path);
        }
        
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return Core::make_error<void>(Core::ErrorCode::FileOpenFailed,
                "Failed to open config file for writing: " + file_path);
        }
        
        file << config.dump(4);  // 4空格缩进
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::FileWriteFailed,
            "Failed to save config: " + std::string(e.what()));
    }
}

Core::Result<void> ConfigLoader::validate_config(const nlohmann::json& config) {
    try {
        // 基本结构验证
        if (!config.is_object()) {
            return Core::make_error<void>(Core::ErrorCode::ConfigurationError,
                "Config must be a JSON object");
        }
        
        // 检查必需的顶级字段
        if (!config.contains("service")) {
            return Core::make_error<void>(Core::ErrorCode::ConfigurationError,
                "Missing 'service' section in config");
        }
        
        // 验证服务配置
        const auto& service_config = config["service"];
        if (!service_config.contains("name")) {
            return Core::make_error<void>(Core::ErrorCode::ConfigurationError,
                "Missing 'name' in service config");
        }
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ConfigurationError,
            "Config validation error: " + std::string(e.what()));
    }
}

// CtpConfigBuilder实现
Core::Result<std::shared_ptr<Providers::CtpConfig>> CtpConfigBuilder::from_json(const nlohmann::json& json) {
    try {
        auto config = std::make_shared<Providers::CtpConfig>();
        
        // 必需字段
        if (json.contains("front_address")) {
            config->front_address = json["front_address"].get<std::string>();
        }
        if (json.contains("broker_id")) {
            config->broker_id = json["broker_id"].get<std::string>();
        }
        if (json.contains("user_id")) {
            config->user_id = json["user_id"].get<std::string>();
        }
        if (json.contains("password")) {
            config->password = json["password"].get<std::string>();
        }
        if (json.contains("flow_path")) {
            config->flow_path = json["flow_path"].get<std::string>();
        }
        
        // 可选字段
        if (json.contains("using_udp")) {
            config->using_udp = json["using_udp"].get<bool>();
        }
        if (json.contains("multicast")) {
            config->multicast = json["multicast"].get<bool>();
        }
        if (json.contains("buffer_size")) {
            config->buffer_size = json["buffer_size"].get<std::size_t>();
        }
        if (json.contains("auto_reconnect")) {
            config->auto_reconnect = json["auto_reconnect"].get<bool>();
        }
        if (json.contains("reconnect_interval")) {
            auto interval = json["reconnect_interval"].get<int>();
            config->reconnect_interval = std::chrono::seconds(interval);
        }
        
        return Core::make_result<std::shared_ptr<Providers::CtpConfig>>(config);
        
    } catch (const std::exception& e) {
        return Core::make_error<std::shared_ptr<Providers::CtpConfig>>(
            Core::ErrorCode::ConfigurationError,
            "Failed to parse CTP config: " + std::string(e.what()));
    }
}

nlohmann::json CtpConfigBuilder::to_json(const Providers::CtpConfig& config) {
    nlohmann::json json;
    
    json["front_address"] = config.front_address;
    json["broker_id"] = config.broker_id;
    json["user_id"] = config.user_id;
    json["password"] = config.password;
    json["flow_path"] = config.flow_path;
    json["using_udp"] = config.using_udp;
    json["multicast"] = config.multicast;
    json["buffer_size"] = config.buffer_size;
    json["auto_reconnect"] = config.auto_reconnect;
    json["reconnect_interval"] = config.reconnect_interval.count();
    
    return json;
}

nlohmann::json CtpConfigBuilder::get_default_template() {
    nlohmann::json template_json;
    
    template_json["front_address"] = "tcp://***************:10131";
    template_json["broker_id"] = "9999";
    template_json["user_id"] = "your_user_id";
    template_json["password"] = "your_password";
    template_json["flow_path"] = "./ctp_flow/";
    template_json["using_udp"] = false;
    template_json["multicast"] = false;
    template_json["buffer_size"] = 64;
    template_json["auto_reconnect"] = true;
    template_json["reconnect_interval"] = 30;
    
    return template_json;
}

// WebDataConfigBuilder实现
Core::Result<std::shared_ptr<Providers::WebDataConfig>> WebDataConfigBuilder::from_json(const nlohmann::json& json) {
    try {
        auto config = std::make_shared<Providers::WebDataConfig>();
        
        // 数据源
        if (json.contains("data_source")) {
            std::string source_str = json["data_source"].get<std::string>();
            if (source_str == "sina") {
                config->data_source = Providers::WebDataSource::Sina;
            } else if (source_str == "tencent") {
                config->data_source = Providers::WebDataSource::Tencent;
            } else if (source_str == "netease") {
                config->data_source = Providers::WebDataSource::NetEase;
            } else {
                config->data_source = Providers::WebDataSource::Sina;  // 默认
            }
        }
        
        // 更新间隔
        if (json.contains("update_interval")) {
            auto interval = json["update_interval"].get<int>();
            config->update_interval = std::chrono::milliseconds(interval);
        }
        
        // 请求超时
        if (json.contains("request_timeout")) {
            auto timeout = json["request_timeout"].get<int>();
            config->request_timeout = std::chrono::milliseconds(timeout);
        }
        
        // 批处理大小
        if (json.contains("max_symbols_per_request")) {
            config->max_symbols_per_request = json["max_symbols_per_request"].get<std::size_t>();
        }
        
        // 并发请求数
        if (json.contains("max_concurrent_requests")) {
            config->max_concurrent_requests = json["max_concurrent_requests"].get<std::size_t>();
        }
        
        // 缓存设置
        if (json.contains("enable_cache")) {
            config->enable_cache = json["enable_cache"].get<bool>();
        }
        if (json.contains("cache_ttl")) {
            auto ttl = json["cache_ttl"].get<int>();
            config->cache_ttl = std::chrono::seconds(ttl);
        }
        
        // HTTP设置
        if (json.contains("user_agent")) {
            config->user_agent = json["user_agent"].get<std::string>();
        }
        
        return Core::make_result<std::shared_ptr<Providers::WebDataConfig>>(config);
        
    } catch (const std::exception& e) {
        return Core::make_error<std::shared_ptr<Providers::WebDataConfig>>(
            Core::ErrorCode::ConfigurationError,
            "Failed to parse WebData config: " + std::string(e.what()));
    }
}

nlohmann::json WebDataConfigBuilder::to_json(const Providers::WebDataConfig& config) {
    nlohmann::json json;
    
    // 数据源转换
    std::string source_str;
    switch (config.data_source) {
        case Providers::WebDataSource::Sina: source_str = "sina"; break;
        case Providers::WebDataSource::Tencent: source_str = "tencent"; break;
        case Providers::WebDataSource::NetEase: source_str = "netease"; break;
        default: source_str = "sina"; break;
    }
    
    json["data_source"] = source_str;
    json["update_interval"] = config.update_interval.count();
    json["request_timeout"] = config.request_timeout.count();
    json["max_symbols_per_request"] = config.max_symbols_per_request;
    json["max_concurrent_requests"] = config.max_concurrent_requests;
    json["enable_cache"] = config.enable_cache;
    json["cache_ttl"] = config.cache_ttl.count();
    json["user_agent"] = config.user_agent;
    
    return json;
}

nlohmann::json WebDataConfigBuilder::get_default_template() {
    nlohmann::json template_json;
    
    template_json["data_source"] = "sina";
    template_json["update_interval"] = 3000;
    template_json["request_timeout"] = 5000;
    template_json["max_symbols_per_request"] = 100;
    template_json["max_concurrent_requests"] = 5;
    template_json["enable_cache"] = true;
    template_json["cache_ttl"] = 30;
    template_json["user_agent"] = "DataHub/1.0";
    
    return template_json;
}

} // namespace DataHub::Config
