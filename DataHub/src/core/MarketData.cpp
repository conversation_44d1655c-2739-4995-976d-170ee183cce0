#include "core/MarketData.h"
#include <algorithm>
#include <numeric>
#include <cmath>

namespace DataHub::Core {

// QuoteData 实现
std::string QuoteData::to_string() const {
    return fmt::format(
        "QuoteData{{symbol={}, timestamp={}, last_price={:.4f}, "
        "volume={}, amount={:.2f}, bid={:.4f}, ask={:.4f}}}",
        symbol, 
        format_timestamp(timestamp, "%Y-%m-%d %H:%M:%S"),
        last_price, volume, amount,
        bid_prices[0], ask_prices[0]
    );
}

nlohmann::json QuoteData::to_json() const {
    nlohmann::json j;
    j["symbol"] = symbol;
    j["timestamp"] = timestamp.time_since_epoch().count();
    j["last_price"] = last_price;
    j["open_price"] = open_price;
    j["high_price"] = high_price;
    j["low_price"] = low_price;
    j["close_price"] = close_price;
    j["pre_close"] = pre_close;
    j["volume"] = volume;
    j["amount"] = amount;
    
    // 买卖盘数据
    j["bid_prices"] = bid_prices;
    j["ask_prices"] = ask_prices;
    j["bid_volumes"] = bid_volumes;
    j["ask_volumes"] = ask_volumes;
    
    // 可选字段
    if (upper_limit.has_value()) {
        j["upper_limit"] = upper_limit.value();
    }
    if (lower_limit.has_value()) {
        j["lower_limit"] = lower_limit.value();
    }
    if (settlement_price.has_value()) {
        j["settlement_price"] = settlement_price.value();
    }
    if (open_interest.has_value()) {
        j["open_interest"] = open_interest.value();
    }
    
    return j;
}

QuoteData QuoteData::from_json(const nlohmann::json& j) {
    QuoteData quote;
    
    quote.symbol = j.at("symbol").get<std::string>();
    quote.timestamp = Timestamp{std::chrono::milliseconds{j.at("timestamp").get<int64_t>()}};
    quote.last_price = j.at("last_price").get<double>();
    quote.open_price = j.value("open_price", 0.0);
    quote.high_price = j.value("high_price", 0.0);
    quote.low_price = j.value("low_price", 0.0);
    quote.close_price = j.value("close_price", 0.0);
    quote.pre_close = j.value("pre_close", 0.0);
    quote.volume = j.at("volume").get<uint64_t>();
    quote.amount = j.at("amount").get<double>();
    
    // 买卖盘数据
    if (j.contains("bid_prices")) {
        auto bid_prices_vec = j.at("bid_prices").get<std::vector<double>>();
        std::copy_n(bid_prices_vec.begin(), 
                   std::min(bid_prices_vec.size(), quote.bid_prices.size()),
                   quote.bid_prices.begin());
    }
    
    if (j.contains("ask_prices")) {
        auto ask_prices_vec = j.at("ask_prices").get<std::vector<double>>();
        std::copy_n(ask_prices_vec.begin(), 
                   std::min(ask_prices_vec.size(), quote.ask_prices.size()),
                   quote.ask_prices.begin());
    }
    
    if (j.contains("bid_volumes")) {
        auto bid_volumes_vec = j.at("bid_volumes").get<std::vector<uint64_t>>();
        std::copy_n(bid_volumes_vec.begin(), 
                   std::min(bid_volumes_vec.size(), quote.bid_volumes.size()),
                   quote.bid_volumes.begin());
    }
    
    if (j.contains("ask_volumes")) {
        auto ask_volumes_vec = j.at("ask_volumes").get<std::vector<uint64_t>>();
        std::copy_n(ask_volumes_vec.begin(), 
                   std::min(ask_volumes_vec.size(), quote.ask_volumes.size()),
                   quote.ask_volumes.begin());
    }
    
    // 可选字段
    if (j.contains("upper_limit")) {
        quote.upper_limit = j.at("upper_limit").get<double>();
    }
    if (j.contains("lower_limit")) {
        quote.lower_limit = j.at("lower_limit").get<double>();
    }
    if (j.contains("settlement_price")) {
        quote.settlement_price = j.at("settlement_price").get<double>();
    }
    if (j.contains("open_interest")) {
        quote.open_interest = j.at("open_interest").get<uint64_t>();
    }
    
    return quote;
}

// BarData 实现
std::string BarData::to_string() const {
    return fmt::format(
        "BarData{{symbol={}, timestamp={}, bar_size={}, bar_type={}, "
        "OHLCV=[{:.4f},{:.4f},{:.4f},{:.4f},{}], amount={:.2f}}}",
        symbol,
        format_timestamp(timestamp, "%Y-%m-%d %H:%M:%S"),
        Core::to_string(bar_size),
        Core::to_string(bar_type),
        open, high, low, close, volume, amount
    );
}

nlohmann::json BarData::to_json() const {
    nlohmann::json j;
    j["symbol"] = symbol;
    j["timestamp"] = timestamp.time_since_epoch().count();
    j["bar_size"] = static_cast<uint8_t>(bar_size);
    j["bar_type"] = static_cast<uint8_t>(bar_type);
    j["open"] = open;
    j["high"] = high;
    j["low"] = low;
    j["close"] = close;
    j["volume"] = volume;
    j["amount"] = amount;
    
    // 可选字段
    if (vwap.has_value()) {
        j["vwap"] = vwap.value();
    }
    if (trade_count.has_value()) {
        j["trade_count"] = trade_count.value();
    }
    if (pre_close.has_value()) {
        j["pre_close"] = pre_close.value();
    }
    
    return j;
}

BarData BarData::from_json(const nlohmann::json& j) {
    BarData bar;
    
    bar.symbol = j.at("symbol").get<std::string>();
    bar.timestamp = Timestamp{std::chrono::milliseconds{j.at("timestamp").get<int64_t>()}};
    bar.bar_size = static_cast<BarSize>(j.at("bar_size").get<uint8_t>());
    bar.bar_type = static_cast<BarType>(j.at("bar_type").get<uint8_t>());
    bar.open = j.at("open").get<double>();
    bar.high = j.at("high").get<double>();
    bar.low = j.at("low").get<double>();
    bar.close = j.at("close").get<double>();
    bar.volume = j.at("volume").get<uint64_t>();
    bar.amount = j.at("amount").get<double>();
    
    // 可选字段
    if (j.contains("vwap")) {
        bar.vwap = j.at("vwap").get<double>();
    }
    if (j.contains("trade_count")) {
        bar.trade_count = j.at("trade_count").get<uint64_t>();
    }
    if (j.contains("pre_close")) {
        bar.pre_close = j.at("pre_close").get<double>();
    }
    
    return bar;
}

void BarData::merge_with(const BarData& other) {
    if (symbol != other.symbol || bar_size != other.bar_size || bar_type != other.bar_type) {
        return; // 不能合并不同类型的K线
    }
    
    // 更新时间戳为较新的
    timestamp = std::max(timestamp, other.timestamp);
    
    // 更新OHLC
    if (is_zero(open)) {
        open = other.open;
    }
    high = std::max(high, other.high);
    if (is_zero(low)) {
        low = other.low;
    } else {
        low = std::min(low, other.low);
    }
    close = other.close; // 使用最新的收盘价
    
    // 累加成交量和成交额
    volume += other.volume;
    amount += other.amount;
    
    // 更新可选字段
    if (other.vwap.has_value()) {
        if (vwap.has_value()) {
            // 重新计算VWAP
            vwap = (amount > 0) ? amount / volume : close;
        } else {
            vwap = other.vwap;
        }
    }
    
    if (other.trade_count.has_value()) {
        if (trade_count.has_value()) {
            trade_count = trade_count.value() + other.trade_count.value();
        } else {
            trade_count = other.trade_count;
        }
    }
}

// TickData 实现
std::string TickData::to_string() const {
    return fmt::format(
        "TickData{{symbol={}, timestamp={}, price={:.4f}, "
        "volume={}, amount={:.2f}, direction={}}}",
        symbol,
        format_timestamp(timestamp, "%Y-%m-%d %H:%M:%S.%f"),
        price, volume, amount,
        static_cast<int>(direction)
    );
}

nlohmann::json TickData::to_json() const {
    nlohmann::json j;
    j["symbol"] = symbol;
    j["timestamp"] = timestamp.time_since_epoch().count();
    j["price"] = price;
    j["volume"] = volume;
    j["amount"] = amount;
    j["direction"] = static_cast<uint8_t>(direction);
    
    return j;
}

TickData TickData::from_json(const nlohmann::json& j) {
    TickData tick;
    
    tick.symbol = j.at("symbol").get<std::string>();
    tick.timestamp = Timestamp{std::chrono::milliseconds{j.at("timestamp").get<int64_t>()}};
    tick.price = j.at("price").get<double>();
    tick.volume = j.at("volume").get<uint64_t>();
    tick.amount = j.at("amount").get<double>();
    tick.direction = static_cast<TickData::Direction>(j.at("direction").get<uint8_t>());
    
    return tick;
}

// 工具函数实现
BarDataVector aggregate_ticks_to_bars(const TickDataVector& ticks, 
                                     BarSize bar_size, 
                                     BarType bar_type) {
    if (ticks.empty()) {
        return {};
    }
    
    BarDataVector bars;
    
    // 按时间排序
    auto sorted_ticks = ticks;
    std::sort(sorted_ticks.begin(), sorted_ticks.end(),
              [](const TickData& a, const TickData& b) {
                  return a.timestamp < b.timestamp;
              });
    
    // 计算时间间隔
    auto interval = get_bar_interval(bar_size);
    if (interval == Duration::zero()) {
        return {}; // 不支持的K线周期
    }
    
    BarData current_bar;
    current_bar.symbol = sorted_ticks[0].symbol;
    current_bar.bar_size = bar_size;
    current_bar.bar_type = bar_type;
    
    auto bar_start_time = align_timestamp_to_interval(sorted_ticks[0].timestamp, interval);
    current_bar.timestamp = bar_start_time;
    
    for (const auto& tick : sorted_ticks) {
        auto tick_bar_time = align_timestamp_to_interval(tick.timestamp, interval);
        
        if (tick_bar_time != bar_start_time) {
            // 开始新的K线
            if (current_bar.volume > 0) {
                bars.push_back(current_bar);
            }
            
            // 重置当前K线
            current_bar = BarData{};
            current_bar.symbol = tick.symbol;
            current_bar.bar_size = bar_size;
            current_bar.bar_type = bar_type;
            current_bar.timestamp = tick_bar_time;
            bar_start_time = tick_bar_time;
        }
        
        // 更新K线数据
        if (is_zero(current_bar.open)) {
            current_bar.open = tick.price;
            current_bar.high = tick.price;
            current_bar.low = tick.price;
        } else {
            current_bar.high = std::max(current_bar.high, tick.price);
            current_bar.low = std::min(current_bar.low, tick.price);
        }
        current_bar.close = tick.price;
        current_bar.volume += tick.volume;
        current_bar.amount += tick.amount;
    }
    
    // 添加最后一个K线
    if (current_bar.volume > 0) {
        bars.push_back(current_bar);
    }
    
    return bars;
}

Duration get_bar_interval(BarSize bar_size) {
    switch (bar_size) {
        case BarSize::Second1: return std::chrono::seconds{1};
        case BarSize::Second5: return std::chrono::seconds{5};
        case BarSize::Second15: return std::chrono::seconds{15};
        case BarSize::Second30: return std::chrono::seconds{30};
        case BarSize::Minute1: return std::chrono::minutes{1};
        case BarSize::Minute5: return std::chrono::minutes{5};
        case BarSize::Minute15: return std::chrono::minutes{15};
        case BarSize::Minute30: return std::chrono::minutes{30};
        case BarSize::Hour1: return std::chrono::hours{1};
        case BarSize::Day: return std::chrono::hours{24};
        default: return Duration::zero();
    }
}

Timestamp align_timestamp_to_interval(const Timestamp& timestamp, const Duration& interval) {
    auto epoch_ms = timestamp.time_since_epoch();
    auto interval_ms = std::chrono::duration_cast<std::chrono::milliseconds>(interval);
    
    auto aligned_ms = (epoch_ms / interval_ms) * interval_ms;
    return Timestamp{aligned_ms};
}

} // namespace DataHub::Core
