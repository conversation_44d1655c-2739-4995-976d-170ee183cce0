#include "core/SecurityInfo.h"
#include <algorithm>

namespace DataHub::Core {

// SecurityInfo 实现
std::string SecurityInfo::to_string() const {
    return fmt::format(
        "SecurityInfo{{symbol={}, name={}, market_type={}, exchange={}, "
        "tick_size={:.4f}, is_active={}}}",
        symbol, name, Core::to_string(market_type), Core::to_string(exchange),
        tick_size, is_active
    );
}

nlohmann::json SecurityInfo::to_json() const {
    nlohmann::json j;
    j["symbol"] = symbol;
    j["name"] = name;
    j["full_name"] = full_name;
    j["market_type"] = static_cast<uint8_t>(market_type);
    j["exchange"] = static_cast<uint8_t>(exchange);
    j["tick_size"] = tick_size;
    j["min_order_qty"] = min_order_qty;
    j["max_order_qty"] = max_order_qty;
    j["is_active"] = is_active;
    j["is_tradable"] = is_tradable;
    j["list_date"] = list_date.time_since_epoch().count();
    
    if (price_limit_up_ratio.has_value()) {
        j["price_limit_up_ratio"] = price_limit_up_ratio.value();
    }
    if (price_limit_down_ratio.has_value()) {
        j["price_limit_down_ratio"] = price_limit_down_ratio.value();
    }
    if (delist_date.has_value()) {
        j["delist_date"] = delist_date.value().time_since_epoch().count();
    }
    
    // 类型特定信息
    if (auto* future_info = get_future_info()) {
        nlohmann::json future_j;
        future_j["underlying"] = future_info->underlying;
        future_j["contract_month"] = future_info->contract_month;
        future_j["contract_multiplier"] = future_info->contract_multiplier;
        future_j["expiry_date"] = future_info->expiry_date.time_since_epoch().count();
        future_j["delivery_date"] = future_info->delivery_date.time_since_epoch().count();
        future_j["margin_ratio"] = future_info->margin_ratio;
        future_j["is_main_contract"] = future_info->is_main_contract;
        future_j["main_symbol"] = future_info->main_symbol;
        j["future_info"] = future_j;
    }
    
    if (auto* stock_info = get_stock_info()) {
        nlohmann::json stock_j;
        stock_j["industry"] = stock_info->industry;
        stock_j["sector"] = stock_info->sector;
        stock_j["region"] = stock_info->region;
        stock_j["total_shares"] = stock_info->total_shares;
        stock_j["float_shares"] = stock_info->float_shares;
        stock_j["market_cap"] = stock_info->market_cap;
        stock_j["is_st"] = stock_info->is_st;
        stock_j["is_suspended"] = stock_info->is_suspended;
        j["stock_info"] = stock_j;
    }
    
    if (auto* option_info = get_option_info()) {
        nlohmann::json option_j;
        option_j["underlying"] = option_info->underlying;
        option_j["strike_price"] = option_info->strike_price;
        option_j["expiry_date"] = option_info->expiry_date.time_since_epoch().count();
        option_j["option_type"] = static_cast<uint8_t>(option_info->option_type);
        option_j["exercise_style"] = static_cast<uint8_t>(option_info->exercise_style);
        j["option_info"] = option_j;
    }
    
    return j;
}

SecurityInfo SecurityInfo::from_json(const nlohmann::json& j) {
    SecurityInfo security;
    
    security.symbol = j.at("symbol").get<std::string>();
    security.name = j.at("name").get<std::string>();
    security.full_name = j.value("full_name", "");
    security.market_type = static_cast<MarketType>(j.at("market_type").get<uint8_t>());
    security.exchange = static_cast<Exchange>(j.at("exchange").get<uint8_t>());
    security.tick_size = j.at("tick_size").get<double>();
    security.min_order_qty = j.value("min_order_qty", 100ULL);
    security.max_order_qty = j.value("max_order_qty", 1000000ULL);
    security.is_active = j.value("is_active", true);
    security.is_tradable = j.value("is_tradable", true);
    
    if (j.contains("list_date")) {
        security.list_date = Timestamp{std::chrono::milliseconds{j.at("list_date").get<int64_t>()}};
    }
    
    if (j.contains("price_limit_up_ratio")) {
        security.price_limit_up_ratio = j.at("price_limit_up_ratio").get<double>();
    }
    if (j.contains("price_limit_down_ratio")) {
        security.price_limit_down_ratio = j.at("price_limit_down_ratio").get<double>();
    }
    if (j.contains("delist_date")) {
        security.delist_date = Timestamp{std::chrono::milliseconds{j.at("delist_date").get<int64_t>()}};
    }
    
    // 类型特定信息
    if (j.contains("future_info")) {
        FutureInfo future_info;
        auto future_j = j.at("future_info");
        future_info.underlying = future_j.at("underlying").get<std::string>();
        future_info.contract_month = future_j.at("contract_month").get<std::string>();
        future_info.contract_multiplier = future_j.at("contract_multiplier").get<double>();
        future_info.expiry_date = Timestamp{std::chrono::milliseconds{future_j.at("expiry_date").get<int64_t>()}};
        future_info.delivery_date = Timestamp{std::chrono::milliseconds{future_j.at("delivery_date").get<int64_t>()}};
        future_info.margin_ratio = future_j.at("margin_ratio").get<double>();
        future_info.is_main_contract = future_j.value("is_main_contract", false);
        future_info.main_symbol = future_j.value("main_symbol", "");
        security.set_future_info(std::move(future_info));
    }
    
    if (j.contains("stock_info")) {
        StockInfo stock_info;
        auto stock_j = j.at("stock_info");
        stock_info.industry = stock_j.value("industry", "");
        stock_info.sector = stock_j.value("sector", "");
        stock_info.region = stock_j.value("region", "");
        stock_info.total_shares = stock_j.value("total_shares", 0ULL);
        stock_info.float_shares = stock_j.value("float_shares", 0ULL);
        stock_info.market_cap = stock_j.value("market_cap", 0.0);
        stock_info.is_st = stock_j.value("is_st", false);
        stock_info.is_suspended = stock_j.value("is_suspended", false);
        security.set_stock_info(std::move(stock_info));
    }
    
    if (j.contains("option_info")) {
        OptionInfo option_info;
        auto option_j = j.at("option_info");
        option_info.underlying = option_j.at("underlying").get<std::string>();
        option_info.strike_price = option_j.at("strike_price").get<double>();
        option_info.expiry_date = Timestamp{std::chrono::milliseconds{option_j.at("expiry_date").get<int64_t>()}};
        option_info.option_type = static_cast<OptionInfo::OptionType>(option_j.at("option_type").get<uint8_t>());
        option_info.exercise_style = static_cast<OptionInfo::ExerciseStyle>(option_j.at("exercise_style").get<uint8_t>());
        security.set_option_info(std::move(option_info));
    }
    
    return security;
}

// BlockInfo 实现
std::string BlockInfo::to_string() const {
    return fmt::format(
        "BlockInfo{{name={}, type={}, description={}, size={}}}",
        name, static_cast<int>(block_type), description, symbols.size()
    );
}

nlohmann::json BlockInfo::to_json() const {
    nlohmann::json j;
    j["name"] = name;
    j["description"] = description;
    j["block_type"] = static_cast<uint8_t>(block_type);
    j["symbols"] = symbols;
    return j;
}

BlockInfo BlockInfo::from_json(const nlohmann::json& j) {
    BlockInfo block;
    block.name = j.at("name").get<std::string>();
    block.description = j.value("description", "");
    block.block_type = static_cast<BlockType>(j.at("block_type").get<uint8_t>());
    block.symbols = j.value("symbols", DataVector<Symbol>{});
    return block;
}

} // namespace DataHub::Core
