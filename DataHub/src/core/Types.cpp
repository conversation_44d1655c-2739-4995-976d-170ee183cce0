#include "core/Types.h"
#include <sstream>
#include <iomanip>

namespace DataHub::Core {

// 枚举到字符串的转换函数
std::string to_string(MarketType market_type) {
    switch (market_type) {
        case MarketType::Stock: return "Stock";
        case MarketType::Future: return "Future";
        case MarketType::Option: return "Option";
        case MarketType::Bond: return "Bond";
        case MarketType::Fund: return "Fund";
        case MarketType::Crypto: return "Crypto";
        default: return "Unknown";
    }
}

std::string to_string(Exchange exchange) {
    switch (exchange) {
        case Exchange::SSE: return "SSE";
        case Exchange::SZSE: return "SZSE";
        case Exchange::SHFE: return "SHFE";
        case Exchange::DCE: return "DCE";
        case Exchange::CZCE: return "CZCE";
        case Exchange::CFFEX: return "CFFEX";
        case Exchange::INE: return "INE";
        case Exchange::BSE: return "BSE";
        case Exchange::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(BarSize bar_size) {
    switch (bar_size) {
        case BarSize::Tick: return "Tick";
        case BarSize::Second1: return "1s";
        case BarSize::Second5: return "5s";
        case BarSize::Second15: return "15s";
        case BarSize::Second30: return "30s";
        case BarSize::Minute1: return "1m";
        case BarSize::Minute5: return "5m";
        case BarSize::Minute15: return "15m";
        case BarSize::Minute30: return "30m";
        case BarSize::Hour1: return "1h";
        case BarSize::Day: return "1d";
        case BarSize::Week: return "1w";
        case BarSize::Month: return "1M";
        case BarSize::Quarter: return "1Q";
        case BarSize::Year: return "1Y";
        case BarSize::Range: return "Range";
        default: return "Unknown";
    }
}

std::string to_string(BarType bar_type) {
    switch (bar_type) {
        case BarType::Candle: return "Candle";
        case BarType::Range: return "Range";
        case BarType::Volume: return "Volume";
        case BarType::Tick: return "Tick";
        default: return "Unknown";
    }
}

std::string to_string(DataRunMode run_mode) {
    switch (run_mode) {
        case DataRunMode::Active: return "Active";
        case DataRunMode::Simulation: return "Simulation";
        case DataRunMode::Backtest: return "Backtest";
        default: return "Unknown";
    }
}

std::string to_string(ErrorCode error_code) {
    switch (error_code) {
        case ErrorCode::Success: return "Success";
        case ErrorCode::InvalidParameter: return "InvalidParameter";
        case ErrorCode::DataNotFound: return "DataNotFound";
        case ErrorCode::DatabaseError: return "DatabaseError";
        case ErrorCode::NetworkError: return "NetworkError";
        case ErrorCode::PermissionDenied: return "PermissionDenied";
        case ErrorCode::InternalError: return "InternalError";
        default: return "Unknown";
    }
}

// 字符串到枚举的转换函数
std::optional<MarketType> parse_market_type(const std::string& str) {
    if (str == "Stock") return MarketType::Stock;
    if (str == "Future") return MarketType::Future;
    if (str == "Option") return MarketType::Option;
    if (str == "Bond") return MarketType::Bond;
    if (str == "Fund") return MarketType::Fund;
    if (str == "Crypto") return MarketType::Crypto;
    return std::nullopt;
}

std::optional<Exchange> parse_exchange(const std::string& str) {
    if (str == "SSE") return Exchange::SSE;
    if (str == "SZSE") return Exchange::SZSE;
    if (str == "SHFE") return Exchange::SHFE;
    if (str == "DCE") return Exchange::DCE;
    if (str == "CZCE") return Exchange::CZCE;
    if (str == "CFFEX") return Exchange::CFFEX;
    if (str == "INE") return Exchange::INE;
    if (str == "BSE") return Exchange::BSE;
    return std::nullopt;
}

std::optional<BarSize> parse_bar_size(const std::string& str) {
    if (str == "Tick") return BarSize::Tick;
    if (str == "1s") return BarSize::Second1;
    if (str == "5s") return BarSize::Second5;
    if (str == "15s") return BarSize::Second15;
    if (str == "30s") return BarSize::Second30;
    if (str == "1m") return BarSize::Minute1;
    if (str == "5m") return BarSize::Minute5;
    if (str == "15m") return BarSize::Minute15;
    if (str == "30m") return BarSize::Minute30;
    if (str == "1h") return BarSize::Hour1;
    if (str == "1d") return BarSize::Day;
    if (str == "1w") return BarSize::Week;
    if (str == "1M") return BarSize::Month;
    if (str == "1Q") return BarSize::Quarter;
    if (str == "1Y") return BarSize::Year;
    if (str == "Range") return BarSize::Range;
    return std::nullopt;
}

std::optional<BarType> parse_bar_type(const std::string& str) {
    if (str == "Candle") return BarType::Candle;
    if (str == "Range") return BarType::Range;
    if (str == "Volume") return BarType::Volume;
    if (str == "Tick") return BarType::Tick;
    return std::nullopt;
}

// 时间相关工具函数
std::string format_timestamp(const Timestamp& timestamp, const std::string& format) {
    auto time_t = std::chrono::system_clock::to_time_t(timestamp);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), format.c_str());
    
    // 添加毫秒
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()) % 1000;
    if (format.find("%f") != std::string::npos) {
        std::string result = ss.str();
        size_t pos = result.find("%f");
        if (pos != std::string::npos) {
            result.replace(pos, 2, std::to_string(ms.count()));
        }
        return result;
    }
    
    return ss.str();
}

Timestamp parse_timestamp(const std::string& str, const std::string& format) {
    std::tm tm = {};
    std::istringstream ss(str);
    ss >> std::get_time(&tm, format.c_str());
    
    if (ss.fail()) {
        return Timestamp{};
    }
    
    auto time_point = std::chrono::system_clock::from_time_t(std::mktime(&tm));
    return std::chrono::time_point_cast<std::chrono::milliseconds>(time_point);
}

Timestamp now() {
    return std::chrono::time_point_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now());
}

// 数值比较工具函数
bool is_equal(double a, double b, double epsilon) {
    return std::abs(a - b) < epsilon;
}

bool is_zero(double value, double epsilon) {
    return std::abs(value) < epsilon;
}

bool is_positive(double value, double epsilon) {
    return value > epsilon;
}

bool is_negative(double value, double epsilon) {
    return value < -epsilon;
}

// 字符串工具函数
std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r\f\v");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(start, end - start + 1);
}

std::vector<std::string> split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(trim(token));
    }
    
    return tokens;
}

std::string join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }
    
    std::ostringstream oss;
    oss << strings[0];
    
    for (size_t i = 1; i < strings.size(); ++i) {
        oss << delimiter << strings[i];
    }
    
    return oss.str();
}

// 哈希函数
std::size_t hash_combine(std::size_t seed, std::size_t hash) {
    return seed ^ (hash + 0x9e3779b9 + (seed << 6) + (seed >> 2));
}

} // namespace DataHub::Core
