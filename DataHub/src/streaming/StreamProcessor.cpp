#include "streaming/StreamProcessor.h"
#include "core/Logging.h"
#include <algorithm>

namespace DataHub::Streaming {

StreamProcessor::StreamProcessor(std::shared_ptr<Services::IDataHubManager> datahub, 
                               StreamProcessorConfig config)
    : datahub_(std::move(datahub))
    , config_(std::move(config))
    , running_(false)
    , stop_requested_(false)
    , event_queue_(config_.queue_capacity)
    , has_events_(false) {
    
    Core::log_info("StreamProcessor", "Created with {} worker threads, queue capacity {}",
                  config_.worker_thread_count, config_.queue_capacity);
}

StreamProcessor::~StreamProcessor() {
    stop();
}

Core::Result<void> StreamProcessor::start() {
    if (running_.load(std::memory_order_acquire)) {
        return Core::make_success();
    }
    
    try {
        stop_requested_.store(false, std::memory_order_release);
        running_.store(true, std::memory_order_release);
        
        // 创建工作线程
        worker_threads_.reserve(config_.worker_thread_count);
        for (std::size_t i = 0; i < config_.worker_thread_count; ++i) {
            worker_threads_.emplace_back(
                std::make_unique<std::thread>([this, i]() {
                    worker_thread_function(static_cast<int>(i));
                })
            );
        }
        
        Core::log_info("StreamProcessor", "Started with {} worker threads", worker_threads_.size());
        return Core::make_success();
        
    } catch (const std::exception& e) {
        running_.store(false, std::memory_order_release);
        return Core::make_error<void>(Core::ErrorCode::StartupFailed,
            "Failed to start stream processor: " + std::string(e.what()));
    }
}

Core::Result<void> StreamProcessor::stop() {
    if (!running_.load(std::memory_order_acquire)) {
        return Core::make_success();
    }
    
    Core::log_info("StreamProcessor", "Stopping stream processor...");
    
    stop_requested_.store(true, std::memory_order_release);
    
    // 等待所有工作线程完成
    for (auto& thread : worker_threads_) {
        if (thread && thread->joinable()) {
            thread->join();
        }
    }
    
    worker_threads_.clear();
    running_.store(false, std::memory_order_release);
    
    Core::log_info("StreamProcessor", "Stream processor stopped");
    return Core::make_success();
}

bool StreamProcessor::is_running() const noexcept {
    return running_.load(std::memory_order_acquire);
}

Core::Result<std::string> StreamProcessor::subscribe(const StreamFilter& filter, 
                                                   StreamEventCallback callback) {
    if (!running_.load(std::memory_order_acquire)) {
        return Core::make_error<std::string>(Core::ErrorCode::InvalidState,
            "Stream processor is not running");
    }
    
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    
    std::string subscription_id = generate_subscription_id();
    
    Subscription subscription;
    subscription.id = subscription_id;
    subscription.filter = filter;
    subscription.callback = std::move(callback);
    subscription.created_time = std::chrono::system_clock::now();
    
    subscriptions_[subscription_id] = std::move(subscription);
    
    Core::log_debug("StreamProcessor", "Added subscription: {}", subscription_id);
    return Core::make_success(subscription_id);
}

Core::Result<void> StreamProcessor::unsubscribe(const std::string& subscription_id) {
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    
    auto it = subscriptions_.find(subscription_id);
    if (it == subscriptions_.end()) {
        return Core::make_error<void>(Core::ErrorCode::NotFound,
            "Subscription not found: " + subscription_id);
    }
    
    subscriptions_.erase(it);
    
    Core::log_debug("StreamProcessor", "Removed subscription: {}", subscription_id);
    return Core::make_success();
}

Core::Result<void> StreamProcessor::publish_event(StreamEvent event) {
    if (!running_.load(std::memory_order_acquire)) {
        return Core::make_error<void>(Core::ErrorCode::InvalidState,
            "Stream processor is not running");
    }
    
    // 使用无锁队列发布事件
    if (event_queue_.enqueue(std::move(event))) {
        has_events_.store(true, std::memory_order_release);
        
        // 更新统计
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_events++;
        
        return Core::make_success();
    } else {
        // 队列满
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.dropped_events++;
        
        return Core::make_error<void>(Core::ErrorCode::QueueFull,
            "Event queue is full");
    }
}

Core::Result<StreamProcessorStats> StreamProcessor::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return Core::make_success(stats_);
}

Core::Result<void> StreamProcessor::reset_statistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = StreamProcessorStats{};
    return Core::make_success();
}

Core::Result<std::size_t> StreamProcessor::get_queue_size() const {
    return Core::make_success(event_queue_.size_approx());
}

Core::Result<void> StreamProcessor::clear_queue() {
    StreamEvent event;
    while (event_queue_.try_dequeue(event)) {
        // 清空队列
    }
    has_events_.store(false, std::memory_order_release);
    
    return Core::make_success();
}

void StreamProcessor::worker_thread_function(int thread_id) {
    Core::log_debug("StreamProcessor", "Worker thread {} started", thread_id);
    
    // 批量处理缓冲区
    constexpr size_t BATCH_SIZE = 50;
    StreamEvent batch_buffer[BATCH_SIZE];
    
    while (!stop_requested_.load(std::memory_order_acquire)) {
        // 检查是否有事件可处理
        if (!has_events_.load(std::memory_order_acquire)) {
            // 没有事件时短暂休眠
            std::this_thread::sleep_for(std::chrono::microseconds(100));
            continue;
        }
        
        // 批量出队事件
        size_t dequeued = event_queue_.try_dequeue_bulk(batch_buffer, BATCH_SIZE);
        
        if (dequeued == 0) {
            // 队列为空，重置标志
            has_events_.store(false, std::memory_order_release);
            continue;
        }
        
        // 批量处理事件
        for (size_t i = 0; i < dequeued; ++i) {
            try {
                process_single_event(batch_buffer[i]);
                
                // 更新统计
                std::lock_guard<std::mutex> lock(stats_mutex_);
                stats_.processed_events++;
                
            } catch (const std::exception& e) {
                Core::log_error("StreamProcessor", "Error processing event in thread {}: {}", 
                               thread_id, e.what());
                
                std::lock_guard<std::mutex> lock(stats_mutex_);
                stats_.failed_events++;
            }
        }
        
        // 如果本次处理的事件量小于批量大小，说明队列可能已空
        if (dequeued < BATCH_SIZE) {
            StreamEvent temp;
            if (!event_queue_.try_dequeue(temp)) {
                has_events_.store(false, std::memory_order_release);
            } else {
                // 还有事件，处理这个额外的事件
                try {
                    process_single_event(temp);
                    
                    std::lock_guard<std::mutex> lock(stats_mutex_);
                    stats_.processed_events++;
                } catch (const std::exception& e) {
                    Core::log_error("StreamProcessor", "Error processing extra event: {}", e.what());
                    
                    std::lock_guard<std::mutex> lock(stats_mutex_);
                    stats_.failed_events++;
                }
            }
        }
    }
    
    // 处理剩余事件
    StreamEvent remaining_event;
    while (event_queue_.try_dequeue(remaining_event)) {
        try {
            process_single_event(remaining_event);
        } catch (const std::exception& e) {
            Core::log_error("StreamProcessor", "Error processing remaining event: {}", e.what());
        }
    }
    
    Core::log_debug("StreamProcessor", "Worker thread {} stopped", thread_id);
}

void StreamProcessor::process_single_event(const StreamEvent& event) {
    std::lock_guard<std::mutex> lock(subscriptions_mutex_);
    
    for (const auto& [id, subscription] : subscriptions_) {
        if (matches_filter(event, subscription.filter)) {
            try {
                subscription.callback(event);
            } catch (const std::exception& e) {
                Core::log_error("StreamProcessor", "Error in subscription callback {}: {}", 
                               id, e.what());
            }
        }
    }
}

bool StreamProcessor::matches_filter(const StreamEvent& event, const StreamFilter& filter) const {
    // 检查事件类型
    if (!filter.event_types.empty()) {
        if (std::find(filter.event_types.begin(), filter.event_types.end(), event.type) 
            == filter.event_types.end()) {
            return false;
        }
    }
    
    // 检查符号过滤
    if (!filter.symbols.empty()) {
        if (std::find(filter.symbols.begin(), filter.symbols.end(), event.symbol) 
            == filter.symbols.end()) {
            return false;
        }
    }
    
    // 检查时间范围
    if (filter.start_time.has_value() && event.timestamp < filter.start_time.value()) {
        return false;
    }
    
    if (filter.end_time.has_value() && event.timestamp > filter.end_time.value()) {
        return false;
    }
    
    return true;
}

std::string StreamProcessor::generate_subscription_id() {
    static std::atomic<uint64_t> counter{0};
    return "sub_" + std::to_string(counter.fetch_add(1, std::memory_order_relaxed));
}

} // namespace DataHub::Streaming
