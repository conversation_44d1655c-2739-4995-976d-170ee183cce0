# DataHub_Modern 市场数据模块

## 概述

DataHub_Modern 的市场数据模块是一个现代化的 C++20 行情数据接收和管理系统，支持多种数据源，提供统一的接口和高性能的数据处理能力。

## 主要特性

### ? 核心功能
- **多数据源支持**：CTP期货行情、新浪/腾讯股票行情
- **统一接口**：抽象化的市场数据提供者接口
- **异步处理**：基于C++20的现代异步编程模型
- **智能路由**：支持主备切换、负载均衡、故障转移
- **高性能缓存**：内存缓存机制，支持TTL和LRU策略
- **实时推送**：基于回调的实时数据推送机制

### ?? 可靠性保障
- **连接管理**：自动重连、心跳检测、连接池
- **错误处理**：完善的错误处理和恢复机制
- **数据验证**：行情数据完整性和合理性验证
- **重复检测**：防止重复数据处理
- **监控告警**：实时监控和性能指标收集

### ?? 配置管理
- **JSON配置**：灵活的JSON配置文件支持
- **热重载**：运行时配置热重载
- **环境变量**：支持环境变量配置
- **配置加密**：敏感信息加密存储
- **配置迁移**：版本间配置自动迁移

## 快速开始

### 1. 基本使用

```cpp
#include "services/MarketDataService.h"
#include "providers/ProviderFactory.h"

using namespace DataHub::Services;
using namespace DataHub::Providers;

// 创建市场数据服务
auto service = create_market_data_service(
    "",  // CTP前置地址（留空表示不使用CTP）
    "",  // CTP经纪商ID
    "",  // CTP用户ID
    "",  // CTP密码
    WebDataSource::Sina  // 使用新浪数据源
);

// 启动服务
auto start_result = service->start();
if (!start_result.is_success()) {
    std::cerr << "Failed to start service: " << start_result.error().message() << std::endl;
    return -1;
}

// 订阅行情
std::vector<Symbol> symbols = {"000001.SZ", "600000.SH"};
auto subscribe_result = service->subscribe_quotes(symbols);

// 设置行情回调
service->set_quote_callback([](const Core::QuoteData& quote) {
    std::cout << "Quote: " << quote.symbol << " = " << quote.last_price << std::endl;
});

// 获取行情数据
auto quote_result = service->get_quote("000001.SZ");
if (quote_result.is_success()) {
    auto quote = quote_result.value();
    std::cout << "Latest quote: " << quote.symbol << " = " << quote.last_price << std::endl;
}
```

### 2. 配置文件使用

```cpp
#include "config/MarketDataConfig.h"

using namespace DataHub::Config;

// 加载配置
auto config_manager = std::make_unique<ConfigManager>("config/market_data_config.json");
auto load_result = config_manager->load_config();

// 创建服务
auto service_config = config_manager->get_service_config();
auto service = std::make_unique<MarketDataService>(service_config);

// 添加提供者
if (auto ctp_config = config_manager->get_ctp_config()) {
    service->add_ctp_provider(ctp_config);
}

if (auto webdata_config = config_manager->get_webdata_config()) {
    service->add_webdata_provider(webdata_config);
}
```

### 3. 高级配置

```cpp
// 设置路由策略
service->set_routing_strategy(RoutingStrategy::Failover);

// 更新提供者优先级
service->update_provider_priority(ProviderType::CTP_Futures, 1);
service->update_provider_priority(ProviderType::WebData_Stock, 2);

// 设置回调
service->set_provider_status_callback([](ProviderType type, ConnectionStatus status) {
    std::cout << "Provider " << static_cast<int>(type) 
              << " status changed to " << static_cast<int>(status) << std::endl;
});

service->set_error_callback([](const std::string& error) {
    std::cerr << "Service error: " << error << std::endl;
});
```

## 架构设计

### 组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    MarketDataService                        │
├─────────────────────────────────────────────────────────────┤
│                  MarketDataManager                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│ CtpProvider     │ WebDataProvider │ Other Providers...      │
├─────────────────┼─────────────────┼─────────────────────────┤
│ CTP API         │ HTTP Client     │ ...                     │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 数据流程

```
数据源 → 提供者 → 管理器 → 服务 → 用户回调
  ↓        ↓        ↓       ↓        ↓
CTP API  解析器   路由器   缓存    应用程序
网络API   验证器   聚合器   事件
```

## 配置说明

### 主要配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `service.name` | 服务名称 | "DataHub_Modern_MarketDataService" |
| `service.enable_logging` | 启用日志 | true |
| `service.enable_metrics` | 启用指标收集 | true |
| `providers.webdata.data_source` | 数据源类型 | "sina" |
| `providers.webdata.update_interval` | 更新间隔(ms) | 3000 |
| `manager.routing_strategy` | 路由策略 | "primary" |
| `manager.enable_failover` | 启用故障转移 | true |

### CTP配置

```json
{
  "providers": {
    "ctp": {
      "enabled": true,
      "front_address": "tcp://***************:10131",
      "broker_id": "9999",
      "user_id": "your_user_id",
      "password": "your_password",
      "flow_path": "./ctp_flow/"
    }
  }
}
```

### WebData配置

```json
{
  "providers": {
    "webdata": {
      "enabled": true,
      "data_source": "sina",
      "update_interval": 3000,
      "enable_cache": true,
      "cache_ttl": 30
    }
  }
}
```

## 测试

### 运行测试

```bash
# 编译测试
mkdir build && cd build
cmake .. -DBUILD_TESTING=ON
make

# 运行所有测试
ctest

# 运行特定测试
./tests/unit_tests
./tests/integration_tests
./tests/performance_tests
```

### 测试类型

1. **单元测试**：测试各个组件的独立功能
2. **集成测试**：测试组件间的集成和端到端功能
3. **性能测试**：测试系统性能和压力承受能力
4. **模拟测试**：使用Mock对象测试复杂依赖

### 性能基准

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 单次查询延迟 | < 100ms | 缓存命中情况下 |
| 批量查询延迟 | < 200ms/symbol | 网络请求情况下 |
| 并发处理能力 | > 1000 req/s | 10个并发线程 |
| 缓存命中率 | > 90% | 正常运行情况下 |
| 内存使用 | < 100MB | 1000个符号缓存 |

## 监控和运维

### 健康检查

```cpp
// 检查服务健康状态
auto health_result = service->health_check();
if (health_result.is_success() && health_result.value()) {
    std::cout << "Service is healthy" << std::endl;
}

// 获取提供者状态
auto status = service->get_provider_status();
for (const auto& provider_info : status.providers) {
    std::cout << "Provider: " << provider_info.name 
              << ", Status: " << static_cast<int>(provider_info.status)
              << ", Quotes: " << provider_info.quote_count << std::endl;
}
```

### 统计信息

```cpp
// 获取统计信息
auto stats = service->get_statistics();
std::cout << "Total quotes: " << stats.total_quotes << std::endl;
std::cout << "Total errors: " << stats.total_errors << std::endl;
std::cout << "Cache hits: " << stats.cache_hits << std::endl;
std::cout << "Average latency: " << stats.average_latency.count() << "ms" << std::endl;
```

### 日志配置

```json
{
  "logging": {
    "level": "info",
    "file_path": "./logs/market_data.log",
    "console_output": true,
    "format": "[%Y-%m-%d %H:%M:%S.%f] [%l] [%n] %v"
  }
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证配置参数
   - 查看错误日志

2. **数据延迟**
   - 检查网络延迟
   - 调整更新间隔
   - 启用缓存机制

3. **内存泄漏**
   - 检查缓存大小限制
   - 运行内存检查工具
   - 查看资源使用统计

### 调试技巧

```cpp
// 启用调试日志
Core::set_log_level(Core::LogLevel::Debug);

// 设置详细的错误回调
service->set_error_callback([](const std::string& error) {
    std::cerr << "Detailed error: " << error << std::endl;
    // 可以在这里添加更多调试信息
});

// 监控提供者状态变化
service->set_provider_status_callback([](ProviderType type, ConnectionStatus status) {
    std::cout << "Provider status change: " << static_cast<int>(type) 
              << " -> " << static_cast<int>(status) << std::endl;
});
```

## 扩展开发

### 添加新的数据提供者

1. 继承 `IMarketDataProvider` 接口
2. 实现必要的虚函数
3. 在工厂类中注册新提供者
4. 添加相应的配置支持

### 自定义数据解析器

1. 继承 `IWebDataParser` 接口
2. 实现数据解析逻辑
3. 在WebDataProvider中注册解析器

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者
