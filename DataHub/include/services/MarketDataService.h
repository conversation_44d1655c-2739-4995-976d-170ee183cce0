#pragma once

#include "IDataService.h"
#include "../providers/MarketDataManager.h"
#include "../providers/IMarketDataProvider.h"
#include "../core/Types.h"
#include "../core/MarketData.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <future>

namespace DataHub::Services {

// 市场数据服务配置
struct MarketDataServiceConfig {
    std::string service_name{"MarketDataService"};
    bool enable_logging{true};
    bool enable_metrics{true};
    bool enable_rest_api{false};
    std::uint16_t rest_api_port{8080};
    std::string rest_api_host{"0.0.0.0"};
    
    // 提供者配置
    std::vector<std::shared_ptr<Providers::ProviderConfig>> provider_configs;
    
    // 管理器配置
    Providers::MarketDataManagerConfig manager_config;
};

// REST API请求/响应结构
struct QuoteRequest {
    std::vector<Core::Symbol> symbols;
    bool use_cache{true};
};

struct QuoteResponse {
    std::vector<Core::QuoteData> quotes;
    std::string error_message;
    bool success{true};
    std::chrono::system_clock::time_point timestamp;
};

struct SubscriptionRequest {
    std::vector<Core::Symbol> symbols;
    bool subscribe{true};  // true for subscribe, false for unsubscribe
};

struct SubscriptionResponse {
    std::vector<Core::Symbol> successful_symbols;
    std::vector<std::pair<Core::Symbol, std::string>> failed_symbols;
    bool success{true};
};

struct ProviderStatusResponse {
    struct ProviderInfo {
        std::string name;
        Providers::ProviderType type;
        Providers::ConnectionStatus status;
        std::size_t quote_count;
        std::size_t error_count;
        std::chrono::milliseconds average_latency;
        bool is_connected;
    };
    
    std::vector<ProviderInfo> providers;
    Providers::MarketDataStats overall_stats;
};

// 市场数据服务
class MarketDataService : public IDataService {
public:
    explicit MarketDataService(MarketDataServiceConfig config = {});
    ~MarketDataService() override;
    
    // IDataService interface
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    bool is_running() const noexcept override;
    
    void subscribe(EventType type, EventHandler handler) override;
    void unsubscribe(EventType type) override;
    void publish_event(const Event& event) override;
    
    Core::Result<bool> health_check() override;
    
    // 市场数据服务接口
    
    // 提供者管理
    Core::Result<void> add_ctp_provider(std::shared_ptr<Providers::CtpConfig> config);
    Core::Result<void> add_webdata_provider(std::shared_ptr<Providers::WebDataConfig> config);
    Core::Result<void> remove_provider(Providers::ProviderType type);
    Core::Result<void> remove_provider(const std::string& name);
    
    std::vector<Providers::IMarketDataProvider*> get_providers() const;
    Providers::IMarketDataProvider* get_provider(Providers::ProviderType type) const;
    Providers::IMarketDataProvider* get_provider(const std::string& name) const;
    
    // 订阅管理
    Core::Result<void> subscribe_quotes(const std::vector<Core::Symbol>& symbols);
    Core::Result<void> unsubscribe_quotes(const std::vector<Core::Symbol>& symbols);
    Core::Result<void> unsubscribe_all();
    
    std::vector<Core::Symbol> get_subscribed_symbols() const;
    
    // 数据查询
    Core::Result<Core::QuoteData> get_quote(const Core::Symbol& symbol);
    Core::Result<std::vector<Core::QuoteData>> get_quotes(const std::vector<Core::Symbol>& symbols);
    
    // 异步数据查询
    std::future<Core::Result<Core::QuoteData>> get_quote_async(const Core::Symbol& symbol);
    std::future<Core::Result<std::vector<Core::QuoteData>>> get_quotes_async(
        const std::vector<Core::Symbol>& symbols);
    
    // 历史数据查询
    Core::Result<std::vector<Core::BarData>> get_history_bars(
        const Core::Symbol& symbol,
        Core::BarSize bar_size,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time);
    
    // 配置管理
    Core::Result<void> set_routing_strategy(Providers::RoutingStrategy strategy);
    Providers::RoutingStrategy get_routing_strategy() const;
    
    Core::Result<void> update_provider_priority(Providers::ProviderType type, int priority);
    std::vector<Providers::ProviderPriority> get_provider_priorities() const;
    
    // 统计和监控
    Providers::MarketDataStats get_statistics() const;
    void reset_statistics();
    
    ProviderStatusResponse get_provider_status() const;
    
    // 缓存管理
    Core::Result<void> clear_cache();
    Core::Result<void> clear_cache(const Core::Symbol& symbol);
    std::size_t get_cache_size() const;
    
    // 回调注册
    using QuoteUpdateCallback = std::function<void(const Core::QuoteData&)>;
    using ProviderStatusCallback = std::function<void(Providers::ProviderType, Providers::ConnectionStatus)>;
    using ServiceErrorCallback = std::function<void(const std::string&)>;
    
    void set_quote_callback(QuoteUpdateCallback callback);
    void set_provider_status_callback(ProviderStatusCallback callback);
    void set_error_callback(ServiceErrorCallback callback);
    
    // REST API接口（如果启用）
    Core::Result<QuoteResponse> handle_quote_request(const QuoteRequest& request);
    Core::Result<SubscriptionResponse> handle_subscription_request(const SubscriptionRequest& request);
    Core::Result<ProviderStatusResponse> handle_status_request();
    
    // 配置热重载
    Core::Result<void> reload_config(const MarketDataServiceConfig& new_config);
    MarketDataServiceConfig get_config() const;

private:
    // 配置
    MarketDataServiceConfig config_;
    
    // 核心组件
    std::unique_ptr<Providers::MarketDataManager> market_data_manager_;
    std::unique_ptr<Providers::IProviderFactory> provider_factory_;
    
    // REST API服务器（如果启用）
    class RestApiServer;
    std::unique_ptr<RestApiServer> rest_api_server_;
    
    // 事件处理
    std::unordered_map<EventType, EventHandler> event_handlers_;
    mutable std::shared_mutex handlers_mutex_;
    
    // 回调函数
    QuoteUpdateCallback quote_callback_;
    ProviderStatusCallback provider_status_callback_;
    ServiceErrorCallback error_callback_;
    
    // 运行状态
    std::atomic<bool> running_{false};
    
    // 内部方法
    Core::Result<void> initialize_providers();
    Core::Result<void> initialize_rest_api();
    Core::Result<void> shutdown_rest_api();
    
    void setup_manager_callbacks();
    void cleanup_manager_callbacks();
    
    void handle_quote_update(const Core::QuoteData& quote);
    void handle_provider_status_change(Providers::ProviderType type, Providers::ConnectionStatus status);
    void handle_service_error(const std::string& error);
    
    // 事件发布
    void publish_quote_event(const Core::QuoteData& quote);
    void publish_provider_status_event(Providers::ProviderType type, Providers::ConnectionStatus status);
    void publish_error_event(const std::string& error);
    
    // 配置验证
    Core::Result<void> validate_config(const MarketDataServiceConfig& config) const;
    
    // 指标收集
    void collect_metrics();
    
    // 日志记录
    void log_service_event(const std::string& event, const std::string& details = "") const;
};

// 市场数据服务工厂
class MarketDataServiceFactory {
public:
    static std::unique_ptr<MarketDataService> create(MarketDataServiceConfig config = {});
    
    static std::unique_ptr<MarketDataService> create_with_ctp(
        std::shared_ptr<Providers::CtpConfig> ctp_config,
        MarketDataServiceConfig service_config = {});
    
    static std::unique_ptr<MarketDataService> create_with_webdata(
        std::shared_ptr<Providers::WebDataConfig> webdata_config,
        MarketDataServiceConfig service_config = {});
    
    static std::unique_ptr<MarketDataService> create_with_both(
        std::shared_ptr<Providers::CtpConfig> ctp_config,
        std::shared_ptr<Providers::WebDataConfig> webdata_config,
        MarketDataServiceConfig service_config = {});
};

// 便利函数
std::unique_ptr<MarketDataService> create_market_data_service(
    const std::string& ctp_front_address = "",
    const std::string& ctp_broker_id = "",
    const std::string& ctp_user_id = "",
    const std::string& ctp_password = "",
    Providers::WebDataSource web_source = Providers::WebDataSource::Sina);

} // namespace DataHub::Services
