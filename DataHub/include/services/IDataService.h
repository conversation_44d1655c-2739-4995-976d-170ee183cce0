#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../core/SecurityInfo.h"
#include "../data/IDataRepository.h"
#include <functional>
#include <memory>

namespace DataHub::Services {

// 事件类型
enum class EventType : std::uint8_t {
    QuoteUpdate = 0,
    BarUpdate = 1,
    TickUpdate = 2,
    SecurityUpdate = 3,
    BlockUpdate = 4,
    SystemEvent = 5
};

// 事件数据
struct Event {
    EventType type;
    Core::Timestamp timestamp;
    std::string source;
    
    // 使用 variant 存储不同类型的事件数据
    std::variant<
        Core::QuoteData,
        Core::BarData,
        Core::TickData,
        Core::SecurityInfo,
        Core::BlockInfo,
        std::string  // 系统事件消息
    > data;
    
    Event(EventType t, std::string src) 
        : type(t), timestamp(std::chrono::system_clock::now()), source(std::move(src)) {}
    
    template<typename T>
    Event(EventType t, std::string src, T&& event_data)
        : type(t), timestamp(std::chrono::system_clock::now()), 
          source(std::move(src)), data(std::forward<T>(event_data)) {}
};

// 事件处理器类型
using EventHandler = std::function<void(const Event&)>;

// 数据服务基础接口
class IDataService {
public:
    virtual ~IDataService() = default;
    
    // 服务生命周期
    virtual Core::Result<void> start() = 0;
    virtual Core::Result<void> stop() = 0;
    virtual bool is_running() const noexcept = 0;
    
    // 事件订阅
    virtual void subscribe(EventType type, EventHandler handler) = 0;
    virtual void unsubscribe(EventType type) = 0;
    virtual void publish_event(const Event& event) = 0;
    
    // 健康检查
    virtual Core::Result<bool> health_check() = 0;
};

// 行情服务接口
class IQuoteService : public virtual IDataService {
public:
    // 订阅行情
    virtual Core::Result<void> subscribe_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> subscribe_quotes(Core::DataSpanConst<Core::Symbol> symbols) = 0;
    virtual Core::Result<void> unsubscribe_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> unsubscribe_all_quotes() = 0;
    
    // 获取行情数据
    virtual Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::QuoteDataVector> get_quotes(const Data::QueryCondition& condition) = 0;
    
    // 异步接口
    virtual Data::AsyncTask<Core::QuoteData> get_latest_quote_async(const Core::Symbol& symbol) = 0;
    virtual Data::AsyncTask<Core::QuoteDataVector> get_quotes_async(const Data::QueryCondition& condition) = 0;
    
    // 协程接口
    virtual Data::CoroTask<Core::QuoteData> get_latest_quote_coro(const Core::Symbol& symbol) = 0;
    virtual Data::CoroTask<Core::QuoteDataVector> get_quotes_coro(const Data::QueryCondition& condition) = 0;
    
    // 数据推送
    virtual Core::Result<void> push_quote(const Core::QuoteData& quote) = 0;
    virtual Core::Result<void> push_quotes(Core::QuoteDataSpanConst quotes) = 0;
    
    // 获取订阅列表
    virtual Core::DataVector<Core::Symbol> get_subscribed_symbols() const = 0;
    
    // 数据质量检查
    virtual Core::Result<bool> validate_quote(const Core::QuoteData& quote) = 0;
};

// 历史数据服务接口
class IHistoryService : public virtual IDataService {
public:
    // K线数据
    virtual Core::Result<Core::BarDataVector> get_bars(const Data::QueryCondition& condition) = 0;
    virtual Core::Result<Core::BarData> get_latest_bar(const Core::Symbol& symbol, 
                                                       Core::BarSize bar_size, 
                                                       Core::BarType bar_type) = 0;
    
    // Tick数据
    virtual Core::Result<Core::TickDataVector> get_ticks(const Data::QueryCondition& condition) = 0;
    
    // 数据聚合
    virtual Core::Result<Core::BarDataVector> aggregate_bars(const Core::Symbol& symbol,
                                                             Core::BarSize from_size,
                                                             Core::BarSize to_size,
                                                             const Data::QueryCondition& condition) = 0;
    
    // 异步接口
    virtual Data::AsyncTask<Core::BarDataVector> get_bars_async(const Data::QueryCondition& condition) = 0;
    virtual Data::AsyncTask<Core::TickDataVector> get_ticks_async(const Data::QueryCondition& condition) = 0;
    
    // 协程接口
    virtual Data::CoroTask<Core::BarDataVector> get_bars_coro(const Data::QueryCondition& condition) = 0;
    virtual Data::CoroTask<Core::TickDataVector> get_ticks_coro(const Data::QueryCondition& condition) = 0;
    
    // 数据导入导出
    virtual Core::Result<void> import_bars(const std::string& file_path, 
                                           const Core::Symbol& symbol,
                                           Core::BarSize bar_size) = 0;
    virtual Core::Result<void> export_bars(const std::string& file_path,
                                           const Data::QueryCondition& condition) = 0;
    
    // 数据统计
    virtual Core::Result<std::size_t> count_bars(const Data::QueryCondition& condition) = 0;
    virtual Core::Result<std::size_t> count_ticks(const Data::QueryCondition& condition) = 0;
    
    // 数据完整性检查
    virtual Core::Result<bool> check_data_integrity(const Core::Symbol& symbol,
                                                    Core::BarSize bar_size,
                                                    const Data::QueryCondition& condition) = 0;
};

// 证券信息服务接口
class ISecurityService : public virtual IDataService {
public:
    // 证券信息
    virtual Core::Result<Core::SecurityInfo> get_security(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::SecurityInfoVector> get_securities(const Data::QueryCondition& condition) = 0;
    virtual Core::Result<Core::SecurityInfoVector> search_securities(const std::string& keyword) = 0;
    
    // 板块信息
    virtual Core::Result<Core::BlockInfo> get_block(const std::string& name) = 0;
    virtual Core::Result<Core::BlockInfoVector> get_blocks() = 0;
    virtual Core::Result<Core::BlockInfoVector> get_blocks_by_type(Core::BlockInfo::BlockType type) = 0;
    
    // 证券分类
    virtual Core::Result<Core::SecurityInfoVector> get_securities_by_exchange(Core::Exchange exchange) = 0;
    virtual Core::Result<Core::SecurityInfoVector> get_securities_by_market_type(Core::MarketType market_type) = 0;
    virtual Core::Result<Core::SecurityInfoVector> get_securities_by_block(const std::string& block_name) = 0;
    
    // 主力合约
    virtual Core::Result<Core::Symbol> get_main_contract(const std::string& underlying) = 0;
    virtual Core::Result<Core::SecurityInfoVector> get_main_contracts() = 0;
    
    // 异步接口
    virtual Data::AsyncTask<Core::SecurityInfo> get_security_async(const Core::Symbol& symbol) = 0;
    virtual Data::AsyncTask<Core::SecurityInfoVector> search_securities_async(const std::string& keyword) = 0;
    
    // 协程接口
    virtual Data::CoroTask<Core::SecurityInfo> get_security_coro(const Core::Symbol& symbol) = 0;
    virtual Data::CoroTask<Core::SecurityInfoVector> search_securities_coro(const std::string& keyword) = 0;
    
    // 数据更新
    virtual Core::Result<void> update_security(const Core::SecurityInfo& security) = 0;
    virtual Core::Result<void> update_block(const Core::BlockInfo& block) = 0;
    
    // 数据同步
    virtual Core::Result<void> sync_securities_from_exchange(Core::Exchange exchange) = 0;
    virtual Core::Result<void> sync_all_securities() = 0;
};

// 指标计算服务接口
class IIndicatorService : public virtual IDataService {
public:
    // 技术指标计算
    virtual Core::Result<Core::DataVector<double>> calculate_sma(const Core::BarDataVector& bars, 
                                                                int period) = 0;
    virtual Core::Result<Core::DataVector<double>> calculate_ema(const Core::BarDataVector& bars, 
                                                                int period) = 0;
    virtual Core::Result<Core::DataVector<double>> calculate_rsi(const Core::BarDataVector& bars, 
                                                                int period) = 0;
    virtual Core::Result<Core::DataVector<double>> calculate_macd(const Core::BarDataVector& bars,
                                                                 int fast_period, 
                                                                 int slow_period, 
                                                                 int signal_period) = 0;
    
    // 统计指标
    virtual Core::Result<double> calculate_volatility(const Core::BarDataVector& bars, 
                                                     int period) = 0;
    virtual Core::Result<double> calculate_correlation(const Core::BarDataVector& bars1,
                                                      const Core::BarDataVector& bars2) = 0;
    
    // 自定义指标
    using IndicatorFunction = std::function<Core::Result<Core::DataVector<double>>(const Core::BarDataVector&)>;
    virtual Core::Result<void> register_indicator(const std::string& name, 
                                                  IndicatorFunction function) = 0;
    virtual Core::Result<Core::DataVector<double>> calculate_custom_indicator(const std::string& name,
                                                                             const Core::BarDataVector& bars) = 0;
    
    // 异步计算
    virtual Data::AsyncTask<Core::DataVector<double>> calculate_sma_async(const Core::BarDataVector& bars, 
                                                                         int period) = 0;
    virtual Data::AsyncTask<Core::DataVector<double>> calculate_ema_async(const Core::BarDataVector& bars, 
                                                                         int period) = 0;
    
    // 批量计算
    virtual Core::Result<std::unordered_map<std::string, Core::DataVector<double>>> 
        calculate_multiple_indicators(const Core::BarDataVector& bars,
                                     const std::vector<std::string>& indicator_names) = 0;
};

// 数据服务工厂接口
class IDataServiceFactory {
public:
    virtual ~IDataServiceFactory() = default;
    
    virtual Core::UniquePtr<IQuoteService> create_quote_service() = 0;
    virtual Core::UniquePtr<IHistoryService> create_history_service() = 0;
    virtual Core::UniquePtr<ISecurityService> create_security_service() = 0;
    virtual Core::UniquePtr<IIndicatorService> create_indicator_service() = 0;
};

} // namespace DataHub::Services
