#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include <concurrentqueue.h>
#include <memory>
#include <thread>
#include <atomic>
#include <vector>
#include <functional>
#include <chrono>

namespace DataHub::Providers {

// 高性能数据处理器配置
struct DataProcessorConfig {
    std::size_t worker_thread_count{4};        // 工作线程数量
    std::size_t batch_size{100};               // 批处理大小
    std::size_t queue_capacity{10000};         // 队列容量
    std::chrono::microseconds spin_wait_time{100}; // 自旋等待时间
    bool enable_affinity{false};               // 启用CPU亲和性
    std::vector<int> cpu_cores;                // 指定CPU核心
};

// 数据处理任务类型
enum class DataTaskType : std::uint8_t {
    Quote = 0,
    Tick = 1,
    Bar = 2,
    Custom = 3
};

// 数据处理任务
struct DataTask {
    DataTaskType type;
    std::chrono::high_resolution_clock::time_point timestamp;
    std::shared_ptr<void> data;  // 使用void*存储不同类型的数据
    std::function<void()> processor;  // 处理函数
    
    DataTask() = default;
    
    template<typename T>
    DataTask(DataTaskType t, std::shared_ptr<T> d, std::function<void(const T&)> proc)
        : type(t), timestamp(std::chrono::high_resolution_clock::now()), data(d) {
        processor = [d, proc]() { proc(*d); };
    }
};

// 高性能数据处理器
class HighPerformanceDataProcessor {
public:
    explicit HighPerformanceDataProcessor(DataProcessorConfig config = {});
    ~HighPerformanceDataProcessor();
    
    // 禁用拷贝和移动
    HighPerformanceDataProcessor(const HighPerformanceDataProcessor&) = delete;
    HighPerformanceDataProcessor& operator=(const HighPerformanceDataProcessor&) = delete;
    HighPerformanceDataProcessor(HighPerformanceDataProcessor&&) = delete;
    HighPerformanceDataProcessor& operator=(HighPerformanceDataProcessor&&) = delete;
    
    // 启动和停止
    bool start();
    void stop();
    bool is_running() const noexcept { return running_.load(std::memory_order_acquire); }
    
    // 提交处理任务
    template<typename T>
    bool submit_task(DataTaskType type, std::shared_ptr<T> data, 
                    std::function<void(const T&)> processor) {
        if (!running_.load(std::memory_order_acquire)) {
            return false;
        }
        
        DataTask task(type, data, processor);
        return task_queue_.enqueue(std::move(task));
    }
    
    // 批量提交任务
    template<typename T>
    bool submit_batch_tasks(DataTaskType type, 
                           const std::vector<std::shared_ptr<T>>& data_batch,
                           std::function<void(const T&)> processor) {
        if (!running_.load(std::memory_order_acquire)) {
            return false;
        }
        
        std::vector<DataTask> tasks;
        tasks.reserve(data_batch.size());
        
        for (const auto& data : data_batch) {
            tasks.emplace_back(type, data, processor);
        }
        
        return task_queue_.enqueue_bulk(tasks.begin(), tasks.size()) == tasks.size();
    }
    
    // 获取统计信息
    struct Statistics {
        std::atomic<std::uint64_t> total_tasks{0};
        std::atomic<std::uint64_t> processed_tasks{0};
        std::atomic<std::uint64_t> failed_tasks{0};
        std::atomic<std::uint64_t> queue_full_count{0};
        std::atomic<std::uint64_t> total_processing_time_ns{0};
        std::atomic<std::uint64_t> max_processing_time_ns{0};
        std::atomic<std::uint64_t> min_processing_time_ns{UINT64_MAX};
        
        double get_average_processing_time_ns() const {
            auto processed = processed_tasks.load();
            if (processed == 0) return 0.0;
            return static_cast<double>(total_processing_time_ns.load()) / processed;
        }
        
        double get_success_rate() const {
            auto total = total_tasks.load();
            if (total == 0) return 0.0;
            return static_cast<double>(processed_tasks.load()) / total;
        }
    };
    
    const Statistics& get_statistics() const noexcept { return stats_; }
    void reset_statistics() noexcept;
    
    // 获取队列状态
    std::size_t get_queue_size() const noexcept {
        return task_queue_.size_approx();
    }
    
    bool is_queue_empty() const noexcept {
        return task_queue_.size_approx() == 0;
    }
    
    // 设置错误回调
    void set_error_callback(std::function<void(const std::string&)> callback) {
        error_callback_ = std::move(callback);
    }

private:
    DataProcessorConfig config_;
    
    // 无锁队列
    moodycamel::ConcurrentQueue<DataTask> task_queue_;
    
    // 工作线程
    std::vector<std::thread> worker_threads_;
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};
    
    // 统计信息
    mutable Statistics stats_;
    
    // 错误回调
    std::function<void(const std::string&)> error_callback_;
    
    // 内部方法
    void worker_thread_function(int thread_id);
    void process_task_batch(DataTask* tasks, std::size_t count, int thread_id);
    void set_thread_affinity(int thread_id);
    void handle_error(const std::string& error);
    void update_processing_time_stats(std::uint64_t processing_time_ns);
};

// 特化的行情数据处理器
class QuoteDataProcessor {
public:
    using QuoteHandler = std::function<void(const Core::QuoteData&)>;
    using TickHandler = std::function<void(const Core::TickData&)>;
    using BarHandler = std::function<void(const Core::BarData&)>;
    
    explicit QuoteDataProcessor(DataProcessorConfig config = {});
    ~QuoteDataProcessor() = default;
    
    bool start() { return processor_.start(); }
    void stop() { processor_.stop(); }
    bool is_running() const noexcept { return processor_.is_running(); }
    
    // 设置处理器
    void set_quote_handler(QuoteHandler handler) { quote_handler_ = std::move(handler); }
    void set_tick_handler(TickHandler handler) { tick_handler_ = std::move(handler); }
    void set_bar_handler(BarHandler handler) { bar_handler_ = std::move(handler); }
    
    // 提交数据
    bool submit_quote(std::shared_ptr<Core::QuoteData> quote) {
        if (!quote_handler_) return false;
        return processor_.submit_task(DataTaskType::Quote, quote, quote_handler_);
    }
    
    bool submit_tick(std::shared_ptr<Core::TickData> tick) {
        if (!tick_handler_) return false;
        return processor_.submit_task(DataTaskType::Tick, tick, tick_handler_);
    }
    
    bool submit_bar(std::shared_ptr<Core::BarData> bar) {
        if (!bar_handler_) return false;
        return processor_.submit_task(DataTaskType::Bar, bar, bar_handler_);
    }
    
    // 批量提交
    bool submit_quotes(const std::vector<std::shared_ptr<Core::QuoteData>>& quotes) {
        if (!quote_handler_) return false;
        return processor_.submit_batch_tasks(DataTaskType::Quote, quotes, quote_handler_);
    }
    
    // 获取统计信息
    const HighPerformanceDataProcessor::Statistics& get_statistics() const noexcept {
        return processor_.get_statistics();
    }
    
    std::size_t get_queue_size() const noexcept {
        return processor_.get_queue_size();
    }

private:
    HighPerformanceDataProcessor processor_;
    QuoteHandler quote_handler_;
    TickHandler tick_handler_;
    BarHandler bar_handler_;
};

// 便利函数
namespace Utils {
    // 创建高性能处理器
    std::unique_ptr<HighPerformanceDataProcessor> create_high_performance_processor(
        std::size_t worker_threads = std::thread::hardware_concurrency(),
        std::size_t batch_size = 100,
        std::size_t queue_capacity = 10000);
    
    // 创建行情数据处理器
    std::unique_ptr<QuoteDataProcessor> create_quote_processor(
        std::size_t worker_threads = std::thread::hardware_concurrency());
    
    // 获取最优配置
    DataProcessorConfig get_optimal_config();
    
    // CPU亲和性设置
    bool set_cpu_affinity(std::thread& thread, int cpu_core);
    std::vector<int> get_available_cpu_cores();
}

} // namespace DataHub::Providers
