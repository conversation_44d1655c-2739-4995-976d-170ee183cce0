#pragma once

#include "IMarketDataProvider.h"
#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../services/IDataService.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <functional>
#include <future>
#include <thread>
#include <atomic>
#include <shared_mutex>

namespace DataHub::Providers {

// 路由策略
enum class RoutingStrategy : std::uint8_t {
    Primary = 0,        // 使用主要提供者
    Failover = 1,       // 故障转移
    LoadBalance = 2,    // 负载均衡
    Broadcast = 3       // 广播到所有提供者
};

// 提供者优先级
struct ProviderPriority {
    ProviderType type;
    int priority;           // 优先级（数字越小优先级越高）
    bool is_primary;        // 是否为主要提供者
    bool is_backup;         // 是否为备用提供者
};

// 市场数据管理器配置
struct MarketDataManagerConfig {
    RoutingStrategy routing_strategy{RoutingStrategy::Primary};
    std::vector<ProviderPriority> provider_priorities;
    bool enable_failover{true};
    std::chrono::seconds failover_timeout{30};
    bool enable_data_validation{true};
    bool enable_duplicate_detection{true};
    std::chrono::milliseconds duplicate_window{1000};
    std::size_t max_cache_size{10000};
    std::chrono::minutes cache_ttl{5};
};

// 数据路由信息
struct DataRoute {
    Core::Symbol symbol;
    ProviderType provider_type;
    std::string provider_name;
    bool is_primary;
    std::chrono::system_clock::time_point last_update;
};

// 市场数据统计
struct MarketDataStats {
    std::size_t total_quotes{0};
    std::size_t total_errors{0};
    std::size_t cache_hits{0};
    std::size_t cache_misses{0};
    std::chrono::milliseconds average_latency{0};
    std::unordered_map<ProviderType, std::size_t> provider_quotes;
    std::unordered_map<ProviderType, std::size_t> provider_errors;
};

// 市场数据管理器
class MarketDataManager : public Services::IDataService {
public:
    explicit MarketDataManager(MarketDataManagerConfig config = {});
    ~MarketDataManager() override;
    
    // IDataService interface
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    bool is_running() const noexcept override;
    
    void subscribe(Services::EventType type, Services::EventHandler handler) override;
    void unsubscribe(Services::EventType type) override;
    void publish_event(const Services::Event& event) override;
    
    Core::Result<bool> health_check() override;
    
    // 提供者管理
    Core::Result<void> add_provider(std::unique_ptr<IMarketDataProvider> provider, 
                                   ProviderPriority priority = {});
    Core::Result<void> remove_provider(ProviderType type);
    Core::Result<void> remove_provider(const std::string& name);
    
    std::vector<IMarketDataProvider*> get_providers() const;
    IMarketDataProvider* get_provider(ProviderType type) const;
    IMarketDataProvider* get_provider(const std::string& name) const;
    
    // 订阅管理
    Core::Result<void> subscribe_quote(const Core::Symbol& symbol);
    Core::Result<void> subscribe_quotes(const std::vector<Core::Symbol>& symbols);
    Core::Result<void> unsubscribe_quote(const Core::Symbol& symbol);
    Core::Result<void> unsubscribe_all();
    
    // 数据查询
    Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol);
    Core::Result<std::vector<Core::QuoteData>> get_quotes(
        const std::vector<Core::Symbol>& symbols);
    
    // 异步数据查询
    std::future<Core::Result<Core::QuoteData>> get_latest_quote_async(
        const Core::Symbol& symbol);
    std::future<Core::Result<std::vector<Core::QuoteData>>> get_quotes_async(
        const std::vector<Core::Symbol>& symbols);
    
    // 路由管理
    Core::Result<void> set_symbol_route(const Core::Symbol& symbol, ProviderType provider);
    Core::Result<void> remove_symbol_route(const Core::Symbol& symbol);
    std::optional<DataRoute> get_symbol_route(const Core::Symbol& symbol) const;
    
    // 配置管理
    Core::Result<void> set_routing_strategy(RoutingStrategy strategy);
    RoutingStrategy get_routing_strategy() const noexcept;
    
    Core::Result<void> update_provider_priority(ProviderType type, int priority);
    std::vector<ProviderPriority> get_provider_priorities() const;
    
    // 统计信息
    MarketDataStats get_statistics() const;
    void reset_statistics();
    
    // 缓存管理
    Core::Result<void> clear_cache();
    Core::Result<void> clear_cache(const Core::Symbol& symbol);
    std::size_t get_cache_size() const;
    
    // 回调注册
    using QuoteUpdateCallback = std::function<void(const Core::QuoteData&)>;
    using ProviderStatusCallback = std::function<void(ProviderType, ConnectionStatus)>;
    using ErrorCallback = std::function<void(const std::string&)>;
    
    void set_quote_callback(QuoteUpdateCallback callback);
    void set_provider_status_callback(ProviderStatusCallback callback);
    void set_error_callback(ErrorCallback callback);

private:
    // 配置
    MarketDataManagerConfig config_;
    
    // 提供者管理
    std::vector<std::unique_ptr<IMarketDataProvider>> providers_;
    std::unordered_map<ProviderType, IMarketDataProvider*> provider_by_type_;
    std::unordered_map<std::string, IMarketDataProvider*> provider_by_name_;
    std::vector<ProviderPriority> provider_priorities_;
    mutable std::shared_mutex providers_mutex_;
    
    // 路由管理
    std::unordered_map<Core::Symbol, DataRoute> symbol_routes_;
    mutable std::shared_mutex routes_mutex_;
    
    // 订阅管理
    std::unordered_set<Core::Symbol> subscribed_symbols_;
    mutable std::shared_mutex subscriptions_mutex_;
    
    // 缓存管理
    struct CachedQuote {
        Core::QuoteData quote;
        std::chrono::system_clock::time_point timestamp;
        ProviderType source_provider;
    };
    std::unordered_map<Core::Symbol, CachedQuote> quote_cache_;
    mutable std::shared_mutex cache_mutex_;
    
    // 重复检测
    struct QuoteSignature {
        Core::Symbol symbol;
        Core::Price last_price;
        Core::Timestamp timestamp;
        
        bool operator==(const QuoteSignature& other) const {
            return symbol == other.symbol && 
                   std::abs(last_price - other.last_price) < 0.0001 &&
                   std::abs(std::chrono::duration_cast<std::chrono::milliseconds>(
                       timestamp - other.timestamp).count()) < 100;
        }
    };
    std::unordered_map<Core::Symbol, QuoteSignature> last_quotes_;
    mutable std::shared_mutex duplicates_mutex_;
    
    // 统计信息
    mutable MarketDataStats stats_;
    mutable std::shared_mutex stats_mutex_;
    
    // 回调函数
    QuoteUpdateCallback quote_callback_;
    ProviderStatusCallback provider_status_callback_;
    ErrorCallback error_callback_;
    
    // 事件处理
    std::unordered_map<Services::EventType, Services::EventHandler> event_handlers_;
    mutable std::shared_mutex handlers_mutex_;
    
    // 运行状态
    std::atomic<bool> running_{false};
    
    // 内部方法
    IMarketDataProvider* select_provider_for_symbol(const Core::Symbol& symbol) const;
    std::vector<IMarketDataProvider*> get_providers_by_priority() const;
    
    bool is_cache_valid(const CachedQuote& cached) const;
    void update_cache(const Core::Symbol& symbol, const Core::QuoteData& quote, 
                     ProviderType provider);
    std::optional<Core::QuoteData> get_from_cache(const Core::Symbol& symbol) const;
    
    bool is_duplicate_quote(const Core::QuoteData& quote) const;
    void update_duplicate_detection(const Core::QuoteData& quote);
    
    bool validate_quote_data(const Core::QuoteData& quote) const;
    
    void handle_provider_quote(const Core::QuoteData& quote, ProviderType provider_type);
    void handle_provider_error(const std::string& error, ProviderType provider_type);
    void handle_provider_status_change(ProviderType provider_type, ConnectionStatus status);
    
    void update_statistics(const Core::QuoteData& quote, ProviderType provider_type);
    void update_error_statistics(ProviderType provider_type);
    
    void setup_provider_callbacks(IMarketDataProvider* provider);
    void cleanup_provider_callbacks(IMarketDataProvider* provider);
    
    // 故障转移
    void handle_provider_failure(ProviderType failed_provider);
    IMarketDataProvider* find_backup_provider(ProviderType failed_provider) const;
    
    // 负载均衡
    IMarketDataProvider* select_provider_round_robin() const;
    mutable std::atomic<std::size_t> round_robin_index_{0};
};

// 市场数据管理器工厂
class MarketDataManagerFactory {
public:
    static std::unique_ptr<MarketDataManager> create(
        MarketDataManagerConfig config = {});
    
    static std::unique_ptr<MarketDataManager> create_with_providers(
        std::vector<std::unique_ptr<IMarketDataProvider>> providers,
        MarketDataManagerConfig config = {});
};

} // namespace DataHub::Providers
