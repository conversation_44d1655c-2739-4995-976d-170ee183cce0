#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../services/IDataService.h"
#include <memory>
#include <functional>
#include <future>
#include <vector>
#include <chrono>

namespace DataHub::Providers {

// 市场数据提供者类型
enum class ProviderType : std::uint8_t {
    CTP_Futures = 0,    // CTP期货行情
    WebData_Stock = 1,  // 网络股票行情
    XTP_Stock = 2,      // XTP股票行情
    IB_Global = 3,      // IB全球行情
    Simulation = 4      // 模拟行情
};

// 连接状态
enum class ConnectionStatus : std::uint8_t {
    Disconnected = 0,
    Connecting = 1,
    Connected = 2,
    Reconnecting = 3,
    Error = 4
};

// 订阅状态
enum class SubscriptionStatus : std::uint8_t {
    Unsubscribed = 0,
    Subscribing = 1,
    Subscribed = 2,
    Failed = 3
};

// 提供者配置基类
struct ProviderConfig {
    std::string name;
    ProviderType type;
    bool auto_reconnect{true};
    std::chrono::seconds reconnect_interval{30};
    std::chrono::seconds heartbeat_interval{30};
    std::size_t max_retry_count{3};
    bool enable_logging{true};
    
    virtual ~ProviderConfig() = default;
};

// 连接信息
struct ConnectionInfo {
    ConnectionStatus status{ConnectionStatus::Disconnected};
    std::string server_address;
    std::chrono::system_clock::time_point connect_time;
    std::chrono::system_clock::time_point last_heartbeat;
    std::size_t retry_count{0};
    std::string error_message;
};

// 订阅信息
struct SubscriptionInfo {
    Core::Symbol symbol;
    SubscriptionStatus status{SubscriptionStatus::Unsubscribed};
    std::chrono::system_clock::time_point subscribe_time;
    std::string error_message;
};

// 数据回调函数类型
using QuoteCallback = std::function<void(const Core::QuoteData&)>;
using BarCallback = std::function<void(const Core::BarData&)>;
using TickCallback = std::function<void(const Core::TickData&)>;
using ErrorCallback = std::function<void(const std::string& error)>;
using StatusCallback = std::function<void(ConnectionStatus status)>;

// 市场数据提供者接口
class IMarketDataProvider {
public:
    virtual ~IMarketDataProvider() = default;
    
    // 生命周期管理
    virtual Core::Result<void> initialize(std::shared_ptr<ProviderConfig> config) = 0;
    virtual Core::Result<void> start() = 0;
    virtual Core::Result<void> stop() = 0;
    virtual Core::Result<void> shutdown() = 0;
    
    // 连接管理
    virtual Core::Result<void> connect() = 0;
    virtual Core::Result<void> disconnect() = 0;
    virtual bool is_connected() const noexcept = 0;
    virtual ConnectionInfo get_connection_info() const = 0;
    
    // 订阅管理
    virtual Core::Result<void> subscribe_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> subscribe_quotes(const std::vector<Core::Symbol>& symbols) = 0;
    virtual Core::Result<void> unsubscribe_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> unsubscribe_all() = 0;
    
    // 获取订阅状态
    virtual std::vector<SubscriptionInfo> get_subscriptions() const = 0;
    virtual SubscriptionInfo get_subscription_info(const Core::Symbol& symbol) const = 0;
    
    // 数据查询（同步）
    virtual Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<std::vector<Core::QuoteData>> get_quotes(
        const std::vector<Core::Symbol>& symbols) = 0;
    
    // 数据查询（异步）
    virtual std::future<Core::Result<Core::QuoteData>> get_latest_quote_async(
        const Core::Symbol& symbol) = 0;
    virtual std::future<Core::Result<std::vector<Core::QuoteData>>> get_quotes_async(
        const std::vector<Core::Symbol>& symbols) = 0;
    
    // 回调注册
    virtual void set_quote_callback(QuoteCallback callback) = 0;
    virtual void set_bar_callback(BarCallback callback) = 0;
    virtual void set_tick_callback(TickCallback callback) = 0;
    virtual void set_error_callback(ErrorCallback callback) = 0;
    virtual void set_status_callback(StatusCallback callback) = 0;
    
    // 提供者信息
    virtual ProviderType get_type() const noexcept = 0;
    virtual std::string get_name() const noexcept = 0;
    virtual std::string get_version() const noexcept = 0;
    
    // 健康检查
    virtual Core::Result<bool> health_check() = 0;
    
    // 统计信息
    virtual std::size_t get_quote_count() const noexcept = 0;
    virtual std::size_t get_error_count() const noexcept = 0;
    virtual std::chrono::milliseconds get_average_latency() const noexcept = 0;
};

// 市场数据提供者基类
class MarketDataProviderBase : public IMarketDataProvider {
public:
    explicit MarketDataProviderBase(ProviderType type, std::string name);
    virtual ~MarketDataProviderBase() = default;
    
    // IMarketDataProvider interface
    Core::Result<void> initialize(std::shared_ptr<ProviderConfig> config) override;
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    Core::Result<void> shutdown() override;
    
    bool is_connected() const noexcept override;
    ConnectionInfo get_connection_info() const override;
    
    std::vector<SubscriptionInfo> get_subscriptions() const override;
    SubscriptionInfo get_subscription_info(const Core::Symbol& symbol) const override;
    
    void set_quote_callback(QuoteCallback callback) override;
    void set_bar_callback(BarCallback callback) override;
    void set_tick_callback(TickCallback callback) override;
    void set_error_callback(ErrorCallback callback) override;
    void set_status_callback(StatusCallback callback) override;
    
    ProviderType get_type() const noexcept override;
    std::string get_name() const noexcept override;
    std::string get_version() const noexcept override;
    
    std::size_t get_quote_count() const noexcept override;
    std::size_t get_error_count() const noexcept override;
    std::chrono::milliseconds get_average_latency() const noexcept override;

protected:
    // 子类需要实现的纯虚函数
    virtual Core::Result<void> do_connect() = 0;
    virtual Core::Result<void> do_disconnect() = 0;
    virtual Core::Result<void> do_subscribe(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> do_unsubscribe(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> do_start() = 0;
    virtual Core::Result<void> do_stop() = 0;
    
    // 辅助方法
    void update_connection_status(ConnectionStatus status, const std::string& error = "");
    void update_subscription_status(const Core::Symbol& symbol, SubscriptionStatus status, 
                                   const std::string& error = "");
    void notify_quote(const Core::QuoteData& quote);
    void notify_bar(const Core::BarData& bar);
    void notify_tick(const Core::TickData& tick);
    void notify_error(const std::string& error);
    
    // 统计更新
    void increment_quote_count();
    void increment_error_count();
    void update_latency(std::chrono::milliseconds latency);
    
    // 配置和状态
    std::shared_ptr<ProviderConfig> config_;
    ConnectionInfo connection_info_;
    std::unordered_map<Core::Symbol, SubscriptionInfo> subscriptions_;
    
    // 回调函数
    QuoteCallback quote_callback_;
    BarCallback bar_callback_;
    TickCallback tick_callback_;
    ErrorCallback error_callback_;
    StatusCallback status_callback_;
    
    // 统计信息
    std::atomic<std::size_t> quote_count_{0};
    std::atomic<std::size_t> error_count_{0};
    std::atomic<std::chrono::milliseconds> average_latency_{0};
    
    // 线程安全
    mutable std::shared_mutex mutex_;
    
private:
    ProviderType type_;
    std::string name_;
    std::atomic<bool> running_{false};
    std::atomic<bool> initialized_{false};
};

// 提供者工厂接口
class IProviderFactory {
public:
    virtual ~IProviderFactory() = default;
    
    virtual std::unique_ptr<IMarketDataProvider> create_provider(
        ProviderType type, 
        std::shared_ptr<ProviderConfig> config) = 0;
    
    virtual std::vector<ProviderType> get_supported_types() const = 0;
    virtual bool supports_type(ProviderType type) const = 0;
};

} // namespace DataHub::Providers
