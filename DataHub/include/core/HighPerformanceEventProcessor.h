#pragma once

#include "Types.h"
#include <concurrentqueue.h>
#include <memory>
#include <thread>
#include <atomic>
#include <vector>
#include <functional>
#include <chrono>
#include <type_traits>

namespace DataHub::Core {

// 通用事件处理器配置
struct EventProcessorConfig {
    std::size_t worker_thread_count{4};        // 工作线程数量
    std::size_t batch_size{50};                // 批处理大小
    std::size_t queue_capacity{10000};         // 队列容量
    std::chrono::microseconds spin_wait_time{100}; // 自旋等待时间
    bool enable_affinity{false};               // 启用CPU亲和性
    std::vector<int> cpu_cores;                // 指定CPU核心
    bool enable_statistics{true};              // 启用统计
};

// 事件处理器统计信息
struct EventProcessorStats {
    std::atomic<std::uint64_t> total_events{0};
    std::atomic<std::uint64_t> processed_events{0};
    std::atomic<std::uint64_t> failed_events{0};
    std::atomic<std::uint64_t> dropped_events{0};
    std::atomic<std::uint64_t> total_processing_time_ns{0};
    std::atomic<std::uint64_t> max_processing_time_ns{0};
    std::atomic<std::uint64_t> min_processing_time_ns{UINT64_MAX};
    
    double get_average_processing_time_ns() const {
        auto processed = processed_events.load();
        if (processed == 0) return 0.0;
        return static_cast<double>(total_processing_time_ns.load()) / processed;
    }
    
    double get_success_rate() const {
        auto total = total_events.load();
        if (total == 0) return 0.0;
        return static_cast<double>(processed_events.load()) / total;
    }
    
    void reset() {
        total_events.store(0);
        processed_events.store(0);
        failed_events.store(0);
        dropped_events.store(0);
        total_processing_time_ns.store(0);
        max_processing_time_ns.store(0);
        min_processing_time_ns.store(UINT64_MAX);
    }
};

// 通用高性能事件处理器
template<typename EventType>
class HighPerformanceEventProcessor {
public:
    using EventHandler = std::function<void(const EventType&)>;
    using ErrorHandler = std::function<void(const std::string&)>;
    
    explicit HighPerformanceEventProcessor(EventProcessorConfig config = {});
    ~HighPerformanceEventProcessor();
    
    // 禁用拷贝和移动
    HighPerformanceEventProcessor(const HighPerformanceEventProcessor&) = delete;
    HighPerformanceEventProcessor& operator=(const HighPerformanceEventProcessor&) = delete;
    HighPerformanceEventProcessor(HighPerformanceEventProcessor&&) = delete;
    HighPerformanceEventProcessor& operator=(HighPerformanceEventProcessor&&) = delete;
    
    // 启动和停止
    bool start();
    void stop();
    bool is_running() const noexcept { return running_.load(std::memory_order_acquire); }
    
    // 设置处理器
    void set_event_handler(EventHandler handler) { event_handler_ = std::move(handler); }
    void set_error_handler(ErrorHandler handler) { error_handler_ = std::move(handler); }
    
    // 提交事件
    bool submit_event(EventType event) {
        if (!running_.load(std::memory_order_acquire) || !event_handler_) {
            return false;
        }
        
        if (event_queue_.enqueue(std::move(event))) {
            has_events_.store(true, std::memory_order_release);
            if (config_.enable_statistics) {
                stats_.total_events.fetch_add(1, std::memory_order_relaxed);
            }
            return true;
        } else {
            if (config_.enable_statistics) {
                stats_.dropped_events.fetch_add(1, std::memory_order_relaxed);
            }
            return false;
        }
    }
    
    // 批量提交事件
    template<typename Iterator>
    std::size_t submit_events(Iterator begin, Iterator end) {
        if (!running_.load(std::memory_order_acquire) || !event_handler_) {
            return 0;
        }
        
        std::size_t count = std::distance(begin, end);
        std::size_t enqueued = event_queue_.enqueue_bulk(begin, count);
        
        if (enqueued > 0) {
            has_events_.store(true, std::memory_order_release);
            if (config_.enable_statistics) {
                stats_.total_events.fetch_add(enqueued, std::memory_order_relaxed);
                stats_.dropped_events.fetch_add(count - enqueued, std::memory_order_relaxed);
            }
        }
        
        return enqueued;
    }
    
    // 获取统计信息
    const EventProcessorStats& get_statistics() const noexcept { return stats_; }
    void reset_statistics() noexcept { stats_.reset(); }
    
    // 获取队列状态
    std::size_t get_queue_size() const noexcept {
        return event_queue_.size_approx();
    }
    
    bool is_queue_empty() const noexcept {
        return event_queue_.size_approx() == 0;
    }

private:
    EventProcessorConfig config_;
    
    // 无锁队列
    moodycamel::ConcurrentQueue<EventType> event_queue_;
    std::atomic<bool> has_events_{false};
    
    // 工作线程
    std::vector<std::thread> worker_threads_;
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};
    
    // 处理器
    EventHandler event_handler_;
    ErrorHandler error_handler_;
    
    // 统计信息
    mutable EventProcessorStats stats_;
    
    // 内部方法
    void worker_thread_function(int thread_id);
    void process_event_batch(EventType* events, std::size_t count, int thread_id);
    void set_thread_affinity(int thread_id);
    void handle_error(const std::string& error);
    void update_processing_time_stats(std::uint64_t processing_time_ns);
};

// 便利函数
namespace Utils {
    // 创建最优配置
    EventProcessorConfig get_optimal_event_processor_config();
    
    // 创建特定类型的事件处理器
    template<typename EventType>
    std::unique_ptr<HighPerformanceEventProcessor<EventType>> 
    create_event_processor(std::size_t worker_threads = std::thread::hardware_concurrency()) {
        EventProcessorConfig config;
        config.worker_thread_count = worker_threads;
        config.batch_size = 50;
        config.queue_capacity = 10000;
        
        return std::make_unique<HighPerformanceEventProcessor<EventType>>(config);
    }
}

// 模板实现
template<typename EventType>
HighPerformanceEventProcessor<EventType>::HighPerformanceEventProcessor(EventProcessorConfig config)
    : config_(std::move(config))
    , event_queue_(config_.queue_capacity) {
    
    // 验证配置
    if (config_.worker_thread_count == 0) {
        config_.worker_thread_count = std::thread::hardware_concurrency();
    }
    
    if (config_.batch_size == 0) {
        config_.batch_size = 50;
    }
}

template<typename EventType>
HighPerformanceEventProcessor<EventType>::~HighPerformanceEventProcessor() {
    stop();
}

template<typename EventType>
bool HighPerformanceEventProcessor<EventType>::start() {
    if (running_.load(std::memory_order_acquire)) {
        return true;
    }
    
    if (!event_handler_) {
        return false;
    }
    
    try {
        stop_requested_.store(false, std::memory_order_release);
        running_.store(true, std::memory_order_release);
        
        // 创建工作线程
        worker_threads_.reserve(config_.worker_thread_count);
        for (std::size_t i = 0; i < config_.worker_thread_count; ++i) {
            worker_threads_.emplace_back([this, i]() {
                worker_thread_function(static_cast<int>(i));
            });
            
            // 设置CPU亲和性
            if (config_.enable_affinity && i < config_.cpu_cores.size()) {
                set_thread_affinity(static_cast<int>(i));
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        running_.store(false, std::memory_order_release);
        handle_error("Failed to start event processor: " + std::string(e.what()));
        return false;
    }
}

template<typename EventType>
void HighPerformanceEventProcessor<EventType>::stop() {
    if (!running_.load(std::memory_order_acquire)) {
        return;
    }
    
    stop_requested_.store(true, std::memory_order_release);
    
    // 等待所有工作线程完成
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    worker_threads_.clear();
    running_.store(false, std::memory_order_release);
}

template<typename EventType>
void HighPerformanceEventProcessor<EventType>::worker_thread_function(int thread_id) {
    // 批处理缓冲区
    std::vector<EventType> batch_buffer;
    batch_buffer.reserve(config_.batch_size);
    
    while (!stop_requested_.load(std::memory_order_acquire)) {
        batch_buffer.clear();
        
        // 检查是否有事件可处理
        if (!has_events_.load(std::memory_order_acquire)) {
            std::this_thread::sleep_for(config_.spin_wait_time);
            continue;
        }
        
        // 批量出队
        std::size_t dequeued = event_queue_.try_dequeue_bulk(
            std::back_inserter(batch_buffer), config_.batch_size);
        
        if (dequeued > 0) {
            process_event_batch(batch_buffer.data(), dequeued, thread_id);
        } else {
            has_events_.store(false, std::memory_order_release);
        }
    }
    
    // 处理剩余事件
    batch_buffer.clear();
    std::size_t remaining = event_queue_.try_dequeue_bulk(
        std::back_inserter(batch_buffer), config_.batch_size);
    
    if (remaining > 0) {
        process_event_batch(batch_buffer.data(), remaining, thread_id);
    }
}

template<typename EventType>
void HighPerformanceEventProcessor<EventType>::process_event_batch(
    EventType* events, std::size_t count, int thread_id) {
    
    for (std::size_t i = 0; i < count; ++i) {
        try {
            auto start_time = std::chrono::high_resolution_clock::now();
            
            event_handler_(events[i]);
            
            if (config_.enable_statistics) {
                auto end_time = std::chrono::high_resolution_clock::now();
                auto processing_time = std::chrono::duration_cast<std::chrono::nanoseconds>(
                    end_time - start_time).count();
                
                stats_.processed_events.fetch_add(1, std::memory_order_relaxed);
                update_processing_time_stats(static_cast<std::uint64_t>(processing_time));
            }
            
        } catch (const std::exception& e) {
            if (config_.enable_statistics) {
                stats_.failed_events.fetch_add(1, std::memory_order_relaxed);
            }
            handle_error("Event processing failed in thread " + std::to_string(thread_id) + 
                        ": " + std::string(e.what()));
        }
    }
}

template<typename EventType>
void HighPerformanceEventProcessor<EventType>::set_thread_affinity(int thread_id) {
    // 实现与HighPerformanceDataProcessor相同的CPU亲和性设置
    // 这里省略具体实现，参考之前的代码
}

template<typename EventType>
void HighPerformanceEventProcessor<EventType>::handle_error(const std::string& error) {
    if (error_handler_) {
        try {
            error_handler_(error);
        } catch (...) {
            // 忽略错误处理器的异常
        }
    }
}

template<typename EventType>
void HighPerformanceEventProcessor<EventType>::update_processing_time_stats(std::uint64_t processing_time_ns) {
    stats_.total_processing_time_ns.fetch_add(processing_time_ns, std::memory_order_relaxed);
    
    // 更新最大处理时间
    auto current_max = stats_.max_processing_time_ns.load(std::memory_order_relaxed);
    while (processing_time_ns > current_max) {
        if (stats_.max_processing_time_ns.compare_exchange_weak(
                current_max, processing_time_ns, std::memory_order_relaxed)) {
            break;
        }
    }
    
    // 更新最小处理时间
    auto current_min = stats_.min_processing_time_ns.load(std::memory_order_relaxed);
    while (processing_time_ns < current_min) {
        if (stats_.min_processing_time_ns.compare_exchange_weak(
                current_min, processing_time_ns, std::memory_order_relaxed)) {
            break;
        }
    }
}

} // namespace DataHub::Core
