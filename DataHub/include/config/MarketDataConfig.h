#pragma once

#include "../providers/IMarketDataProvider.h"
#include "../providers/CtpMarketDataProvider.h"
#include "../providers/WebDataProvider.h"
#include "../services/MarketDataService.h"
#include "../core/Types.h"
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <memory>
#include <fstream>

namespace DataHub::Config {

// JSON配置加载器
class ConfigLoader {
public:
    // 从文件加载配置
    static Core::Result<nlohmann::json> load_from_file(const std::string& file_path);
    
    // 从字符串加载配置
    static Core::Result<nlohmann::json> load_from_string(const std::string& json_str);
    
    // 保存配置到文件
    static Core::Result<void> save_to_file(const nlohmann::json& config, const std::string& file_path);
    
    // 验证配置格式
    static Core::Result<void> validate_config(const nlohmann::json& config);
};

// CTP配置构建器
class CtpConfigBuilder {
public:
    static Core::Result<std::shared_ptr<Providers::CtpConfig>> from_json(const nlohmann::json& json);
    static nlohmann::json to_json(const Providers::CtpConfig& config);
    
    // 默认配置模板
    static nlohmann::json get_default_template();
};

// WebData配置构建器
class WebDataConfigBuilder {
public:
    static Core::Result<std::shared_ptr<Providers::WebDataConfig>> from_json(const nlohmann::json& json);
    static nlohmann::json to_json(const Providers::WebDataConfig& config);
    
    // 默认配置模板
    static nlohmann::json get_default_template();
};

// 市场数据服务配置构建器
class MarketDataServiceConfigBuilder {
public:
    static Core::Result<Services::MarketDataServiceConfig> from_json(const nlohmann::json& json);
    static nlohmann::json to_json(const Services::MarketDataServiceConfig& config);
    
    // 默认配置模板
    static nlohmann::json get_default_template();
};

// 配置管理器
class ConfigManager {
public:
    explicit ConfigManager(const std::string& config_file_path = "market_data_config.json");
    
    // 加载配置
    Core::Result<void> load_config();
    
    // 保存配置
    Core::Result<void> save_config();
    
    // 重新加载配置
    Core::Result<void> reload_config();
    
    // 获取服务配置
    Services::MarketDataServiceConfig get_service_config() const;
    
    // 获取CTP配置
    std::shared_ptr<Providers::CtpConfig> get_ctp_config() const;
    
    // 获取WebData配置
    std::shared_ptr<Providers::WebDataConfig> get_webdata_config() const;
    
    // 设置配置
    void set_service_config(const Services::MarketDataServiceConfig& config);
    void set_ctp_config(std::shared_ptr<Providers::CtpConfig> config);
    void set_webdata_config(std::shared_ptr<Providers::WebDataConfig> config);
    
    // 配置验证
    Core::Result<void> validate_all_configs() const;
    
    // 生成默认配置文件
    static Core::Result<void> generate_default_config_file(const std::string& file_path);
    
    // 配置热重载支持
    void enable_hot_reload(bool enable = true);
    bool is_hot_reload_enabled() const;
    
    // 配置变更回调
    using ConfigChangeCallback = std::function<void(const std::string& section)>;
    void set_config_change_callback(ConfigChangeCallback callback);

private:
    std::string config_file_path_;
    nlohmann::json config_json_;
    
    Services::MarketDataServiceConfig service_config_;
    std::shared_ptr<Providers::CtpConfig> ctp_config_;
    std::shared_ptr<Providers::WebDataConfig> webdata_config_;
    
    bool hot_reload_enabled_{false};
    ConfigChangeCallback config_change_callback_;
    
    // 内部方法
    Core::Result<void> parse_config();
    Core::Result<void> build_configs_from_json();
    nlohmann::json build_json_from_configs() const;
    
    // 文件监控（用于热重载）
    void start_file_monitoring();
    void stop_file_monitoring();
    std::thread file_monitor_thread_;
    std::atomic<bool> stop_monitoring_{false};
};

// 配置模板生成器
class ConfigTemplateGenerator {
public:
    // 生成完整的配置模板
    static nlohmann::json generate_full_template();
    
    // 生成CTP专用配置模板
    static nlohmann::json generate_ctp_only_template();
    
    // 生成WebData专用配置模板
    static nlohmann::json generate_webdata_only_template();
    
    // 生成开发环境配置模板
    static nlohmann::json generate_development_template();
    
    // 生成生产环境配置模板
    static nlohmann::json generate_production_template();
    
    // 生成测试环境配置模板
    static nlohmann::json generate_test_template();
};

// 环境变量配置支持
class EnvironmentConfigLoader {
public:
    // 从环境变量加载CTP配置
    static Core::Result<std::shared_ptr<Providers::CtpConfig>> load_ctp_from_env();
    
    // 从环境变量加载WebData配置
    static Core::Result<std::shared_ptr<Providers::WebDataConfig>> load_webdata_from_env();
    
    // 从环境变量加载服务配置
    static Core::Result<Services::MarketDataServiceConfig> load_service_from_env();
    
    // 设置环境变量前缀
    static void set_env_prefix(const std::string& prefix);
    
    // 获取环境变量值
    static std::string get_env_value(const std::string& key, const std::string& default_value = "");
    
    // 检查必需的环境变量
    static Core::Result<void> validate_required_env_vars();

private:
    static std::string env_prefix_;
    static std::string build_env_key(const std::string& key);
};

// 配置加密支持
class ConfigEncryption {
public:
    // 加密敏感配置项（如密码）
    static Core::Result<std::string> encrypt_value(const std::string& value, const std::string& key);
    
    // 解密敏感配置项
    static Core::Result<std::string> decrypt_value(const std::string& encrypted_value, const std::string& key);
    
    // 加密整个配置文件
    static Core::Result<void> encrypt_config_file(const std::string& input_file, 
                                                  const std::string& output_file, 
                                                  const std::string& key);
    
    // 解密整个配置文件
    static Core::Result<void> decrypt_config_file(const std::string& input_file, 
                                                  const std::string& output_file, 
                                                  const std::string& key);
    
    // 生成加密密钥
    static std::string generate_encryption_key();
    
    // 验证加密密钥
    static bool validate_encryption_key(const std::string& key);
};

// 配置迁移支持
class ConfigMigration {
public:
    // 检查配置版本
    static Core::Result<int> get_config_version(const nlohmann::json& config);
    
    // 迁移配置到最新版本
    static Core::Result<nlohmann::json> migrate_to_latest(const nlohmann::json& old_config);
    
    // 从v1.0迁移到v1.1
    static Core::Result<nlohmann::json> migrate_v1_0_to_v1_1(const nlohmann::json& config);
    
    // 备份配置文件
    static Core::Result<void> backup_config_file(const std::string& file_path);
    
    // 恢复配置文件
    static Core::Result<void> restore_config_file(const std::string& backup_path, 
                                                  const std::string& target_path);

private:
    static constexpr int CURRENT_CONFIG_VERSION = 1;
    static std::string get_backup_filename(const std::string& original_path);
};

// 便利函数
namespace Utils {
    // 快速创建配置管理器
    std::unique_ptr<ConfigManager> create_config_manager(const std::string& config_file = "");
    
    // 快速加载服务配置
    Core::Result<Services::MarketDataServiceConfig> load_service_config(const std::string& config_file);
    
    // 快速保存服务配置
    Core::Result<void> save_service_config(const Services::MarketDataServiceConfig& config, 
                                          const std::string& config_file);
    
    // 合并配置文件
    Core::Result<nlohmann::json> merge_configs(const std::vector<std::string>& config_files);
    
    // 验证配置文件
    Core::Result<void> validate_config_file(const std::string& config_file);
    
    // 生成配置文档
    Core::Result<std::string> generate_config_documentation();
}

} // namespace DataHub::Config
