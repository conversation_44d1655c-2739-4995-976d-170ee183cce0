#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../core/SecurityInfo.h"
#include <future>

namespace DataHub::Data {

// 查询条件
struct QueryCondition {
    std::optional<Core::Symbol> symbol;
    std::optional<Core::Timestamp> start_time;
    std::optional<Core::Timestamp> end_time;
    std::optional<Core::BarSize> bar_size;
    std::optional<Core::BarType> bar_type;
    std::optional<std::size_t> limit;
    std::optional<std::size_t> offset;
    
    // 排序选项
    enum class SortOrder : std::uint8_t {
        Ascending = 0,
        Descending = 1
    };
    
    struct SortBy {
        std::string field;
        SortOrder order{SortOrder::Ascending};
    };
    
    std::optional<SortBy> sort_by;
};

// 异步任务类型
template<typename T>
using AsyncTask = std::future<Core::Result<T>>;

// 数据仓库基础接口
class IDataRepository {
public:
    virtual ~IDataRepository() = default;
    
    // 连接和断开
    virtual Core::Result<void> connect() = 0;
    virtual Core::Result<void> disconnect() = 0;
    virtual bool is_connected() const noexcept = 0;
    
    // 事务支持
    virtual Core::Result<void> begin_transaction() = 0;
    virtual Core::Result<void> commit_transaction() = 0;
    virtual Core::Result<void> rollback_transaction() = 0;
    
    // 健康检查
    virtual Core::Result<bool> health_check() = 0;
};

// 行情数据仓库接口
class IQuoteRepository : public virtual IDataRepository {
public:
    // 同步接口
    virtual Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::QuoteDataVector> get_quotes(const QueryCondition& condition) = 0;
    virtual Core::Result<void> save_quote(const Core::QuoteData& quote) = 0;
    virtual Core::Result<void> save_quotes(Core::QuoteDataSpanConst quotes) = 0;
    
    // 异步接口
    virtual AsyncTask<Core::QuoteData> get_latest_quote_async(const Core::Symbol& symbol) = 0;
    virtual AsyncTask<Core::QuoteDataVector> get_quotes_async(const QueryCondition& condition) = 0;
    virtual AsyncTask<void> save_quote_async(const Core::QuoteData& quote) = 0;
    virtual AsyncTask<void> save_quotes_async(Core::QuoteDataSpanConst quotes) = 0;
    
    // 批量操作
    virtual Core::Result<void> batch_save_quotes(Core::QuoteDataSpanConst quotes) = 0;
    virtual Core::Result<std::size_t> delete_quotes(const QueryCondition& condition) = 0;
    virtual Core::Result<std::size_t> count_quotes(const QueryCondition& condition) = 0;
};

// K线数据仓库接口
class IBarRepository : public virtual IDataRepository {
public:
    // 同步接口
    virtual Core::Result<Core::BarDataVector> get_bars(const QueryCondition& condition) = 0;
    virtual Core::Result<Core::BarData> get_latest_bar(const Core::Symbol& symbol, 
                                                       Core::BarSize bar_size, 
                                                       Core::BarType bar_type) = 0;
    virtual Core::Result<void> save_bar(const Core::BarData& bar) = 0;
    virtual Core::Result<void> save_bars(Core::BarDataSpanConst bars) = 0;
    virtual Core::Result<void> update_bar(const Core::BarData& bar) = 0;
    
    // 异步接口
    virtual AsyncTask<Core::BarDataVector> get_bars_async(const QueryCondition& condition) = 0;
    virtual AsyncTask<Core::BarData> get_latest_bar_async(const Core::Symbol& symbol, 
                                                          Core::BarSize bar_size, 
                                                          Core::BarType bar_type) = 0;
    virtual AsyncTask<void> save_bar_async(const Core::BarData& bar) = 0;
    virtual AsyncTask<void> save_bars_async(Core::BarDataSpanConst bars) = 0;
    
    // 数据聚合
    virtual Core::Result<Core::BarDataVector> aggregate_bars(const Core::Symbol& symbol,
                                                             Core::BarSize from_size,
                                                             Core::BarSize to_size,
                                                             const QueryCondition& condition) = 0;
    
    // 批量操作
    virtual Core::Result<void> batch_save_bars(Core::BarDataSpanConst bars) = 0;
    virtual Core::Result<std::size_t> delete_bars(const QueryCondition& condition) = 0;
    virtual Core::Result<std::size_t> count_bars(const QueryCondition& condition) = 0;
};

// Tick数据仓库接口
class ITickRepository : public virtual IDataRepository {
public:
    // 同步接口
    virtual Core::Result<Core::TickDataVector> get_ticks(const QueryCondition& condition) = 0;
    virtual Core::Result<void> save_tick(const Core::TickData& tick) = 0;
    virtual Core::Result<void> save_ticks(Core::TickDataSpanConst ticks) = 0;
    
    // 异步接口
    virtual AsyncTask<Core::TickDataVector> get_ticks_async(const QueryCondition& condition) = 0;
    virtual AsyncTask<void> save_tick_async(const Core::TickData& tick) = 0;
    virtual AsyncTask<void> save_ticks_async(Core::TickDataSpanConst ticks) = 0;
    
    // 批量操作
    virtual Core::Result<void> batch_save_ticks(Core::TickDataSpanConst ticks) = 0;
    virtual Core::Result<std::size_t> delete_ticks(const QueryCondition& condition) = 0;
    virtual Core::Result<std::size_t> count_ticks(const QueryCondition& condition) = 0;
};

// 证券信息仓库接口
class ISecurityRepository : public virtual IDataRepository {
public:
    // 同步接口
    virtual Core::Result<Core::SecurityInfo> get_security(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::SecurityInfoVector> get_securities(const QueryCondition& condition) = 0;
    virtual Core::Result<void> save_security(const Core::SecurityInfo& security) = 0;
    virtual Core::Result<void> update_security(const Core::SecurityInfo& security) = 0;
    virtual Core::Result<void> delete_security(const Core::Symbol& symbol) = 0;
    
    // 板块相关
    virtual Core::Result<Core::BlockInfo> get_block(const std::string& name) = 0;
    virtual Core::Result<Core::BlockInfoVector> get_blocks() = 0;
    virtual Core::Result<void> save_block(const Core::BlockInfo& block) = 0;
    virtual Core::Result<void> update_block(const Core::BlockInfo& block) = 0;
    virtual Core::Result<void> delete_block(const std::string& name) = 0;
    
    // 异步接口
    virtual AsyncTask<Core::SecurityInfo> get_security_async(const Core::Symbol& symbol) = 0;
    virtual AsyncTask<Core::SecurityInfoVector> get_securities_async(const QueryCondition& condition) = 0;
};

} // namespace DataHub::Data
