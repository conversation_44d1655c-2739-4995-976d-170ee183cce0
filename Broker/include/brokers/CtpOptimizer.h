#pragma once

#include "brokers/CtpBroker.h"
#include "utils/ThreadSafety.h"
#include <atomic>
#include <chrono>
#include <memory>
#include <queue>

namespace RoboQuant::Broker {

// CTP 性能统计
struct CtpPerformanceStats {
    // 连接统计
    std::atomic<uint64_t> connect_count{0};
    std::atomic<uint64_t> disconnect_count{0};
    std::atomic<uint64_t> reconnect_count{0};
    std::atomic<uint64_t> auth_success_count{0};
    std::atomic<uint64_t> auth_failure_count{0};
    
    // 请求统计
    std::atomic<uint64_t> total_requests{0};
    std::atomic<uint64_t> successful_requests{0};
    std::atomic<uint64_t> failed_requests{0};
    std::atomic<uint64_t> timeout_requests{0};
    
    // 订单统计
    std::atomic<uint64_t> orders_submitted{0};
    std::atomic<uint64_t> orders_filled{0};
    std::atomic<uint64_t> orders_cancelled{0};
    std::atomic<uint64_t> orders_rejected{0};
    
    // 延迟统计
    std::atomic<double> avg_request_latency_ms{0.0};
    std::atomic<double> avg_order_latency_ms{0.0};
    std::atomic<double> max_request_latency_ms{0.0};
    std::atomic<double> max_order_latency_ms{0.0};
    
    // 错误统计
    std::atomic<uint64_t> network_errors{0};
    std::atomic<uint64_t> api_errors{0};
    std::atomic<uint64_t> timeout_errors{0};
    std::atomic<uint64_t> auth_errors{0};
    
    // 时间戳
    TimePoint start_time{std::chrono::system_clock::now()};
    std::atomic<TimePoint> last_update_time{std::chrono::system_clock::now()};
    
    // 计算统计信息
    double get_success_rate() const;
    double get_requests_per_second() const;
    double get_uptime_seconds() const;
    
    nlohmann::json to_json() const;
    void reset();
};

// CTP 连接池
class CtpConnectionPool {
public:
    explicit CtpConnectionPool(size_t pool_size = 3);
    ~CtpConnectionPool();
    
    // 获取连接
    std::shared_ptr<CtpBroker> acquire_connection(const CtpConfig& config);
    void release_connection(std::shared_ptr<CtpBroker> broker);
    
    // 连接管理
    void preconnect(const std::vector<CtpConfig>& configs);
    void disconnect_all();
    
    // 健康检查
    void health_check_all();
    size_t healthy_connections() const;
    size_t total_connections() const;
    
    // 统计信息
    CtpPerformanceStats get_pool_stats() const;

private:
    struct ConnectionInfo {
        std::shared_ptr<CtpBroker> broker;
        CtpConfig config;
        bool is_healthy{true};
        bool is_in_use{false};
        TimePoint last_used;
        TimePoint created_time;
    };
    
    void cleanup_idle_connections();
    void health_check_worker();
    
    size_t pool_size_;
    mutable std::mutex pool_mutex_;
    std::vector<std::unique_ptr<ConnectionInfo>> connections_;
    
    std::atomic<bool> health_check_running_{false};
    std::thread health_check_thread_;
    
    CtpPerformanceStats pool_stats_;
};

// CTP 请求优化器
class CtpRequestOptimizer {
public:
    explicit CtpRequestOptimizer(int base_interval_ms = 1000);
    
    // 自适应请求间隔
    void update_request_result(bool success, double latency_ms);
    int get_optimal_interval_ms() const;
    
    // 请求批处理
    void add_request(std::function<int()> request, int priority = 0);
    void process_batch();
    
    // 请求重试
    void add_retry_request(std::function<int()> request, int max_retries = 3);
    
    // 统计信息
    double get_success_rate() const;
    double get_average_latency() const;
    size_t get_pending_requests() const;

private:
    struct RequestItem {
        std::function<int()> request;
        int priority;
        int retry_count;
        int max_retries;
        TimePoint submit_time;
        
        bool operator<(const RequestItem& other) const {
            return priority < other.priority; // 优先级队列，数字越大优先级越高
        }
    };
    
    void adaptive_interval_adjustment();
    
    std::atomic<int> base_interval_ms_;
    std::atomic<int> current_interval_ms_;
    
    // 性能统计
    std::atomic<uint64_t> total_requests_{0};
    std::atomic<uint64_t> successful_requests_{0};
    std::atomic<double> total_latency_ms_{0.0};
    
    // 请求队列
    mutable std::mutex queue_mutex_;
    std::priority_queue<RequestItem> request_queue_;
    
    // 自适应参数
    static constexpr double SUCCESS_RATE_THRESHOLD = 0.95;
    static constexpr double LATENCY_THRESHOLD_MS = 500.0;
    static constexpr int MIN_INTERVAL_MS = 100;
    static constexpr int MAX_INTERVAL_MS = 5000;
};

// CTP 错误恢复器
class CtpErrorRecovery {
public:
    enum class ErrorType {
        NetworkError,
        AuthError,
        ApiError,
        TimeoutError,
        UnknownError
    };
    
    struct RecoveryStrategy {
        int max_retry_count{3};
        Duration retry_interval{std::chrono::seconds(5)};
        bool auto_reconnect{true};
        bool reset_session{false};
        std::function<bool()> custom_recovery;
    };
    
    CtpErrorRecovery();
    
    // 错误处理
    bool handle_error(ErrorType error_type, const std::string& error_msg, 
                     std::shared_ptr<CtpBroker> broker);
    
    // 恢复策略配置
    void set_recovery_strategy(ErrorType error_type, const RecoveryStrategy& strategy);
    RecoveryStrategy get_recovery_strategy(ErrorType error_type) const;
    
    // 错误统计
    uint64_t get_error_count(ErrorType error_type) const;
    uint64_t get_recovery_success_count(ErrorType error_type) const;
    double get_recovery_success_rate(ErrorType error_type) const;

private:
    bool try_network_recovery(std::shared_ptr<CtpBroker> broker);
    bool try_auth_recovery(std::shared_ptr<CtpBroker> broker);
    bool try_api_recovery(std::shared_ptr<CtpBroker> broker);
    bool try_timeout_recovery(std::shared_ptr<CtpBroker> broker);
    
    std::unordered_map<ErrorType, RecoveryStrategy> strategies_;
    std::unordered_map<ErrorType, std::atomic<uint64_t>> error_counts_;
    std::unordered_map<ErrorType, std::atomic<uint64_t>> recovery_success_counts_;
    
    mutable std::shared_mutex recovery_mutex_;
};

// CTP 缓存管理器
class CtpCacheManager {
public:
    explicit CtpCacheManager(size_t max_cache_size = 10000);
    
    // 订单缓存
    void cache_order(const Order& order);
    std::optional<Order> get_cached_order(const OrderId& order_id) const;
    void remove_cached_order(const OrderId& order_id);
    void clear_order_cache();
    
    // 账户缓存
    void cache_account(const Account& account);
    std::optional<Account> get_cached_account(const AccountId& account_id) const;
    void update_account_cache(const AccountId& account_id, const AccountBalance& balance);
    
    // 持仓缓存
    void cache_position(const Position& position);
    std::optional<Position> get_cached_position(const AccountId& account_id, const AssetId& asset_id) const;
    std::vector<Position> get_cached_positions(const AccountId& account_id) const;
    
    // 行情缓存
    void cache_market_data(const AssetId& asset_id, Price price, TimePoint timestamp);
    std::optional<std::pair<Price, TimePoint>> get_cached_market_data(const AssetId& asset_id) const;
    
    // 缓存统计
    size_t get_cache_size() const;
    double get_hit_rate() const;
    void clear_all_cache();

private:
    template<typename T>
    class LRUCache {
    public:
        explicit LRUCache(size_t capacity) : capacity_(capacity) {}
        
        void put(const std::string& key, const T& value);
        std::optional<T> get(const std::string& key);
        void remove(const std::string& key);
        void clear();
        size_t size() const;
        
    private:
        struct Node {
            std::string key;
            T value;
            std::shared_ptr<Node> prev;
            std::shared_ptr<Node> next;
        };
        
        size_t capacity_;
        std::unordered_map<std::string, std::shared_ptr<Node>> cache_;
        std::shared_ptr<Node> head_;
        std::shared_ptr<Node> tail_;
        mutable std::shared_mutex mutex_;
        
        void move_to_head(std::shared_ptr<Node> node);
        void remove_node(std::shared_ptr<Node> node);
        std::shared_ptr<Node> remove_tail();
    };
    
    LRUCache<Order> order_cache_;
    LRUCache<Account> account_cache_;
    LRUCache<Position> position_cache_;
    LRUCache<std::pair<Price, TimePoint>> market_data_cache_;
    
    std::atomic<uint64_t> total_requests_{0};
    std::atomic<uint64_t> cache_hits_{0};
    
    size_t max_cache_size_;
};

// CTP 监控器
class CtpMonitor {
public:
    CtpMonitor();
    ~CtpMonitor();
    
    // 监控管理
    void start_monitoring(std::shared_ptr<CtpBroker> broker);
    void stop_monitoring();
    bool is_monitoring() const;
    
    // 性能监控
    void record_request_latency(double latency_ms);
    void record_order_latency(double latency_ms);
    void record_error(const std::string& error_type);
    
    // 健康检查
    bool is_broker_healthy() const;
    std::string get_health_status() const;
    
    // 告警
    using AlertCallback = std::function<void(const std::string& alert_type, const std::string& message)>;
    void set_alert_callback(AlertCallback callback);
    
    // 统计报告
    CtpPerformanceStats get_performance_stats() const;
    nlohmann::json generate_monitoring_report() const;

private:
    void monitoring_worker();
    void check_performance_thresholds();
    void send_alert(const std::string& alert_type, const std::string& message);
    
    std::shared_ptr<CtpBroker> monitored_broker_;
    std::atomic<bool> monitoring_active_{false};
    std::thread monitoring_thread_;
    
    CtpPerformanceStats stats_;
    AlertCallback alert_callback_;
    
    // 告警阈值
    double max_latency_threshold_ms_{1000.0};
    double min_success_rate_threshold_{0.9};
    int max_consecutive_errors_{5};
    
    std::atomic<int> consecutive_errors_{0};
    TimePoint last_alert_time_;
    Duration alert_cooldown_{std::chrono::minutes(5)};
};

// CTP 优化器主类
class CtpOptimizer {
public:
    explicit CtpOptimizer(std::shared_ptr<CtpBroker> broker);
    ~CtpOptimizer();
    
    // 优化管理
    void enable_optimization(bool enable = true);
    bool is_optimization_enabled() const;
    
    // 组件访问
    CtpRequestOptimizer& get_request_optimizer() { return request_optimizer_; }
    CtpErrorRecovery& get_error_recovery() { return error_recovery_; }
    CtpCacheManager& get_cache_manager() { return cache_manager_; }
    CtpMonitor& get_monitor() { return monitor_; }
    
    // 性能报告
    nlohmann::json generate_performance_report() const;
    void reset_performance_stats();
    
    // 优化建议
    std::vector<std::string> get_optimization_suggestions() const;

private:
    std::shared_ptr<CtpBroker> broker_;
    std::atomic<bool> optimization_enabled_{false};
    
    CtpRequestOptimizer request_optimizer_;
    CtpErrorRecovery error_recovery_;
    CtpCacheManager cache_manager_;
    CtpMonitor monitor_;
};

// 工具函数
std::string to_string(CtpErrorRecovery::ErrorType error_type);
CtpErrorRecovery::ErrorType parse_error_type(const std::string& str);

// 性能基准测试
class CtpBenchmark {
public:
    struct BenchmarkResult {
        double avg_latency_ms;
        double max_latency_ms;
        double min_latency_ms;
        double success_rate;
        uint64_t total_requests;
        Duration total_time;
        
        nlohmann::json to_json() const;
    };
    
    static BenchmarkResult run_connection_benchmark(const CtpConfig& config, int iterations = 100);
    static BenchmarkResult run_order_benchmark(std::shared_ptr<CtpBroker> broker, int iterations = 100);
    static BenchmarkResult run_query_benchmark(std::shared_ptr<CtpBroker> broker, int iterations = 100);
    
    static nlohmann::json run_full_benchmark(const CtpConfig& config);
};

} // namespace RoboQuant::Broker
