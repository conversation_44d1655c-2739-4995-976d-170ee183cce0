#pragma once

#include "brokers/CtpBroker.h"
#include "core/Configuration.h"
#include <unordered_map>
#include <vector>

namespace RoboQuant::Broker {

// CTP 环境配置
enum class CtpEnvironment {
    Production,    // 生产环境
    Simulation,    // 仿真环境
    Testing,       // 测试环境
    Development    // 开发环境
};

std::string to_string(CtpEnvironment env);
CtpEnvironment parse_ctp_environment(const std::string& str);

// CTP 服务器配置
struct CtpServerConfig {
    std::string name;                    // 服务器名称
    std::string trade_front;             // 交易前置地址
    std::string md_front;                // 行情前置地址
    std::string broker_id;               // 经纪商代码
    CtpEnvironment environment;          // 环境类型
    bool is_active{true};                // 是否激活
    int priority{0};                     // 优先级 (数字越小优先级越高)
    
    nlohmann::json to_json() const;
    static CtpServerConfig from_json(const nlohmann::json& j);
};

// CTP 账户配置
struct CtpAccountConfig {
    std::string account_id;              // 账户ID
    std::string user_id;                 // 用户代码
    std::string password;                // 密码
    std::string app_id;                  // 应用标识
    std::string auth_code;               // 授权编码
    std::string server_name;             // 关联的服务器名称
    bool is_active{true};                // 是否激活
    
    // 风控参数
    double max_position_value{1000000.0}; // 最大持仓价值
    double max_order_value{100000.0};      // 最大单笔订单价值
    int max_orders_per_second{10};         // 每秒最大订单数
    
    nlohmann::json to_json() const;
    static CtpAccountConfig from_json(const nlohmann::json& j);
};

// CTP 交易参数配置
struct CtpTradingConfig {
    // 请求参数
    int request_interval_ms{1000};       // 请求间隔
    int max_retry_count{3};              // 最大重试次数
    int retry_interval_ms{5000};         // 重试间隔
    
    // 连接参数
    int connect_timeout_ms{10000};       // 连接超时
    int heartbeat_interval_ms{30000};    // 心跳间隔
    bool auto_reconnect{true};           // 自动重连
    int max_reconnect_attempts{5};       // 最大重连次数
    int reconnect_interval_ms{5000};     // 重连间隔
    
    // 查询参数
    int query_interval_ms{1000};         // 查询间隔
    bool auto_query_account{true};       // 自动查询账户
    bool auto_query_position{true};      // 自动查询持仓
    int account_query_interval_ms{5000}; // 账户查询间隔
    int position_query_interval_ms{10000}; // 持仓查询间隔
    
    // 订单参数
    bool enable_order_validation{true};  // 启用订单验证
    bool enable_risk_check{true};        // 启用风险检查
    int order_timeout_ms{30000};         // 订单超时
    
    // 行情参数
    bool enable_market_data{true};       // 启用行情
    int md_heartbeat_interval_ms{30000}; // 行情心跳间隔
    
    nlohmann::json to_json() const;
    static CtpTradingConfig from_json(const nlohmann::json& j);
};

// CTP 配置管理器
class CtpConfigManager {
public:
    static CtpConfigManager& instance();
    
    // 配置文件管理
    Result<void> load_config(const std::filesystem::path& config_file);
    Result<void> save_config(const std::filesystem::path& config_file) const;
    Result<void> reload_config();
    
    // 服务器配置管理
    void add_server_config(const CtpServerConfig& config);
    void remove_server_config(const std::string& name);
    void update_server_config(const std::string& name, const CtpServerConfig& config);
    std::optional<CtpServerConfig> get_server_config(const std::string& name) const;
    std::vector<CtpServerConfig> get_all_server_configs() const;
    std::vector<CtpServerConfig> get_active_server_configs() const;
    CtpServerConfig get_best_server_config(CtpEnvironment env = CtpEnvironment::Production) const;
    
    // 账户配置管理
    void add_account_config(const CtpAccountConfig& config);
    void remove_account_config(const std::string& account_id);
    void update_account_config(const std::string& account_id, const CtpAccountConfig& config);
    std::optional<CtpAccountConfig> get_account_config(const std::string& account_id) const;
    std::vector<CtpAccountConfig> get_all_account_configs() const;
    std::vector<CtpAccountConfig> get_active_account_configs() const;
    
    // 交易参数管理
    void set_trading_config(const CtpTradingConfig& config);
    CtpTradingConfig get_trading_config() const;
    
    // 环境管理
    void set_current_environment(CtpEnvironment env);
    CtpEnvironment get_current_environment() const;
    
    // 配置验证
    Result<void> validate_server_config(const CtpServerConfig& config) const;
    Result<void> validate_account_config(const CtpAccountConfig& config) const;
    Result<void> validate_trading_config(const CtpTradingConfig& config) const;
    
    // 配置生成
    CtpConfig generate_ctp_config(const std::string& account_id) const;
    CtpConfig generate_ctp_config(const CtpAccountConfig& account_config, 
                                 const CtpServerConfig& server_config) const;
    
    // 配置模板
    static CtpServerConfig create_production_server_template();
    static CtpServerConfig create_simulation_server_template();
    static CtpAccountConfig create_account_template();
    static CtpTradingConfig create_trading_template();
    
    // 配置导入导出
    Result<void> export_config(const std::filesystem::path& export_file) const;
    Result<void> import_config(const std::filesystem::path& import_file);
    
    // 配置备份
    Result<void> backup_config(const std::string& backup_name = "") const;
    Result<void> restore_config(const std::string& backup_name);
    std::vector<std::string> list_config_backups() const;
    
    // 配置监控
    void enable_config_monitoring(bool enable = true);
    bool is_config_monitoring_enabled() const;
    
    // 配置变更通知
    using ConfigChangeCallback = std::function<void(const std::string& type, const std::string& name)>;
    void set_config_change_callback(ConfigChangeCallback callback);

private:
    CtpConfigManager() = default;
    ~CtpConfigManager() = default;
    
    // 禁止拷贝和移动
    CtpConfigManager(const CtpConfigManager&) = delete;
    CtpConfigManager& operator=(const CtpConfigManager&) = delete;
    
    void notify_config_change(const std::string& type, const std::string& name);
    void start_config_monitoring();
    void stop_config_monitoring();
    void config_monitor_worker();
    
    mutable std::shared_mutex config_mutex_;
    
    // 配置数据
    std::unordered_map<std::string, CtpServerConfig> server_configs_;
    std::unordered_map<std::string, CtpAccountConfig> account_configs_;
    CtpTradingConfig trading_config_;
    CtpEnvironment current_environment_{CtpEnvironment::Production};
    
    // 配置文件路径
    std::filesystem::path config_file_path_;
    std::filesystem::file_time_type last_config_time_;
    
    // 配置监控
    std::atomic<bool> monitoring_enabled_{false};
    std::thread monitor_thread_;
    
    // 变更通知
    ConfigChangeCallback change_callback_;
    
    // 备份管理
    std::filesystem::path backup_directory_{"./config_backups/"};
};

// CTP 配置验证器
class CtpConfigValidator {
public:
    static Result<void> validate_server_address(const std::string& address);
    static Result<void> validate_broker_id(const std::string& broker_id);
    static Result<void> validate_user_credentials(const std::string& user_id, 
                                                  const std::string& password);
    static Result<void> validate_app_credentials(const std::string& app_id, 
                                                 const std::string& auth_code);
    static Result<void> validate_timeout_values(int connect_timeout, int heartbeat_interval);
    static Result<void> validate_trading_parameters(const CtpTradingConfig& config);
    static Result<void> validate_risk_parameters(const CtpAccountConfig& config);
    
    // 网络连通性测试
    static Result<void> test_server_connectivity(const std::string& address, int timeout_ms = 5000);
    static Result<void> test_ctp_server_connectivity(const CtpServerConfig& config);
};

// CTP 配置工具
class CtpConfigUtils {
public:
    // 配置文件格式转换
    static Result<nlohmann::json> convert_ini_to_json(const std::filesystem::path& ini_file);
    static Result<void> convert_json_to_ini(const nlohmann::json& json, 
                                           const std::filesystem::path& ini_file);
    
    // 配置加密解密
    static std::string encrypt_password(const std::string& password, const std::string& key);
    static std::string decrypt_password(const std::string& encrypted_password, const std::string& key);
    
    // 配置合并
    static CtpConfig merge_configs(const CtpConfig& base, const CtpConfig& override);
    static CtpTradingConfig merge_trading_configs(const CtpTradingConfig& base, 
                                                  const CtpTradingConfig& override);
    
    // 环境变量支持
    static CtpConfig apply_env_overrides(const CtpConfig& config, const std::string& prefix = "CTP_");
    static std::unordered_map<std::string, std::string> get_ctp_env_vars(const std::string& prefix = "CTP_");
    
    // 配置模板生成
    static nlohmann::json generate_config_template();
    static nlohmann::json generate_server_template(CtpEnvironment env);
    static nlohmann::json generate_account_template();
    
    // 配置验证报告
    struct ValidationReport {
        bool is_valid{true};
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
        std::vector<std::string> suggestions;
        
        nlohmann::json to_json() const;
    };
    
    static ValidationReport validate_complete_config(const nlohmann::json& config);
    static ValidationReport validate_ctp_config(const CtpConfig& config);
};

// 预定义配置常量
namespace CtpConfigConstants {
    // 生产环境服务器
    extern const std::vector<CtpServerConfig> PRODUCTION_SERVERS;
    
    // 仿真环境服务器
    extern const std::vector<CtpServerConfig> SIMULATION_SERVERS;
    
    // 默认交易参数
    extern const CtpTradingConfig DEFAULT_TRADING_CONFIG;
    
    // 配置文件模板
    extern const std::string CONFIG_TEMPLATE_JSON;
    
    // 环境变量前缀
    extern const std::string ENV_PREFIX;
}

// 便利宏
#define CTP_CONFIG_GET(key, type) \
    CtpConfigManager::instance().get_trading_config().key

#define CTP_CONFIG_SET(key, value) \
    do { \
        auto config = CtpConfigManager::instance().get_trading_config(); \
        config.key = value; \
        CtpConfigManager::instance().set_trading_config(config); \
    } while(0)

} // namespace RoboQuant::Broker
