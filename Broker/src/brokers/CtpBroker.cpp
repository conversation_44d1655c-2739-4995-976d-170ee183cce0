#include "brokers/CtpBroker.h"
#include "accounts/Account.h"
#include <spdlog/spdlog.h>
#include <filesystem>
#include <random>
#include <sstream>
#include <iomanip>

namespace RoboQuant::Broker {

namespace {
    // 生成唯一的订单引用
    std::string generate_order_ref() {
        static std::atomic<uint32_t> counter{1};
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
            now.time_since_epoch()).count();
        
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(8) << (timestamp % *********)
            << std::setw(4) << (counter.fetch_add(1) % 10000);
        return oss.str();
    }
}

// CtpConfig 实现
nlohmann::json CtpConfig::to_json() const {
    nlohmann::json j;
    j["front_address"] = front_address;
    j["md_front_address"] = md_front_address;
    j["broker_id"] = broker_id;
    j["user_id"] = user_id;
    j["password"] = password;
    j["app_id"] = app_id;
    j["auth_code"] = auth_code;
    j["user_product_info"] = user_product_info;
    j["flow_path"] = flow_path;
    j["connect_timeout_ms"] = connect_timeout_ms;
    j["heartbeat_interval_ms"] = heartbeat_interval_ms;
    j["auto_reconnect"] = auto_reconnect;
    j["max_reconnect_attempts"] = max_reconnect_attempts;
    j["enable_market_data"] = enable_market_data;
    j["enable_trading"] = enable_trading;
    j["request_interval_ms"] = request_interval_ms;
    return j;
}

CtpConfig CtpConfig::from_json(const nlohmann::json& j) {
    CtpConfig config;
    config.front_address = j.value("front_address", "");
    config.md_front_address = j.value("md_front_address", "");
    config.broker_id = j.value("broker_id", "");
    config.user_id = j.value("user_id", "");
    config.password = j.value("password", "");
    config.app_id = j.value("app_id", "");
    config.auth_code = j.value("auth_code", "");
    config.user_product_info = j.value("user_product_info", "BrokerModern");
    config.flow_path = j.value("flow_path", "./ctp_flow/");
    config.connect_timeout_ms = j.value("connect_timeout_ms", 10000);
    config.heartbeat_interval_ms = j.value("heartbeat_interval_ms", 30000);
    config.auto_reconnect = j.value("auto_reconnect", true);
    config.max_reconnect_attempts = j.value("max_reconnect_attempts", 5);
    config.enable_market_data = j.value("enable_market_data", true);
    config.enable_trading = j.value("enable_trading", true);
    config.request_interval_ms = j.value("request_interval_ms", 1000);
    return config;
}

// CtpOrderStatusMap 实现
OrderStatus CtpOrderStatusMap::from_ctp_status(char ctp_status) {
    switch (ctp_status) {
        case '0': return OrderStatus::New;           // 全部成交
        case '1': return OrderStatus::PartiallyFilled; // 部分成交还在队列中
        case '2': return OrderStatus::PartiallyFilled; // 部分成交不在队列中
        case '3': return OrderStatus::New;           // 未成交还在队列中
        case '4': return OrderStatus::New;           // 未成交不在队列中
        case '5': return OrderStatus::Cancelled;     // 撤单
        case 'a': return OrderStatus::Unknown;       // 未知
        case 'b': return OrderStatus::New;           // 尚未触发
        case 'c': return OrderStatus::New;           // 已触发
        default: return OrderStatus::Unknown;
    }
}

char CtpOrderStatusMap::to_ctp_status(OrderStatus status) {
    switch (status) {
        case OrderStatus::New: return '3';
        case OrderStatus::PartiallyFilled: return '1';
        case OrderStatus::Filled: return '0';
        case OrderStatus::Cancelled: return '5';
        default: return 'a';
    }
}

OrderSide CtpOrderStatusMap::from_ctp_direction(char direction) {
    switch (direction) {
        case '0': return OrderSide::Buy;   // 买
        case '1': return OrderSide::Sell;  // 卖
        default: return OrderSide::Buy;
    }
}

char CtpOrderStatusMap::to_ctp_direction(OrderSide side) {
    switch (side) {
        case OrderSide::Buy: return '0';
        case OrderSide::Sell: return '1';
        default: return '0';
    }
}

OrderType CtpOrderStatusMap::from_ctp_order_type(char order_type) {
    switch (order_type) {
        case '1': return OrderType::Market;  // 任意价
        case '2': return OrderType::Limit;   // 限价
        case '3': return OrderType::Market;  // 最优价
        case '4': return OrderType::Market;  // 最新价
        default: return OrderType::Limit;
    }
}

char CtpOrderStatusMap::to_ctp_order_type(OrderType type) {
    switch (type) {
        case OrderType::Market: return '1';
        case OrderType::Limit: return '2';
        default: return '2';
    }
}

// CtpRequestManager 实现
CtpRequestManager::CtpRequestManager(int interval_ms) 
    : interval_(interval_ms) {
}

CtpRequestManager::~CtpRequestManager() {
    stop();
}

void CtpRequestManager::add_request(std::function<int()> request) {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        request_queue_.push(std::move(request));
    }
    queue_cv_.notify_one();
    total_requests_.fetch_add(1);
}

void CtpRequestManager::start() {
    if (running_.load()) return;
    
    running_.store(true);
    worker_ = std::thread(&CtpRequestManager::worker_thread, this);
    spdlog::info("CTP request manager started");
}

void CtpRequestManager::stop() {
    if (!running_.load()) return;
    
    running_.store(false);
    queue_cv_.notify_all();
    
    if (worker_.joinable()) {
        worker_.join();
    }
    spdlog::info("CTP request manager stopped");
}

size_t CtpRequestManager::pending_requests() const {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    return request_queue_.size();
}

size_t CtpRequestManager::total_requests() const {
    return total_requests_.load();
}

void CtpRequestManager::worker_thread() {
    while (running_.load()) {
        std::function<int()> request;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] {
                return !request_queue_.empty() || !running_.load();
            });
            
            if (!running_.load()) break;
            
            if (!request_queue_.empty()) {
                request = std::move(request_queue_.front());
                request_queue_.pop();
            }
        }
        
        if (request) {
            try {
                int result = request();
                if (result != 0) {
                    spdlog::warn("CTP request failed with code: {}", result);
                }
            } catch (const std::exception& e) {
                spdlog::error("Exception in CTP request: {}", e.what());
            }
            
            // 请求间隔控制
            std::this_thread::sleep_for(interval_);
        }
    }
}

// CtpBroker 实现
CtpBroker::CtpBroker(const CtpConfig& config)
    : BaseBroker(BrokerType::CTP, "CTP Broker")
    , config_(config)
    , request_manager_(std::make_unique<CtpRequestManager>(config.request_interval_ms)) {
    
    // 创建流文件目录
    if (!config_.flow_path.empty()) {
        std::filesystem::create_directories(config_.flow_path);
    }
    
    // 生成订单引用前缀
    order_ref_prefix_ = std::to_string(std::chrono::system_clock::now().time_since_epoch().count() % 1000000);
    
    spdlog::info("CTP Broker created with config: {}", config_.broker_id);
}

CtpBroker::~CtpBroker() {
    disconnect();
    spdlog::info("CTP Broker destroyed");
}

BrokerCapabilities CtpBroker::get_capabilities() const {
    BrokerCapabilities caps;
    caps.supports_market_orders = true;
    caps.supports_limit_orders = true;
    caps.supports_stop_orders = false;
    caps.supports_stop_limit_orders = false;
    caps.supports_fak_orders = true;
    caps.supports_fok_orders = true;
    caps.supports_order_modification = false; // CTP 不支持改单，只能撤单重发
    caps.supports_partial_fills = true;
    caps.supports_position_tracking = true;
    caps.supports_real_time_data = true;
    caps.supports_historical_data = false;
    caps.supports_multiple_accounts = false;
    
    caps.supported_asset_types = {
        AssetType::Stock, AssetType::Future, AssetType::Option
    };
    caps.supported_exchanges = {"SHFE", "DCE", "CZCE", "CFFEX", "INE"};
    
    return caps;
}

Result<void> CtpBroker::connect(const ConnectionInfo& info) {
    if (is_connected()) {
        return Result<void>::success();
    }
    
    set_connection_status(ConnectionStatus::Connecting);
    
    try {
        // 初始化 CTP API
        initialize_apis();
        
        // 启动请求管理器
        request_manager_->start();
        
        // 启动心跳
        start_heartbeat();
        
        // 模拟连接成功 (实际实现中会在 CTP 回调中设置)
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        on_front_connected();
        
        spdlog::info("CTP Broker connected to: {}", config_.front_address);
        return Result<void>::success();
        
    } catch (const std::exception& e) {
        set_connection_status(ConnectionStatus::Error);
        return Result<void>::failure(ErrorCode::ConnectionError, 
            "Failed to connect to CTP: " + std::string(e.what()));
    }
}

Result<void> CtpBroker::disconnect() {
    if (!is_connected()) {
        return Result<void>::success();
    }
    
    // 停止心跳
    stop_heartbeat();
    
    // 停止请求管理器
    if (request_manager_) {
        request_manager_->stop();
    }
    
    // 清理 API
    cleanup_apis();
    
    // 重置状态
    trader_connected_.store(false);
    md_connected_.store(false);
    authenticated_.store(false);
    logged_in_.store(false);
    
    set_connection_status(ConnectionStatus::Disconnected);
    spdlog::info("CTP Broker disconnected");
    
    return Result<void>::success();
}

Result<void> CtpBroker::authenticate(const std::string& username, const std::string& password) {
    if (!is_connected()) {
        return Result<void>::failure(ErrorCode::ConnectionError, "Not connected");
    }
    
    // 更新配置
    config_.user_id = username;
    config_.password = password;
    
    // 发送认证请求
    request_manager_->add_request([this]() {
        return req_authenticate();
    });
    
    // 等待认证结果 (实际实现中应该使用异步等待)
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    if (authenticated_.load()) {
        // 发送登录请求
        request_manager_->add_request([this]() {
            return req_user_login();
        });
        
        // 等待登录结果
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        if (logged_in_.load()) {
            set_connection_status(ConnectionStatus::Authenticated);
            spdlog::info("CTP Broker authenticated for user: {}", username);
            return Result<void>::success();
        } else {
            return Result<void>::failure(ErrorCode::AuthenticationError, "Login failed");
        }
    } else {
        return Result<void>::failure(ErrorCode::AuthenticationError, "Authentication failed");
    }
}

Result<void> CtpBroker::submit_order(const Order& order) {
    if (!is_authenticated()) {
        return Result<void>::failure(ErrorCode::ConnectionError, "Not authenticated");
    }
    
    // 验证订单
    if (!order.is_valid()) {
        return Result<void>::failure(ErrorCode::InvalidParameter, order.validation_error());
    }
    
    // 添加到内部订单管理
    add_order_internal(order);
    
    // 发送订单请求
    request_manager_->add_request([this, order]() {
        return req_order_insert(order);
    });
    
    spdlog::info("CTP order submitted: {}", order.order_id());
    return Result<void>::success();
}

Result<void> CtpBroker::cancel_order(const Order& order) {
    if (!is_authenticated()) {
        return Result<void>::failure(ErrorCode::ConnectionError, "Not authenticated");
    }
    
    auto order_ptr = find_order_internal(order.order_id());
    if (!order_ptr) {
        return Result<void>::failure(ErrorCode::OrderNotFound, 
            "Order not found: " + order.order_id());
    }
    
    if (!order_ptr->is_active()) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Cannot cancel inactive order: " + order.order_id());
    }
    
    // 更新订单状态
    update_order_internal(order.order_id(), [](Order& o) {
        o.set_status(OrderStatus::PendingCancel);
    });
    
    // 发送撤单请求
    request_manager_->add_request([this, order]() {
        return req_order_action(order);
    });
    
    spdlog::info("CTP order cancel requested: {}", order.order_id());
    return Result<void>::success();
}

Result<void> CtpBroker::modify_order(const Order& order, const OrderRequest& new_request) {
    // CTP 不支持改单，只能撤单重发
    auto cancel_result = cancel_order(order);
    if (!cancel_result) {
        return cancel_result;
    }
    
    // 创建新订单
    Order new_order(new_request);
    return submit_order(new_order);
}

Result<std::vector<Order>> CtpBroker::query_orders(const AccountId& account_id) {
    std::shared_lock<std::shared_mutex> lock(orders_mutex_);
    std::vector<Order> result;
    
    for (const auto& [id, order] : orders_) {
        if (account_id.empty() || order.account_id() == account_id) {
            result.push_back(order);
        }
    }
    
    return Result<std::vector<Order>>::success(std::move(result));
}

Result<Order> CtpBroker::query_order(const OrderId& order_id) {
    std::shared_lock<std::shared_mutex> lock(orders_mutex_);
    
    auto it = orders_.find(order_id);
    if (it == orders_.end()) {
        return Result<Order>::failure(ErrorCode::OrderNotFound, 
            "Order not found: " + order_id);
    }
    
    return Result<Order>::success(it->second);
}

Result<std::vector<Account>> CtpBroker::query_accounts() {
    // 发送账户查询请求
    request_manager_->add_request([this]() {
        return req_qry_trading_account();
    });
    
    // 等待查询结果 (实际实现中应该使用异步等待)
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    std::shared_lock<std::shared_mutex> lock(accounts_mutex_);
    std::vector<Account> result;
    
    for (const auto& [id, account] : accounts_) {
        result.push_back(account);
    }
    
    return Result<std::vector<Account>>::success(std::move(result));
}

Result<Account> CtpBroker::query_account(const AccountId& account_id) {
    std::shared_lock<std::shared_mutex> lock(accounts_mutex_);
    
    auto it = accounts_.find(account_id);
    if (it == accounts_.end()) {
        return Result<Account>::failure(ErrorCode::AccountNotFound, 
            "Account not found: " + account_id);
    }
    
    return Result<Account>::success(it->second);
}

Result<std::vector<Position>> CtpBroker::query_positions(const AccountId& account_id) {
    // 发送持仓查询请求
    request_manager_->add_request([this]() {
        return req_qry_investor_position();
    });
    
    // 等待查询结果
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    std::shared_lock<std::shared_mutex> lock(positions_mutex_);
    std::vector<Position> result;
    
    for (const auto& [key, position] : positions_) {
        if (account_id.empty() || position.account_id() == account_id) {
            result.push_back(position);
        }
    }
    
    return Result<std::vector<Position>>::success(std::move(result));
}

Result<std::vector<Trade>> CtpBroker::query_trades(const AccountId& account_id, 
                                                  const TimePoint& from,
                                                  const TimePoint& to) {
    std::shared_lock<std::shared_mutex> lock(trades_mutex_);
    std::vector<Trade> result;
    
    for (const auto& trade : trades_) {
        bool match_account = account_id.empty() || trade.account_id() == account_id;
        bool match_time = (from == TimePoint{} || trade.trade_time() >= from) &&
                         (to == TimePoint{} || trade.trade_time() <= to);
        
        if (match_account && match_time) {
            result.push_back(trade);
        }
    }
    
    return Result<std::vector<Trade>>::success(std::move(result));
}

Result<void> CtpBroker::subscribe_market_data(const AssetId& asset_id) {
    std::unique_lock<std::shared_mutex> lock(subscriptions_mutex_);
    subscribed_assets_.insert(asset_id);
    
    // 实际实现中会调用 CTP 行情订阅接口
    spdlog::info("CTP market data subscribed: {}", asset_id);
    return Result<void>::success();
}

Result<void> CtpBroker::unsubscribe_market_data(const AssetId& asset_id) {
    std::unique_lock<std::shared_mutex> lock(subscriptions_mutex_);
    subscribed_assets_.erase(asset_id);
    
    // 实际实现中会调用 CTP 行情退订接口
    spdlog::info("CTP market data unsubscribed: {}", asset_id);
    return Result<void>::success();
}

Result<void> CtpBroker::health_check() {
    if (!is_connected()) {
        return Result<void>::failure(ErrorCode::ConnectionError, "Not connected");
    }
    
    if (!is_authenticated()) {
        return Result<void>::failure(ErrorCode::AuthenticationError, "Not authenticated");
    }
    
    update_heartbeat();
    return Result<void>::success();
}

void CtpBroker::set_ctp_config(const CtpConfig& config) {
    config_ = config;
    if (request_manager_) {
        // 重新创建请求管理器以应用新的间隔设置
        request_manager_->stop();
        request_manager_ = std::make_unique<CtpRequestManager>(config_.request_interval_ms);
        if (is_connected()) {
            request_manager_->start();
        }
    }
}

CtpConfig CtpBroker::get_ctp_config() const {
    return config_;
}

bool CtpBroker::is_authenticated() const {
    return authenticated_.load() && logged_in_.load();
}

size_t CtpBroker::pending_requests() const {
    return request_manager_ ? request_manager_->pending_requests() : 0;
}

std::string CtpBroker::get_trading_day() const {
    return trading_day_;
}

// CTP API 回调处理
void CtpBroker::on_front_connected() {
    trader_connected_.store(true);
    set_connection_status(ConnectionStatus::Connected);
    spdlog::info("CTP front connected");
}

void CtpBroker::on_front_disconnected(int reason) {
    trader_connected_.store(false);
    authenticated_.store(false);
    logged_in_.store(false);
    set_connection_status(ConnectionStatus::Disconnected);
    spdlog::warn("CTP front disconnected, reason: {}", reason);

    // 自动重连逻辑
    if (config_.auto_reconnect) {
        // 实际实现中会启动重连线程
        spdlog::info("CTP auto reconnect enabled");
    }
}

void CtpBroker::on_rsp_authenticate(bool success, const std::string& error_msg) {
    if (success) {
        authenticated_.store(true);
        spdlog::info("CTP authentication successful");
    } else {
        authenticated_.store(false);
        spdlog::error("CTP authentication failed: {}", error_msg);
        notify_error(ErrorCode::AuthenticationError, "CTP authentication failed: " + error_msg);
    }
}

void CtpBroker::on_rsp_user_login(bool success, const std::string& error_msg) {
    if (success) {
        logged_in_.store(true);
        trading_day_ = "20240101"; // 实际实现中从 CTP 响应获取
        spdlog::info("CTP user login successful, trading day: {}", trading_day_);
    } else {
        logged_in_.store(false);
        spdlog::error("CTP user login failed: {}", error_msg);
        notify_error(ErrorCode::AuthenticationError, "CTP login failed: " + error_msg);
    }
}

void CtpBroker::on_rsp_user_logout(bool success, const std::string& error_msg) {
    logged_in_.store(false);
    if (success) {
        spdlog::info("CTP user logout successful");
    } else {
        spdlog::warn("CTP user logout failed: {}", error_msg);
    }
}

void CtpBroker::on_rsp_order_insert(const std::string& order_id, bool success, const std::string& error_msg) {
    if (success) {
        update_order_internal(order_id, [](Order& order) {
            order.set_status(OrderStatus::New);
        });
        spdlog::info("CTP order insert successful: {}", order_id);
    } else {
        update_order_internal(order_id, [&error_msg](Order& order) {
            order.set_status(OrderStatus::Rejected);
            order.set_error(error_msg);
        });
        spdlog::error("CTP order insert failed: {} - {}", order_id, error_msg);
    }
}

void CtpBroker::on_rsp_order_action(const std::string& order_id, bool success, const std::string& error_msg) {
    if (success) {
        spdlog::info("CTP order action successful: {}", order_id);
    } else {
        update_order_internal(order_id, [](Order& order) {
            order.set_status(OrderStatus::New); // 撤单失败，恢复原状态
        });
        spdlog::error("CTP order action failed: {} - {}", order_id, error_msg);
    }
}

void CtpBroker::on_rtn_order(const std::string& order_id, OrderStatus status, Quantity filled_qty) {
    update_order_internal(order_id, [status](Order& order) {
        order.set_status(status);
    });

    auto order_ptr = find_order_internal(order_id);
    if (order_ptr) {
        notify_order_update(*order_ptr);
    }

    spdlog::debug("CTP order return: {} status: {} filled: {}",
                 order_id, to_string(status), filled_qty);
}

void CtpBroker::on_rtn_trade(const std::string& trade_id, const std::string& order_id,
                            const AssetId& asset_id, OrderSide side, Quantity quantity, Price price) {
    // 更新订单成交信息
    update_order_internal(order_id, [quantity, price](Order& order) {
        order.add_fill(quantity, price);
    });

    // 创建交易记录
    auto order_ptr = find_order_internal(order_id);
    if (order_ptr) {
        Trade trade(trade_id, order_id, order_ptr->account_id(), asset_id, side, quantity, price);

        {
            std::unique_lock<std::shared_mutex> lock(trades_mutex_);
            trades_.push_back(trade);
        }

        // 通知交易回调
        if (trade_callback_) {
            trade_callback_(trade);
        }

        // 通知订单更新
        notify_order_update(*order_ptr);
    }

    spdlog::info("CTP trade return: {} {} {} @ {}", trade_id, quantity, asset_id, price);
}

void CtpBroker::on_rsp_qry_trading_account(const AccountBalance& balance) {
    // 更新账户信息
    std::unique_lock<std::shared_mutex> lock(accounts_mutex_);

    AccountId account_id = config_.user_id; // 使用用户ID作为账户ID
    auto it = accounts_.find(account_id);
    if (it == accounts_.end()) {
        // 创建新账户
        Account account(account_id, BrokerType::CTP, AccountType::Future);
        account.update_balance(balance);
        accounts_[account_id] = account;
    } else {
        // 更新现有账户
        it->second.update_balance(balance);
    }

    lock.unlock();

    // 通知账户更新
    if (account_callback_) {
        account_callback_(accounts_[account_id]);
    }

    spdlog::debug("CTP account updated: {} balance: {}", account_id, balance.total_balance);
}

void CtpBroker::on_rsp_qry_investor_position(const Position& position) {
    std::unique_lock<std::shared_mutex> lock(positions_mutex_);

    std::string key = position.account_id() + "_" + position.asset_id();
    positions_[key] = position;

    lock.unlock();

    // 通知持仓更新
    if (position_callback_) {
        position_callback_(position);
    }

    spdlog::debug("CTP position updated: {} {} qty: {}",
                 position.account_id(), position.asset_id(), position.quantity());
}

// 内部方法
void CtpBroker::initialize_apis() {
    // 实际实现中会创建 CTP API 实例
    // trader_api_ = CThostFtdcTraderApi::CreateFtdcTraderApi(config_.flow_path.c_str());
    // md_api_ = CThostFtdcMdApi::CreateFtdcMdApi(config_.flow_path.c_str());

    // 模拟初始化
    trader_api_ = reinterpret_cast<void*>(0x1);
    md_api_ = reinterpret_cast<void*>(0x2);

    spdlog::info("CTP APIs initialized");
}

void CtpBroker::cleanup_apis() {
    // 实际实现中会释放 CTP API 实例
    // if (trader_api_) {
    //     trader_api_->Release();
    //     trader_api_ = nullptr;
    // }
    // if (md_api_) {
    //     md_api_->Release();
    //     md_api_ = nullptr;
    // }

    trader_api_ = nullptr;
    md_api_ = nullptr;

    spdlog::info("CTP APIs cleaned up");
}

void CtpBroker::start_heartbeat() {
    if (heartbeat_running_.load()) return;

    heartbeat_running_.store(true);
    heartbeat_thread_ = std::thread(&CtpBroker::heartbeat_worker, this);
    spdlog::debug("CTP heartbeat started");
}

void CtpBroker::stop_heartbeat() {
    if (!heartbeat_running_.load()) return;

    heartbeat_running_.store(false);
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
    spdlog::debug("CTP heartbeat stopped");
}

void CtpBroker::heartbeat_worker() {
    while (heartbeat_running_.load()) {
        update_heartbeat();
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.heartbeat_interval_ms));
    }
}

// 订单管理
void CtpBroker::add_order_internal(const Order& order) {
    std::unique_lock<std::shared_mutex> lock(orders_mutex_);
    orders_[order.order_id()] = order;
}

void CtpBroker::update_order_internal(const OrderId& order_id, std::function<void(Order&)> updater) {
    std::unique_lock<std::shared_mutex> lock(orders_mutex_);
    auto it = orders_.find(order_id);
    if (it != orders_.end()) {
        updater(it->second);
    }
}

Order* CtpBroker::find_order_internal(const OrderId& order_id) {
    std::shared_lock<std::shared_mutex> lock(orders_mutex_);
    auto it = orders_.find(order_id);
    return it != orders_.end() ? &it->second : nullptr;
}

// 请求方法 (模拟实现)
int CtpBroker::req_authenticate() {
    // 实际实现中会调用 CTP API
    // CThostFtdcReqAuthenticateField req = {};
    // strncpy(req.BrokerID, config_.broker_id.c_str(), sizeof(req.BrokerID) - 1);
    // strncpy(req.UserID, config_.user_id.c_str(), sizeof(req.UserID) - 1);
    // strncpy(req.AppID, config_.app_id.c_str(), sizeof(req.AppID) - 1);
    // strncpy(req.AuthCode, config_.auth_code.c_str(), sizeof(req.AuthCode) - 1);
    // return trader_api_->ReqAuthenticate(&req, ++request_id_);

    // 模拟成功
    std::thread([this]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        on_rsp_authenticate(true, "");
    }).detach();

    return 0;
}

int CtpBroker::req_user_login() {
    // 实际实现中会调用 CTP API
    // CThostFtdcReqUserLoginField req = {};
    // strncpy(req.BrokerID, config_.broker_id.c_str(), sizeof(req.BrokerID) - 1);
    // strncpy(req.UserID, config_.user_id.c_str(), sizeof(req.UserID) - 1);
    // strncpy(req.Password, config_.password.c_str(), sizeof(req.Password) - 1);
    // strncpy(req.UserProductInfo, config_.user_product_info.c_str(), sizeof(req.UserProductInfo) - 1);
    // return trader_api_->ReqUserLogin(&req, ++request_id_);

    // 模拟成功
    std::thread([this]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        on_rsp_user_login(true, "");
    }).detach();

    return 0;
}

int CtpBroker::req_user_logout() {
    // 实际实现中会调用 CTP API
    // CThostFtdcUserLogoutField req = {};
    // strncpy(req.BrokerID, config_.broker_id.c_str(), sizeof(req.BrokerID) - 1);
    // strncpy(req.UserID, config_.user_id.c_str(), sizeof(req.UserID) - 1);
    // return trader_api_->ReqUserLogout(&req, ++request_id_);

    return 0;
}

int CtpBroker::req_order_insert(const Order& order) {
    // 实际实现中会调用 CTP API
    // CThostFtdcInputOrderField req = {};
    // strncpy(req.BrokerID, config_.broker_id.c_str(), sizeof(req.BrokerID) - 1);
    // strncpy(req.InvestorID, config_.user_id.c_str(), sizeof(req.InvestorID) - 1);
    // strncpy(req.InstrumentID, order.asset_id().c_str(), sizeof(req.InstrumentID) - 1);
    // strncpy(req.OrderRef, generate_order_ref().c_str(), sizeof(req.OrderRef) - 1);
    // req.Direction = CtpOrderStatusMap::to_ctp_direction(order.side());
    // req.OrderPriceType = CtpOrderStatusMap::to_ctp_order_type(order.type());
    // req.VolumeTotalOriginal = static_cast<int>(order.quantity());
    // if (order.price()) {
    //     req.LimitPrice = *order.price();
    // }
    // return trader_api_->ReqOrderInsert(&req, ++request_id_);

    // 模拟成功
    std::thread([this, order]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        on_rsp_order_insert(order.order_id(), true, "");

        // 模拟成交
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        on_rtn_order(order.order_id(), OrderStatus::Filled, order.quantity());

        Price fill_price = order.price() ? *order.price() : 100.0;
        on_rtn_trade(generate_trade_id(), order.order_id(), order.asset_id(),
                    order.side(), order.quantity(), fill_price);
    }).detach();

    return 0;
}

int CtpBroker::req_order_action(const Order& order) {
    // 实际实现中会调用 CTP API
    // CThostFtdcInputOrderActionField req = {};
    // strncpy(req.BrokerID, config_.broker_id.c_str(), sizeof(req.BrokerID) - 1);
    // strncpy(req.InvestorID, config_.user_id.c_str(), sizeof(req.InvestorID) - 1);
    // strncpy(req.OrderRef, order.broker_order_id().c_str(), sizeof(req.OrderRef) - 1);
    // req.ActionFlag = THOST_FTDC_AF_Delete;
    // return trader_api_->ReqOrderAction(&req, ++request_id_);

    // 模拟成功
    std::thread([this, order]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        on_rsp_order_action(order.order_id(), true, "");
        on_rtn_order(order.order_id(), OrderStatus::Cancelled, 0);
    }).detach();

    return 0;
}

int CtpBroker::req_qry_trading_account() {
    // 实际实现中会调用 CTP API
    // CThostFtdcQryTradingAccountField req = {};
    // strncpy(req.BrokerID, config_.broker_id.c_str(), sizeof(req.BrokerID) - 1);
    // strncpy(req.InvestorID, config_.user_id.c_str(), sizeof(req.InvestorID) - 1);
    // return trader_api_->ReqQryTradingAccount(&req, ++request_id_);

    // 模拟返回账户信息
    std::thread([this]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        AccountBalance balance;
        balance.total_balance = 1000000.0;
        balance.available_balance = 800000.0;
        balance.frozen_balance = 200000.0;
        balance.currency = "CNY";
        on_rsp_qry_trading_account(balance);
    }).detach();

    return 0;
}

int CtpBroker::req_qry_investor_position() {
    // 实际实现中会调用 CTP API
    // CThostFtdcQryInvestorPositionField req = {};
    // strncpy(req.BrokerID, config_.broker_id.c_str(), sizeof(req.BrokerID) - 1);
    // strncpy(req.InvestorID, config_.user_id.c_str(), sizeof(req.InvestorID) - 1);
    // return trader_api_->ReqQryInvestorPosition(&req, ++request_id_);

    return 0;
}

int CtpBroker::req_qry_order() {
    // 实际实现中会调用 CTP API
    return 0;
}

int CtpBroker::req_qry_trade() {
    // 实际实现中会调用 CTP API
    return 0;
}

// CtpBrokerFactory 实现
BrokerInterfacePtr CtpBrokerFactory::create_broker() {
    return std::make_shared<CtpBroker>();
}

BrokerInterfacePtr CtpBrokerFactory::create_broker(const BrokerConfig& config) {
    CtpConfig ctp_config;

    // 从 BrokerConfig 转换为 CtpConfig
    if (config.connection_info.broker_type == BrokerType::CTP) {
        ctp_config.front_address = config.connection_info.server_address + ":" +
                                  std::to_string(config.connection_info.port);
        ctp_config.user_id = config.connection_info.username;
        ctp_config.password = config.connection_info.password;

        // 从额外参数中获取 CTP 特有配置
        auto& params = config.connection_info.extra_params;
        if (params.find("broker_id") != params.end()) {
            ctp_config.broker_id = params.at("broker_id");
        }
        if (params.find("app_id") != params.end()) {
            ctp_config.app_id = params.at("app_id");
        }
        if (params.find("auth_code") != params.end()) {
            ctp_config.auth_code = params.at("auth_code");
        }
    }

    auto broker = std::make_shared<CtpBroker>(ctp_config);
    broker->configure(config);
    return broker;
}

bool CtpBrokerFactory::supports_config(const BrokerConfig& config) const {
    return config.broker_type == BrokerType::CTP;
}

// 工具函数
void register_ctp_broker() {
    auto factory = std::make_shared<CtpBrokerFactory>();
    BrokerRegistry::instance().register_factory(factory);
    spdlog::info("CTP Broker factory registered");
}

CtpConfig create_default_ctp_config() {
    CtpConfig config;
    config.front_address = "tcp://***************:10130";
    config.md_front_address = "tcp://***************:10131";
    config.broker_id = "9999";
    config.user_product_info = "BrokerModern";
    config.flow_path = "./ctp_flow/";
    return config;
}

std::string generate_ctp_order_ref() {
    return generate_order_ref();
}

std::string ctp_error_msg_to_string(int error_id, const std::string& error_msg) {
    std::ostringstream oss;
    oss << "CTP Error [" << error_id << "]: " << error_msg;
    return oss.str();
}

} // namespace RoboQuant::Broker
