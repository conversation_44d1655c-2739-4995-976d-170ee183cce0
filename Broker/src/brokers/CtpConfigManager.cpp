#include "brokers/CtpConfigManager.h"
#include <spdlog/spdlog.h>
#include <fstream>
#include <algorithm>
#include <regex>

namespace RoboQuant::Broker {

// CtpEnvironment 转换函数
std::string to_string(CtpEnvironment env) {
    switch (env) {
        case CtpEnvironment::Production: return "Production";
        case CtpEnvironment::Simulation: return "Simulation";
        case CtpEnvironment::Testing: return "Testing";
        case CtpEnvironment::Development: return "Development";
        default: return "Unknown";
    }
}

CtpEnvironment parse_ctp_environment(const std::string& str) {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "production" || lower_str == "prod") return CtpEnvironment::Production;
    if (lower_str == "simulation" || lower_str == "sim") return CtpEnvironment::Simulation;
    if (lower_str == "testing" || lower_str == "test") return CtpEnvironment::Testing;
    if (lower_str == "development" || lower_str == "dev") return CtpEnvironment::Development;
    
    return CtpEnvironment::Production; // 默认生产环境
}

// CtpServerConfig 实现
nlohmann::json CtpServerConfig::to_json() const {
    nlohmann::json j;
    j["name"] = name;
    j["trade_front"] = trade_front;
    j["md_front"] = md_front;
    j["broker_id"] = broker_id;
    j["environment"] = to_string(environment);
    j["is_active"] = is_active;
    j["priority"] = priority;
    return j;
}

CtpServerConfig CtpServerConfig::from_json(const nlohmann::json& j) {
    CtpServerConfig config;
    config.name = j.value("name", "");
    config.trade_front = j.value("trade_front", "");
    config.md_front = j.value("md_front", "");
    config.broker_id = j.value("broker_id", "");
    config.environment = parse_ctp_environment(j.value("environment", "Production"));
    config.is_active = j.value("is_active", true);
    config.priority = j.value("priority", 0);
    return config;
}

// CtpAccountConfig 实现
nlohmann::json CtpAccountConfig::to_json() const {
    nlohmann::json j;
    j["account_id"] = account_id;
    j["user_id"] = user_id;
    j["password"] = password; // 实际使用中应该加密
    j["app_id"] = app_id;
    j["auth_code"] = auth_code;
    j["server_name"] = server_name;
    j["is_active"] = is_active;
    j["max_position_value"] = max_position_value;
    j["max_order_value"] = max_order_value;
    j["max_orders_per_second"] = max_orders_per_second;
    return j;
}

CtpAccountConfig CtpAccountConfig::from_json(const nlohmann::json& j) {
    CtpAccountConfig config;
    config.account_id = j.value("account_id", "");
    config.user_id = j.value("user_id", "");
    config.password = j.value("password", "");
    config.app_id = j.value("app_id", "");
    config.auth_code = j.value("auth_code", "");
    config.server_name = j.value("server_name", "");
    config.is_active = j.value("is_active", true);
    config.max_position_value = j.value("max_position_value", 1000000.0);
    config.max_order_value = j.value("max_order_value", 100000.0);
    config.max_orders_per_second = j.value("max_orders_per_second", 10);
    return config;
}

// CtpTradingConfig 实现
nlohmann::json CtpTradingConfig::to_json() const {
    nlohmann::json j;
    j["request_interval_ms"] = request_interval_ms;
    j["max_retry_count"] = max_retry_count;
    j["retry_interval_ms"] = retry_interval_ms;
    j["connect_timeout_ms"] = connect_timeout_ms;
    j["heartbeat_interval_ms"] = heartbeat_interval_ms;
    j["auto_reconnect"] = auto_reconnect;
    j["max_reconnect_attempts"] = max_reconnect_attempts;
    j["reconnect_interval_ms"] = reconnect_interval_ms;
    j["query_interval_ms"] = query_interval_ms;
    j["auto_query_account"] = auto_query_account;
    j["auto_query_position"] = auto_query_position;
    j["account_query_interval_ms"] = account_query_interval_ms;
    j["position_query_interval_ms"] = position_query_interval_ms;
    j["enable_order_validation"] = enable_order_validation;
    j["enable_risk_check"] = enable_risk_check;
    j["order_timeout_ms"] = order_timeout_ms;
    j["enable_market_data"] = enable_market_data;
    j["md_heartbeat_interval_ms"] = md_heartbeat_interval_ms;
    return j;
}

CtpTradingConfig CtpTradingConfig::from_json(const nlohmann::json& j) {
    CtpTradingConfig config;
    config.request_interval_ms = j.value("request_interval_ms", 1000);
    config.max_retry_count = j.value("max_retry_count", 3);
    config.retry_interval_ms = j.value("retry_interval_ms", 5000);
    config.connect_timeout_ms = j.value("connect_timeout_ms", 10000);
    config.heartbeat_interval_ms = j.value("heartbeat_interval_ms", 30000);
    config.auto_reconnect = j.value("auto_reconnect", true);
    config.max_reconnect_attempts = j.value("max_reconnect_attempts", 5);
    config.reconnect_interval_ms = j.value("reconnect_interval_ms", 5000);
    config.query_interval_ms = j.value("query_interval_ms", 1000);
    config.auto_query_account = j.value("auto_query_account", true);
    config.auto_query_position = j.value("auto_query_position", true);
    config.account_query_interval_ms = j.value("account_query_interval_ms", 5000);
    config.position_query_interval_ms = j.value("position_query_interval_ms", 10000);
    config.enable_order_validation = j.value("enable_order_validation", true);
    config.enable_risk_check = j.value("enable_risk_check", true);
    config.order_timeout_ms = j.value("order_timeout_ms", 30000);
    config.enable_market_data = j.value("enable_market_data", true);
    config.md_heartbeat_interval_ms = j.value("md_heartbeat_interval_ms", 30000);
    return config;
}

// CtpConfigManager 实现
CtpConfigManager& CtpConfigManager::instance() {
    static CtpConfigManager manager;
    return manager;
}

Result<void> CtpConfigManager::load_config(const std::filesystem::path& config_file) {
    try {
        if (!std::filesystem::exists(config_file)) {
            return Result<void>::failure(ErrorCode::InvalidParameter, 
                "Config file not found: " + config_file.string());
        }
        
        std::ifstream file(config_file);
        if (!file.is_open()) {
            return Result<void>::failure(ErrorCode::InvalidParameter, 
                "Cannot open config file: " + config_file.string());
        }
        
        nlohmann::json config_json;
        file >> config_json;
        
        std::unique_lock<std::shared_mutex> lock(config_mutex_);
        
        // 加载服务器配置
        if (config_json.contains("servers")) {
            server_configs_.clear();
            for (const auto& server_json : config_json["servers"]) {
                auto server_config = CtpServerConfig::from_json(server_json);
                server_configs_[server_config.name] = server_config;
            }
        }
        
        // 加载账户配置
        if (config_json.contains("accounts")) {
            account_configs_.clear();
            for (const auto& account_json : config_json["accounts"]) {
                auto account_config = CtpAccountConfig::from_json(account_json);
                account_configs_[account_config.account_id] = account_config;
            }
        }
        
        // 加载交易配置
        if (config_json.contains("trading")) {
            trading_config_ = CtpTradingConfig::from_json(config_json["trading"]);
        }
        
        // 加载环境设置
        if (config_json.contains("environment")) {
            current_environment_ = parse_ctp_environment(config_json["environment"]);
        }
        
        config_file_path_ = config_file;
        last_config_time_ = std::filesystem::last_write_time(config_file);
        
        lock.unlock();
        
        spdlog::info("CTP config loaded from: {}", config_file.string());
        notify_config_change("config", "loaded");
        
        return Result<void>::success();
        
    } catch (const std::exception& e) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Failed to load config: " + std::string(e.what()));
    }
}

Result<void> CtpConfigManager::save_config(const std::filesystem::path& config_file) const {
    try {
        std::shared_lock<std::shared_mutex> lock(config_mutex_);
        
        nlohmann::json config_json;
        
        // 保存服务器配置
        config_json["servers"] = nlohmann::json::array();
        for (const auto& [name, server_config] : server_configs_) {
            config_json["servers"].push_back(server_config.to_json());
        }
        
        // 保存账户配置
        config_json["accounts"] = nlohmann::json::array();
        for (const auto& [account_id, account_config] : account_configs_) {
            config_json["accounts"].push_back(account_config.to_json());
        }
        
        // 保存交易配置
        config_json["trading"] = trading_config_.to_json();
        
        // 保存环境设置
        config_json["environment"] = to_string(current_environment_);
        
        lock.unlock();
        
        std::ofstream file(config_file);
        if (!file.is_open()) {
            return Result<void>::failure(ErrorCode::InvalidParameter, 
                "Cannot create config file: " + config_file.string());
        }
        
        file << config_json.dump(4);
        
        spdlog::info("CTP config saved to: {}", config_file.string());
        return Result<void>::success();
        
    } catch (const std::exception& e) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Failed to save config: " + std::string(e.what()));
    }
}

Result<void> CtpConfigManager::reload_config() {
    if (config_file_path_.empty()) {
        return Result<void>::failure(ErrorCode::InvalidParameter, "No config file loaded");
    }
    
    return load_config(config_file_path_);
}

void CtpConfigManager::add_server_config(const CtpServerConfig& config) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    server_configs_[config.name] = config;
    lock.unlock();
    
    notify_config_change("server", config.name);
    spdlog::info("CTP server config added: {}", config.name);
}

void CtpConfigManager::remove_server_config(const std::string& name) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    server_configs_.erase(name);
    lock.unlock();
    
    notify_config_change("server", name);
    spdlog::info("CTP server config removed: {}", name);
}

void CtpConfigManager::update_server_config(const std::string& name, const CtpServerConfig& config) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    server_configs_[name] = config;
    lock.unlock();
    
    notify_config_change("server", name);
    spdlog::info("CTP server config updated: {}", name);
}

std::optional<CtpServerConfig> CtpConfigManager::get_server_config(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    auto it = server_configs_.find(name);
    return it != server_configs_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::vector<CtpServerConfig> CtpConfigManager::get_all_server_configs() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    std::vector<CtpServerConfig> result;
    for (const auto& [name, config] : server_configs_) {
        result.push_back(config);
    }
    return result;
}

std::vector<CtpServerConfig> CtpConfigManager::get_active_server_configs() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    std::vector<CtpServerConfig> result;
    for (const auto& [name, config] : server_configs_) {
        if (config.is_active) {
            result.push_back(config);
        }
    }
    return result;
}

CtpServerConfig CtpConfigManager::get_best_server_config(CtpEnvironment env) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    
    std::vector<CtpServerConfig> candidates;
    for (const auto& [name, config] : server_configs_) {
        if (config.is_active && config.environment == env) {
            candidates.push_back(config);
        }
    }
    
    if (candidates.empty()) {
        // 如果没有找到指定环境的服务器，返回默认配置
        if (env == CtpEnvironment::Production) {
            return create_production_server_template();
        } else {
            return create_simulation_server_template();
        }
    }
    
    // 按优先级排序，返回优先级最高的
    std::sort(candidates.begin(), candidates.end(), 
              [](const CtpServerConfig& a, const CtpServerConfig& b) {
                  return a.priority < b.priority;
              });
    
    return candidates[0];
}

void CtpConfigManager::add_account_config(const CtpAccountConfig& config) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    account_configs_[config.account_id] = config;
    lock.unlock();
    
    notify_config_change("account", config.account_id);
    spdlog::info("CTP account config added: {}", config.account_id);
}

void CtpConfigManager::remove_account_config(const std::string& account_id) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    account_configs_.erase(account_id);
    lock.unlock();
    
    notify_config_change("account", account_id);
    spdlog::info("CTP account config removed: {}", account_id);
}

void CtpConfigManager::update_account_config(const std::string& account_id, const CtpAccountConfig& config) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    account_configs_[account_id] = config;
    lock.unlock();
    
    notify_config_change("account", account_id);
    spdlog::info("CTP account config updated: {}", account_id);
}

std::optional<CtpAccountConfig> CtpConfigManager::get_account_config(const std::string& account_id) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    auto it = account_configs_.find(account_id);
    return it != account_configs_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::vector<CtpAccountConfig> CtpConfigManager::get_all_account_configs() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    std::vector<CtpAccountConfig> result;
    for (const auto& [account_id, config] : account_configs_) {
        result.push_back(config);
    }
    return result;
}

std::vector<CtpAccountConfig> CtpConfigManager::get_active_account_configs() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    std::vector<CtpAccountConfig> result;
    for (const auto& [account_id, config] : account_configs_) {
        if (config.is_active) {
            result.push_back(config);
        }
    }
    return result;
}

void CtpConfigManager::set_trading_config(const CtpTradingConfig& config) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    trading_config_ = config;
    lock.unlock();
    
    notify_config_change("trading", "config");
    spdlog::info("CTP trading config updated");
}

CtpTradingConfig CtpConfigManager::get_trading_config() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    return trading_config_;
}

void CtpConfigManager::set_current_environment(CtpEnvironment env) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    current_environment_ = env;
    lock.unlock();
    
    notify_config_change("environment", to_string(env));
    spdlog::info("CTP environment changed to: {}", to_string(env));
}

CtpEnvironment CtpConfigManager::get_current_environment() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    return current_environment_;
}

CtpConfig CtpConfigManager::generate_ctp_config(const std::string& account_id) const {
    auto account_config = get_account_config(account_id);
    if (!account_config) {
        throw std::runtime_error("Account config not found: " + account_id);
    }
    
    auto server_config = get_server_config(account_config->server_name);
    if (!server_config) {
        server_config = get_best_server_config(current_environment_);
    }
    
    return generate_ctp_config(*account_config, *server_config);
}

CtpConfig CtpConfigManager::generate_ctp_config(const CtpAccountConfig& account_config, 
                                               const CtpServerConfig& server_config) const {
    CtpConfig config;
    
    // 服务器配置
    config.front_address = server_config.trade_front;
    config.md_front_address = server_config.md_front;
    config.broker_id = server_config.broker_id;
    
    // 账户配置
    config.user_id = account_config.user_id;
    config.password = account_config.password;
    config.app_id = account_config.app_id;
    config.auth_code = account_config.auth_code;
    
    // 交易配置
    auto trading_config = get_trading_config();
    config.connect_timeout_ms = trading_config.connect_timeout_ms;
    config.heartbeat_interval_ms = trading_config.heartbeat_interval_ms;
    config.auto_reconnect = trading_config.auto_reconnect;
    config.max_reconnect_attempts = trading_config.max_reconnect_attempts;
    config.enable_market_data = trading_config.enable_market_data;
    config.enable_trading = true;
    config.request_interval_ms = trading_config.request_interval_ms;
    
    // 流文件路径
    config.flow_path = "./ctp_flow/" + account_config.account_id + "/";
    
    return config;
}

// 配置模板
CtpServerConfig CtpConfigManager::create_production_server_template() {
    CtpServerConfig config;
    config.name = "Production_Server";
    config.trade_front = "tcp://180.168.146.187:10130";
    config.md_front = "tcp://180.168.146.187:10131";
    config.broker_id = "9999";
    config.environment = CtpEnvironment::Production;
    config.is_active = true;
    config.priority = 0;
    return config;
}

CtpServerConfig CtpConfigManager::create_simulation_server_template() {
    CtpServerConfig config;
    config.name = "Simulation_Server";
    config.trade_front = "tcp://180.168.146.187:10201";
    config.md_front = "tcp://180.168.146.187:10211";
    config.broker_id = "9999";
    config.environment = CtpEnvironment::Simulation;
    config.is_active = true;
    config.priority = 0;
    return config;
}

CtpAccountConfig CtpConfigManager::create_account_template() {
    CtpAccountConfig config;
    config.account_id = "template_account";
    config.user_id = "";
    config.password = "";
    config.app_id = "";
    config.auth_code = "";
    config.server_name = "Production_Server";
    config.is_active = false;
    config.max_position_value = 1000000.0;
    config.max_order_value = 100000.0;
    config.max_orders_per_second = 10;
    return config;
}

CtpTradingConfig CtpConfigManager::create_trading_template() {
    return CtpTradingConfig{}; // 使用默认值
}

void CtpConfigManager::notify_config_change(const std::string& type, const std::string& name) {
    if (change_callback_) {
        try {
            change_callback_(type, name);
        } catch (const std::exception& e) {
            spdlog::error("Exception in config change callback: {}", e.what());
        }
    }
}

void CtpConfigManager::set_config_change_callback(ConfigChangeCallback callback) {
    change_callback_ = std::move(callback);
}

} // namespace RoboQuant::Broker
