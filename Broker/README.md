# Broker_Modern - 现代化交易接口模块

## 项目概述

Broker_Modern 是基于 C++20 标准重构的现代化交易接口模块，旨在替代原有的 Broker 模块，提供更加安全、高效和可维护的交易功能。

## 核心特性

### ? 现代化 C++20 设计
- **强类型系统**: 使用 enum class、concepts 和类型别名确保类型安全
- **异步处理**: 基于事件驱动的异步架构，支持高并发交易
- **内存安全**: 使用智能指针和 RAII 原则，避免内存泄漏
- **错误处理**: 统一的 Result<T> 类型和异常安全设计

### ? 插件式架构
- **统一接口**: BrokerInterface 抽象层支持多种交易接口
- **工厂模式**: BrokerFactory 和 BrokerRegistry 实现插件式管理
- **动态加载**: 支持运行时加载和卸载交易接口插件
- **配置驱动**: JSON 配置文件驱动的灵活配置管理

### ? 完整的交易功能
- **订单管理**: 支持多种订单类型（市价、限价、止损等）
- **账户管理**: 多账户支持，实时资金和风险管理
- **持仓跟踪**: 实时持仓更新和 P&L 计算
- **交易记录**: 完整的交易历史和审计跟踪

### ?? 风险控制
- **实时风险监控**: 动态风险指标计算和限制检查
- **多层验证**: 订单提交前的多重验证机制
- **资金管理**: 可用资金、保证金和杠杆控制
- **限额管理**: 可配置的交易限额和风险参数

## 架构设计

### 核心模块

```
Broker_Modern/
├── include/
│   ├── core/                 # 核心类型和系统
│   │   ├── Types.h          # 基础类型定义
│   │   ├── EventSystem.h    # 事件系统
│   │   ├── ErrorHandling.h  # 错误处理
│   │   └── Configuration.h  # 配置管理
│   ├── orders/              # 订单管理
│   │   ├── Order.h          # 订单类定义
│   │   └── OrderManager.h   # 订单管理器
│   ├── accounts/            # 账户管理
│   │   ├── Account.h        # 账户类定义
│   │   └── AccountManager.h # 账户管理器
│   ├── brokers/             # 交易接口
│   │   ├── BrokerInterface.h    # 抽象接口
│   │   ├── SimulationBroker.h   # 模拟交易
│   │   ├── CtpBroker.h          # CTP 接口
│   │   ├── IbBroker.h           # IB 接口
│   │   └── FixBroker.h          # FIX 接口
│   └── utils/               # 工具类
│       ├── ThreadSafety.h   # 线程安全工具
│       └── Logging.h        # 日志系统
└── src/                     # 实现文件
```

### 类型系统

#### 现代化枚举类
```cpp
enum class BrokerType : uint8_t {
    CTP = 0, IB = 1, FIX = 2, Simulation = 3, XTP = 4, Unknown = 255
};

enum class OrderStatus : uint8_t {
    PendingNew = 0, New = 1, PartiallyFilled = 2, Filled = 3,
    PendingCancel = 4, Cancelled = 5, Rejected = 6, Expired = 7, Unknown = 255
};
```

#### 概念约束
```cpp
template<typename T>
concept OrderLike = requires(T t) {
    { t.order_id() } -> std::convertible_to<OrderId>;
    { t.asset_id() } -> std::convertible_to<AssetId>;
    { t.side() } -> std::convertible_to<OrderSide>;
    { t.quantity() } -> std::convertible_to<Quantity>;
};
```

#### 错误处理
```cpp
template<typename T>
using Result = std::variant<T, ErrorCode>;

// 使用示例
Result<OrderId> submit_order(const OrderRequest& request);
```

### 事件系统

#### 异步事件处理
```cpp
class EventDispatcher {
public:
    void publish(EventPtr event);
    void subscribe(EventType type, EventHandlerPtr handler);
    std::future<EventPtr> wait_for_event_async(EventType type);
};
```

#### 事件类型
- **OrderEvent**: 订单状态更新
- **TradeEvent**: 交易成交通知
- **AccountEvent**: 账户资金变化
- **ConnectionEvent**: 连接状态变化
- **ErrorEvent**: 错误事件通知

### 订单管理

#### 订单生命周期
```cpp
class OrderManager {
public:
    Result<OrderId> submit_order(const OrderRequest& request);
    Result<void> cancel_order(const OrderId& order_id);
    std::vector<Order> get_active_orders() const;
    void update_order_status(const OrderId& order_id, OrderStatus status);
};
```

#### 订单验证
- 基础参数验证
- 风险限制检查
- 账户资金验证
- 自定义验证器支持

### 交易接口抽象

#### 统一接口设计
```cpp
class BrokerInterface {
public:
    virtual Result<void> submit_order(const Order& order) = 0;
    virtual Result<void> cancel_order(const Order& order) = 0;
    virtual Result<std::vector<Order>> query_orders(const AccountId& account_id) = 0;
    virtual Result<std::vector<Account>> query_accounts() = 0;
};
```

#### 插件式管理
```cpp
class BrokerRegistry {
public:
    void register_factory(BrokerFactoryPtr factory);
    BrokerInterfacePtr create_broker(BrokerType broker_type);
    BrokerInterfacePtr create_broker(const BrokerConfig& config);
};
```

## 与原有系统的兼容性

### 数据格式兼容
- 保持与原有 Order、Account 等数据结构的兼容性
- 提供数据转换工具和适配器
- 支持渐进式迁移

### 接口兼容
- 提供原有 TbMsgCallback 和 TbQueryBrokerDataCallback 的适配层
- 保持原有 BrokerType 枚举值的兼容性
- 支持原有配置文件格式

### 集成方式
```cpp
// 兼容性适配器
class LegacyBrokerAdapter {
public:
    void register_tb_msg_callback(const TbMsgCallback& callback);
    void register_tb_query_broker_data_callback(const TbQueryBrokerDataCallback& callback);
    bool submit_order(const ::Order& legacy_order);
};
```

## 性能优化

### 无锁数据结构
- 使用 concurrentqueue 库实现高性能消息队列
- 原子操作减少锁竞争
- 内存池管理减少动态分配

### 异步处理
- 事件驱动的非阻塞架构
- 线程池管理并发任务
- 批量处理提高吞吐量

### 内存管理
- 智能指针自动管理生命周期
- 对象池复用减少分配开销
- 移动语义减少拷贝成本

## 安全性设计

### 线程安全
- 使用 std::shared_mutex 实现读写锁
- 原子操作保证数据一致性
- 无锁数据结构避免死锁

### 异常安全
- RAII 原则确保资源释放
- 异常中性的接口设计
- 强异常安全保证

### 类型安全
- 强类型系统避免类型错误
- 概念约束编译期检查
- 枚举类避免隐式转换

## 编译和构建

### 依赖要求
- C++20 兼容编译器 (GCC 10+, Clang 12+, MSVC 2019+)
- CMake 3.20+
- Boost 1.75+
- nlohmann/json 3.9+
- spdlog 1.8+

### 构建步骤
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --parallel
```

### 测试运行
```bash
ctest --parallel --output-on-failure
```

## 使用示例

### 基本使用
```cpp
#include "brokers/SimulationBroker.h"
#include "orders/OrderManager.h"

// 创建模拟交易接口
auto broker = std::make_shared<SimulationBroker>();
broker->connect({BrokerType::Simulation, "", 0});

// 创建订单管理器
OrderManager order_manager;
order_manager.set_broker_interface(broker);
order_manager.start_event_handling();

// 提交订单
OrderRequest request;
request.asset_id = "000001.SZ";
request.side = OrderSide::Buy;
request.type = OrderType::Limit;
request.quantity = 1000;
request.price = 10.50;
request.account_id = "test_account";

auto result = order_manager.submit_order(request);
if (is_success(result)) {
    auto order_id = get_value(result);
    std::cout << "Order submitted: " << order_id << std::endl;
}
```

## 后续开发计划

### 短期目标
- [ ] 完成 CTP 接口实现
- [ ] 实现配置管理系统
- [ ] 添加单元测试覆盖
- [ ] 完善错误处理机制

### 中期目标
- [ ] 实现 IB 和 FIX 接口
- [ ] 添加性能基准测试
- [ ] 实现持久化存储
- [ ] 集成到 QuantServices

### 长期目标
- [ ] 支持更多交易接口
- [ ] 实现高级订单类型
- [ ] 添加机器学习风控
- [ ] 云原生部署支持

## 贡献指南

欢迎贡献代码和建议！请遵循以下原则：
- 遵循现代 C++ 最佳实践
- 保持代码简洁和可读性
- 添加充分的单元测试
- 更新相关文档

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
