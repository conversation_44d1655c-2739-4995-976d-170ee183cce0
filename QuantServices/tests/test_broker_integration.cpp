#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "services/BrokerService.h"
#include "api/BrokerEndpoints.h"
#include "quantservices/ServiceManager.h"
#include <nlohmann/json.hpp>
#include <thread>
#include <chrono>

using namespace QuantServices;
using namespace QuantServices::API;
using namespace RoboQuant::Broker;

class BrokerIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试配置
        nlohmann::json config;
        config["auto_connect_on_start"] = false;
        config["enable_monitoring"] = true;
        config["monitoring_interval_seconds"] = 1;
        
        // 创建 BrokerService
        broker_service = std::make_shared<BrokerService>();
        auto init_result = broker_service->initialize(config);
        ASSERT_TRUE(init_result.has_value()) << "Failed to initialize BrokerService: " << init_result.error().message;
        
        auto start_result = broker_service->start();
        ASSERT_TRUE(start_result.has_value()) << "Failed to start BrokerService: " << start_result.error().message;
        
        // 创建 API 端点
        broker_endpoints = std::make_unique<BrokerEndpoints>(broker_service);
        
        // 等待服务启动
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    void TearDown() override {
        if (broker_service) {
            broker_service->stop();
        }
    }
    
    std::shared_ptr<BrokerService> broker_service;
    std::unique_ptr<BrokerEndpoints> broker_endpoints;
};

TEST_F(BrokerIntegrationTest, ServiceInitialization) {
    EXPECT_EQ(ServiceStatus::Running, broker_service->get_status());
    EXPECT_EQ("BrokerService", broker_service->get_service_name());
    EXPECT_EQ("1.0.0", broker_service->get_service_version());
}

TEST_F(BrokerIntegrationTest, AddSimulationBroker) {
    // 创建模拟 Broker 配置
    BrokerConnectionInfo broker_info;
    broker_info.broker_id = "test_simulation";
    broker_info.broker_type = BrokerType::Simulation;
    broker_info.name = "Test Simulation Broker";
    broker_info.description = "Test broker for integration testing";
    broker_info.is_active = true;
    broker_info.auto_connect = false;
    
    broker_info.connection_info.broker_type = BrokerType::Simulation;
    broker_info.connection_info.server_address = "localhost";
    broker_info.connection_info.port = 0;
    broker_info.connection_info.username = "test_user";
    broker_info.connection_info.password = "test_pass";
    
    // 添加 Broker
    auto add_result = broker_service->add_broker(broker_info);
    ASSERT_TRUE(add_result.has_value()) << "Failed to add broker: " << add_result.error().message;
    EXPECT_EQ("test_simulation", add_result.value());
    
    // 验证 Broker 已添加
    auto brokers = broker_service->get_all_brokers();
    EXPECT_EQ(1, brokers.size());
    EXPECT_EQ("test_simulation", brokers[0].broker_id);
    EXPECT_EQ(BrokerType::Simulation, brokers[0].broker_type);
}

TEST_F(BrokerIntegrationTest, ConnectAndAuthenticateBroker) {
    // 添加模拟 Broker
    BrokerConnectionInfo broker_info;
    broker_info.broker_id = "test_simulation";
    broker_info.broker_type = BrokerType::Simulation;
    broker_info.name = "Test Simulation Broker";
    broker_info.is_active = true;
    broker_info.connection_info.broker_type = BrokerType::Simulation;
    broker_info.connection_info.server_address = "localhost";
    broker_info.connection_info.port = 0;
    broker_info.connection_info.username = "test_user";
    broker_info.connection_info.password = "test_pass";
    
    auto add_result = broker_service->add_broker(broker_info);
    ASSERT_TRUE(add_result.has_value());
    
    // 连接 Broker
    auto connect_result = broker_service->connect_broker("test_simulation");
    ASSERT_TRUE(connect_result.has_value()) << "Failed to connect broker: " << connect_result.error().message;
    
    // 等待连接完成
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 验证连接状态
    auto connected_brokers = broker_service->get_connected_brokers();
    EXPECT_EQ(1, connected_brokers.size());
    EXPECT_EQ("test_simulation", connected_brokers[0]);
    
    // 认证 Broker
    auto auth_result = broker_service->authenticate_broker("test_simulation", "test_user", "test_pass");
    ASSERT_TRUE(auth_result.has_value()) << "Failed to authenticate broker: " << auth_result.error().message;
    
    // 等待认证完成
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 验证认证状态
    auto authenticated_brokers = broker_service->get_authenticated_brokers();
    EXPECT_EQ(1, authenticated_brokers.size());
    EXPECT_EQ("test_simulation", authenticated_brokers[0]);
}

TEST_F(BrokerIntegrationTest, SubmitAndCancelOrder) {
    // 设置 Broker
    BrokerConnectionInfo broker_info;
    broker_info.broker_id = "test_simulation";
    broker_info.broker_type = BrokerType::Simulation;
    broker_info.name = "Test Simulation Broker";
    broker_info.is_active = true;
    broker_info.connection_info.broker_type = BrokerType::Simulation;
    broker_info.connection_info.server_address = "localhost";
    broker_info.connection_info.port = 0;
    broker_info.connection_info.username = "test_user";
    broker_info.connection_info.password = "test_pass";
    
    broker_service->add_broker(broker_info);
    broker_service->connect_broker("test_simulation");
    broker_service->authenticate_broker("test_simulation", "test_user", "test_pass");
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 创建订单请求
    OrderRequest order_request;
    order_request.asset_id = "TEST_ASSET";
    order_request.side = OrderSide::Buy;
    order_request.type = OrderType::Limit;
    order_request.quantity = 100;
    order_request.price = 50.0;
    order_request.account_id = "test_account";
    order_request.strategy_id = "test_strategy";
    
    // 提交订单
    auto submit_result = broker_service->submit_order("test_simulation", order_request);
    ASSERT_TRUE(submit_result.has_value()) << "Failed to submit order: " << submit_result.error().message;
    
    std::string order_id = submit_result.value();
    EXPECT_FALSE(order_id.empty());
    
    // 等待订单处理
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 查询订单
    auto order_result = broker_service->query_order("test_simulation", order_id);
    ASSERT_TRUE(order_result.has_value()) << "Failed to query order: " << order_result.error().message;
    
    const auto& order = order_result.value();
    EXPECT_EQ(order_id, order.order_id());
    EXPECT_EQ("TEST_ASSET", order.asset_id());
    EXPECT_EQ(OrderSide::Buy, order.side());
    EXPECT_EQ(100, order.quantity());
    
    // 撤销订单
    auto cancel_result = broker_service->cancel_order("test_simulation", order_id);
    ASSERT_TRUE(cancel_result.has_value()) << "Failed to cancel order: " << cancel_result.error().message;
    
    // 等待撤单处理
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 验证订单状态
    auto updated_order_result = broker_service->query_order("test_simulation", order_id);
    ASSERT_TRUE(updated_order_result.has_value());
    
    const auto& updated_order = updated_order_result.value();
    EXPECT_TRUE(updated_order.status() == OrderStatus::Cancelled || 
                updated_order.status() == OrderStatus::PendingCancel);
}

TEST_F(BrokerIntegrationTest, QueryAccountsAndPositions) {
    // 设置 Broker
    BrokerConnectionInfo broker_info;
    broker_info.broker_id = "test_simulation";
    broker_info.broker_type = BrokerType::Simulation;
    broker_info.name = "Test Simulation Broker";
    broker_info.is_active = true;
    broker_info.connection_info.broker_type = BrokerType::Simulation;
    broker_info.connection_info.server_address = "localhost";
    broker_info.connection_info.port = 0;
    broker_info.connection_info.username = "test_user";
    broker_info.connection_info.password = "test_pass";
    
    broker_service->add_broker(broker_info);
    broker_service->connect_broker("test_simulation");
    broker_service->authenticate_broker("test_simulation", "test_user", "test_pass");
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 查询账户
    auto accounts_result = broker_service->query_accounts("test_simulation");
    ASSERT_TRUE(accounts_result.has_value()) << "Failed to query accounts: " << accounts_result.error().message;
    
    const auto& accounts = accounts_result.value();
    EXPECT_FALSE(accounts.empty());
    
    if (!accounts.empty()) {
        const auto& account = accounts[0];
        EXPECT_FALSE(account.account_id().empty());
        EXPECT_EQ(BrokerType::Simulation, account.broker_type());
        EXPECT_GT(account.balance().total_balance, 0);
    }
    
    // 查询持仓
    auto positions_result = broker_service->query_positions("test_simulation");
    ASSERT_TRUE(positions_result.has_value()) << "Failed to query positions: " << positions_result.error().message;
    
    // 持仓可能为空，这是正常的
    const auto& positions = positions_result.value();
    // 只验证查询成功，不验证具体内容
}

TEST_F(BrokerIntegrationTest, PerformanceMonitoring) {
    // 添加 Broker
    BrokerConnectionInfo broker_info;
    broker_info.broker_id = "test_simulation";
    broker_info.broker_type = BrokerType::Simulation;
    broker_info.name = "Test Simulation Broker";
    broker_info.is_active = true;
    broker_info.connection_info.broker_type = BrokerType::Simulation;
    broker_info.connection_info.server_address = "localhost";
    broker_info.connection_info.port = 0;
    broker_info.connection_info.username = "test_user";
    broker_info.connection_info.password = "test_pass";
    
    broker_service->add_broker(broker_info);
    broker_service->connect_broker("test_simulation");
    broker_service->authenticate_broker("test_simulation", "test_user", "test_pass");
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 获取性能统计
    auto performance_stats = broker_service->get_performance_stats();
    EXPECT_TRUE(performance_stats.is_object());
    
    // 获取 Broker 性能
    auto broker_performance = broker_service->get_broker_performance("test_simulation");
    EXPECT_TRUE(broker_performance.is_object());
    
    // 健康检查
    auto health_result = broker_service->health_check();
    EXPECT_TRUE(health_result.has_value());
    
    auto broker_health_result = broker_service->health_check_broker("test_simulation");
    EXPECT_TRUE(broker_health_result.has_value());
}

TEST_F(BrokerIntegrationTest, ServiceManagerIntegration) {
    // 创建 ServiceManager
    ServiceManager service_manager;
    
    // 创建配置
    nlohmann::json config;
    config["services"]["broker"]["enabled"] = true;
    config["broker"]["auto_connect_on_start"] = false;
    config["broker"]["enable_monitoring"] = true;
    
    // 初始化服务
    bool init_result = service_manager.initialize(config);
    EXPECT_TRUE(init_result);
    
    // 启动服务
    bool start_result = service_manager.start_all();
    EXPECT_TRUE(start_result);
    
    // 获取 BrokerService
    auto broker_service_from_manager = service_manager.get_broker_service();
    EXPECT_TRUE(broker_service_from_manager != nullptr);
    
    if (broker_service_from_manager) {
        EXPECT_EQ(ServiceStatus::Running, broker_service_from_manager->get_status());
    }
    
    // 停止服务
    bool stop_result = service_manager.stop_all();
    EXPECT_TRUE(stop_result);
}

// 性能测试
TEST_F(BrokerIntegrationTest, PerformanceTest) {
    // 设置 Broker
    BrokerConnectionInfo broker_info;
    broker_info.broker_id = "test_simulation";
    broker_info.broker_type = BrokerType::Simulation;
    broker_info.name = "Test Simulation Broker";
    broker_info.is_active = true;
    broker_info.connection_info.broker_type = BrokerType::Simulation;
    broker_info.connection_info.server_address = "localhost";
    broker_info.connection_info.port = 0;
    broker_info.connection_info.username = "test_user";
    broker_info.connection_info.password = "test_pass";
    
    broker_service->add_broker(broker_info);
    broker_service->connect_broker("test_simulation");
    broker_service->authenticate_broker("test_simulation", "test_user", "test_pass");
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 批量提交订单测试
    const int order_count = 100;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    std::vector<std::string> order_ids;
    for (int i = 0; i < order_count; ++i) {
        OrderRequest order_request;
        order_request.asset_id = "TEST_ASSET_" + std::to_string(i);
        order_request.side = (i % 2 == 0) ? OrderSide::Buy : OrderSide::Sell;
        order_request.type = OrderType::Limit;
        order_request.quantity = 100;
        order_request.price = 50.0 + i * 0.1;
        order_request.account_id = "test_account";
        order_request.strategy_id = "performance_test";
        
        auto submit_result = broker_service->submit_order("test_simulation", order_request);
        if (submit_result.has_value()) {
            order_ids.push_back(submit_result.value());
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(order_count, order_ids.size());
    EXPECT_LT(duration.count(), 5000); // 应该在 5 秒内完成
    
    double orders_per_second = static_cast<double>(order_count) / (duration.count() / 1000.0);
    std::cout << "Performance: " << orders_per_second << " orders/second" << std::endl;
    
    // 等待订单处理
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 验证所有订单都已提交
    auto orders_result = broker_service->query_orders("test_simulation");
    ASSERT_TRUE(orders_result.has_value());
    
    const auto& orders = orders_result.value();
    EXPECT_GE(orders.size(), order_count);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
