#pragma once

#include "core/Service.h"
#include "core/Types.h"
#include "core/Configuration.h"
#include "core/EventSystem.h"

// Broker_Modern 头文件
#include "brokers/BrokerInterface.h"
#include "brokers/SimulationBroker.h"
#include "brokers/CtpBroker.h"
#include "brokers/CtpConfigManager.h"
#include "brokers/CtpOptimizer.h"
#include "orders/Order.h"
#include "accounts/Account.h"

#include <unordered_map>
#include <memory>
#include <shared_mutex>
#include <atomic>
#include <thread>

namespace QuantServices {

using namespace RoboQuant::Broker;

// Broker 连接信息
struct BrokerConnectionInfo {
    std::string broker_id;
    BrokerType broker_type;
    std::string name;
    std::string description;
    ConnectionInfo connection_info;
    bool is_active{true};
    bool auto_connect{false};
    TimePoint created_time{std::chrono::system_clock::now()};
    TimePoint last_connected_time;
    
    nlohmann::json to_json() const;
    static BrokerConnectionInfo from_json(const nlohmann::json& j);
};

// Broker 状态信息
struct BrokerStatus {
    std::string broker_id;
    ConnectionStatus connection_status{ConnectionStatus::Disconnected};
    bool is_authenticated{false};
    std::string last_error;
    TimePoint last_update_time{std::chrono::system_clock::now()};
    
    // 统计信息
    uint64_t total_orders{0};
    uint64_t active_orders{0};
    uint64_t filled_orders{0};
    uint64_t cancelled_orders{0};
    uint64_t rejected_orders{0};
    
    // 性能指标
    double avg_order_latency_ms{0.0};
    double success_rate{1.0};
    
    nlohmann::json to_json() const;
};

// Broker 服务管理器
class BrokerService : public Service {
public:
    BrokerService();
    ~BrokerService() override;
    
    // Service 接口实现
    std::string get_service_name() const override { return "BrokerService"; }
    std::string get_service_version() const override { return "1.0.0"; }
    ServiceType get_service_type() const override { return ServiceType::Trading; }
    
    Result<void> initialize(const nlohmann::json& config) override;
    Result<void> start() override;
    Result<void> stop() override;
    Result<void> restart() override;
    ServiceStatus get_status() const override;
    nlohmann::json get_status_details() const override;
    Result<void> configure(const nlohmann::json& config) override;
    
    // Broker 管理
    Result<std::string> add_broker(const BrokerConnectionInfo& broker_info);
    Result<void> remove_broker(const std::string& broker_id);
    Result<void> connect_broker(const std::string& broker_id);
    Result<void> disconnect_broker(const std::string& broker_id);
    Result<void> authenticate_broker(const std::string& broker_id, 
                                   const std::string& username, 
                                   const std::string& password);
    
    // Broker 查询
    std::vector<BrokerConnectionInfo> get_all_brokers() const;
    std::optional<BrokerConnectionInfo> get_broker_info(const std::string& broker_id) const;
    BrokerStatus get_broker_status(const std::string& broker_id) const;
    std::vector<BrokerStatus> get_all_broker_status() const;
    
    // 获取 Broker 实例
    std::shared_ptr<BrokerInterface> get_broker(const std::string& broker_id) const;
    std::vector<std::string> get_connected_brokers() const;
    std::vector<std::string> get_authenticated_brokers() const;
    
    // 订单操作
    Result<std::string> submit_order(const std::string& broker_id, const OrderRequest& request);
    Result<void> cancel_order(const std::string& broker_id, const std::string& order_id);
    Result<void> modify_order(const std::string& broker_id, const std::string& order_id, 
                             const OrderRequest& new_request);
    
    // 查询操作
    Result<std::vector<Order>> query_orders(const std::string& broker_id, 
                                           const std::string& account_id = "");
    Result<Order> query_order(const std::string& broker_id, const std::string& order_id);
    Result<std::vector<Account>> query_accounts(const std::string& broker_id);
    Result<std::vector<Position>> query_positions(const std::string& broker_id, 
                                                 const std::string& account_id = "");
    Result<std::vector<Trade>> query_trades(const std::string& broker_id, 
                                           const std::string& account_id = "",
                                           const TimePoint& from = {},
                                           const TimePoint& to = {});
    
    // 行情订阅
    Result<void> subscribe_market_data(const std::string& broker_id, const std::string& asset_id);
    Result<void> unsubscribe_market_data(const std::string& broker_id, const std::string& asset_id);
    
    // 事件回调设置
    using OrderCallback = std::function<void(const std::string& broker_id, const Order& order)>;
    using TradeCallback = std::function<void(const std::string& broker_id, const Trade& trade)>;
    using AccountCallback = std::function<void(const std::string& broker_id, const Account& account)>;
    using PositionCallback = std::function<void(const std::string& broker_id, const Position& position)>;
    using ErrorCallback = std::function<void(const std::string& broker_id, const std::string& error)>;
    
    void set_order_callback(OrderCallback callback) { order_callback_ = std::move(callback); }
    void set_trade_callback(TradeCallback callback) { trade_callback_ = std::move(callback); }
    void set_account_callback(AccountCallback callback) { account_callback_ = std::move(callback); }
    void set_position_callback(PositionCallback callback) { position_callback_ = std::move(callback); }
    void set_error_callback(ErrorCallback callback) { error_callback_ = std::move(callback); }
    
    // 配置管理
    Result<void> update_broker_config(const std::string& broker_id, const nlohmann::json& config);
    nlohmann::json get_broker_config(const std::string& broker_id) const;
    
    // 性能监控
    nlohmann::json get_performance_stats() const;
    nlohmann::json get_broker_performance(const std::string& broker_id) const;
    
    // 健康检查
    Result<void> health_check();
    Result<void> health_check_broker(const std::string& broker_id);

private:
    struct BrokerInstance {
        BrokerConnectionInfo info;
        std::shared_ptr<BrokerInterface> broker;
        std::unique_ptr<CtpOptimizer> optimizer; // 仅对 CTP 有效
        BrokerStatus status;
        std::atomic<bool> is_monitoring{false};
        std::thread monitor_thread;
        
        BrokerInstance(const BrokerConnectionInfo& info) : info(info) {
            status.broker_id = info.broker_id;
        }
        
        ~BrokerInstance() {
            stop_monitoring();
        }
        
        void stop_monitoring() {
            if (is_monitoring.load()) {
                is_monitoring.store(false);
                if (monitor_thread.joinable()) {
                    monitor_thread.join();
                }
            }
        }
    };
    
    // 内部方法
    Result<std::shared_ptr<BrokerInterface>> create_broker(const BrokerConnectionInfo& info);
    void setup_broker_callbacks(const std::string& broker_id, std::shared_ptr<BrokerInterface> broker);
    void start_broker_monitoring(const std::string& broker_id);
    void broker_monitor_worker(const std::string& broker_id);
    void update_broker_status(const std::string& broker_id);
    
    // 事件处理
    void on_broker_order_update(const std::string& broker_id, const Order& order);
    void on_broker_trade_update(const std::string& broker_id, const Trade& trade);
    void on_broker_account_update(const std::string& broker_id, const Account& account);
    void on_broker_position_update(const std::string& broker_id, const Position& position);
    void on_broker_error(const std::string& broker_id, const std::string& error);
    
    // 配置加载
    Result<void> load_broker_configs(const nlohmann::json& config);
    Result<void> save_broker_configs() const;
    
    // 数据成员
    mutable std::shared_mutex brokers_mutex_;
    std::unordered_map<std::string, std::unique_ptr<BrokerInstance>> brokers_;
    
    // 配置
    nlohmann::json service_config_;
    std::string config_file_path_;
    bool auto_connect_on_start_{false};
    bool enable_monitoring_{true};
    Duration monitoring_interval_{std::chrono::seconds(30)};
    
    // 回调函数
    OrderCallback order_callback_;
    TradeCallback trade_callback_;
    AccountCallback account_callback_;
    PositionCallback position_callback_;
    ErrorCallback error_callback_;
    
    // 统计信息
    std::atomic<uint64_t> total_orders_submitted_{0};
    std::atomic<uint64_t> total_orders_filled_{0};
    std::atomic<uint64_t> total_orders_cancelled_{0};
    std::atomic<uint64_t> total_orders_rejected_{0};
    
    TimePoint service_start_time_;
    std::atomic<bool> is_running_{false};
};

// Broker 工厂函数
std::shared_ptr<BrokerInterface> create_broker_instance(BrokerType type, const nlohmann::json& config = {});

// 工具函数
std::string generate_broker_id(BrokerType type, const std::string& suffix = "");
BrokerConnectionInfo create_simulation_broker_info(const std::string& broker_id = "");
BrokerConnectionInfo create_ctp_broker_info(const std::string& broker_id, 
                                           const std::string& front_address,
                                           const std::string& broker_code,
                                           const std::string& user_id);

} // namespace QuantServices
