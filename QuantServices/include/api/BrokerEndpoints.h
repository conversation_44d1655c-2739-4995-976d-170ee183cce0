#pragma once

#include "core/Types.h"
#include "services/BrokerService.h"
#include <boost/beast/http.hpp>
#include <nlohmann/json.hpp>
#include <memory>

namespace QuantServices::API {

namespace http = boost::beast::http;

// Broker API 端点处理器
class BrokerEndpoints {
public:
    explicit BrokerEndpoints(std::shared_ptr<BrokerService> broker_service);
    
    // 注册所有端点
    void register_endpoints(auto& router);
    
    // Broker 管理端点
    http::response<http::string_body> handle_get_brokers(const http::request<http::string_body>& req);
    http::response<http::string_body> handle_get_broker(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_add_broker(const http::request<http::string_body>& req);
    http::response<http::string_body> handle_remove_broker(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_connect_broker(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_disconnect_broker(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_authenticate_broker(const http::request<http::string_body>& req, const std::string& broker_id);
    
    // Broker 状态端点
    http::response<http::string_body> handle_get_broker_status(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_get_all_broker_status(const http::request<http::string_body>& req);
    http::response<http::string_body> handle_broker_health_check(const http::request<http::string_body>& req, const std::string& broker_id);
    
    // 订单管理端点
    http::response<http::string_body> handle_submit_order(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_cancel_order(const http::request<http::string_body>& req, const std::string& broker_id, const std::string& order_id);
    http::response<http::string_body> handle_modify_order(const http::request<http::string_body>& req, const std::string& broker_id, const std::string& order_id);
    http::response<http::string_body> handle_get_orders(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_get_order(const http::request<http::string_body>& req, const std::string& broker_id, const std::string& order_id);
    
    // 账户查询端点
    http::response<http::string_body> handle_get_accounts(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_get_account(const http::request<http::string_body>& req, const std::string& broker_id, const std::string& account_id);
    http::response<http::string_body> handle_get_positions(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_get_position(const http::request<http::string_body>& req, const std::string& broker_id, const std::string& account_id, const std::string& asset_id);
    http::response<http::string_body> handle_get_trades(const http::request<http::string_body>& req, const std::string& broker_id);
    
    // 行情订阅端点
    http::response<http::string_body> handle_subscribe_market_data(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_unsubscribe_market_data(const http::request<http::string_body>& req, const std::string& broker_id);
    
    // 配置管理端点
    http::response<http::string_body> handle_get_broker_config(const http::request<http::string_body>& req, const std::string& broker_id);
    http::response<http::string_body> handle_update_broker_config(const http::request<http::string_body>& req, const std::string& broker_id);
    
    // 性能监控端点
    http::response<http::string_body> handle_get_performance_stats(const http::request<http::string_body>& req);
    http::response<http::string_body> handle_get_broker_performance(const http::request<http::string_body>& req, const std::string& broker_id);

private:
    // 工具方法
    http::response<http::string_body> create_response(http::status status, const nlohmann::json& body);
    http::response<http::string_body> create_error_response(http::status status, const std::string& message);
    http::response<http::string_body> create_success_response(const nlohmann::json& data = nlohmann::json::object());
    
    // 请求解析
    std::optional<nlohmann::json> parse_request_body(const http::request<http::string_body>& req);
    std::unordered_map<std::string, std::string> parse_query_params(const std::string& query);
    
    // 数据转换
    nlohmann::json broker_info_to_json(const BrokerConnectionInfo& info);
    nlohmann::json broker_status_to_json(const BrokerStatus& status);
    nlohmann::json order_to_json(const RoboQuant::Broker::Order& order);
    nlohmann::json account_to_json(const RoboQuant::Broker::Account& account);
    nlohmann::json position_to_json(const RoboQuant::Broker::Position& position);
    nlohmann::json trade_to_json(const RoboQuant::Broker::Trade& trade);
    
    RoboQuant::Broker::OrderRequest json_to_order_request(const nlohmann::json& j);
    BrokerConnectionInfo json_to_broker_info(const nlohmann::json& j);
    
    // 验证方法
    bool validate_broker_id(const std::string& broker_id);
    bool validate_order_request(const nlohmann::json& request);
    bool validate_broker_connection_info(const nlohmann::json& info);
    
    std::shared_ptr<BrokerService> broker_service_;
};

// WebSocket 支持
class BrokerWebSocketHandler {
public:
    explicit BrokerWebSocketHandler(std::shared_ptr<BrokerService> broker_service);
    
    // WebSocket 连接管理
    void handle_connection(const std::string& connection_id);
    void handle_disconnection(const std::string& connection_id);
    void handle_message(const std::string& connection_id, const std::string& message);
    
    // 订阅管理
    void subscribe_to_orders(const std::string& connection_id, const std::string& broker_id);
    void subscribe_to_trades(const std::string& connection_id, const std::string& broker_id);
    void subscribe_to_accounts(const std::string& connection_id, const std::string& broker_id);
    void subscribe_to_positions(const std::string& connection_id, const std::string& broker_id);
    
    void unsubscribe_from_orders(const std::string& connection_id, const std::string& broker_id);
    void unsubscribe_from_trades(const std::string& connection_id, const std::string& broker_id);
    void unsubscribe_from_accounts(const std::string& connection_id, const std::string& broker_id);
    void unsubscribe_from_positions(const std::string& connection_id, const std::string& broker_id);
    
    // 数据推送
    void broadcast_order_update(const std::string& broker_id, const RoboQuant::Broker::Order& order);
    void broadcast_trade_update(const std::string& broker_id, const RoboQuant::Broker::Trade& trade);
    void broadcast_account_update(const std::string& broker_id, const RoboQuant::Broker::Account& account);
    void broadcast_position_update(const std::string& broker_id, const RoboQuant::Broker::Position& position);

private:
    struct WebSocketConnection {
        std::string connection_id;
        std::set<std::string> subscribed_brokers_orders;
        std::set<std::string> subscribed_brokers_trades;
        std::set<std::string> subscribed_brokers_accounts;
        std::set<std::string> subscribed_brokers_positions;
        TimePoint connected_time{std::chrono::system_clock::now()};
    };
    
    void send_message(const std::string& connection_id, const nlohmann::json& message);
    void setup_broker_callbacks();
    
    std::shared_ptr<BrokerService> broker_service_;
    mutable std::shared_mutex connections_mutex_;
    std::unordered_map<std::string, WebSocketConnection> connections_;
    
    // 回调函数已设置标志
    std::atomic<bool> callbacks_setup_{false};
};

// API 路由注册
void register_broker_api_routes(auto& router, std::shared_ptr<BrokerService> broker_service);

// 工具函数
std::string extract_path_parameter(const std::string& path, const std::string& pattern, const std::string& param_name);
std::vector<std::string> split_path(const std::string& path);
bool match_path_pattern(const std::string& path, const std::string& pattern);

} // namespace QuantServices::API
