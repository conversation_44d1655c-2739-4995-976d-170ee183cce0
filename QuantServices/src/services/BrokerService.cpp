#include "services/BrokerService.h"
#include "core/Logger.h"
#include <spdlog/spdlog.h>
#include <fstream>
#include <random>

namespace QuantServices {

// BrokerConnectionInfo 实现
nlohmann::json BrokerConnectionInfo::to_json() const {
    nlohmann::json j;
    j["broker_id"] = broker_id;
    j["broker_type"] = to_string(broker_type);
    j["name"] = name;
    j["description"] = description;
    j["is_active"] = is_active;
    j["auto_connect"] = auto_connect;
    
    j["connection_info"] = {
        {"server_address", connection_info.server_address},
        {"port", connection_info.port},
        {"username", connection_info.username},
        {"password", connection_info.password},
        {"extra_params", connection_info.extra_params}
    };
    
    j["created_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        created_time.time_since_epoch()).count();
    
    if (last_connected_time != TimePoint{}) {
        j["last_connected_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
            last_connected_time.time_since_epoch()).count();
    }
    
    return j;
}

BrokerConnectionInfo BrokerConnectionInfo::from_json(const nlohmann::json& j) {
    BrokerConnectionInfo info;
    info.broker_id = j.value("broker_id", "");
    info.broker_type = parse_broker_type(j.value("broker_type", "Unknown"));
    info.name = j.value("name", "");
    info.description = j.value("description", "");
    info.is_active = j.value("is_active", true);
    info.auto_connect = j.value("auto_connect", false);
    
    if (j.contains("connection_info")) {
        auto conn_json = j["connection_info"];
        info.connection_info.broker_type = info.broker_type;
        info.connection_info.server_address = conn_json.value("server_address", "");
        info.connection_info.port = conn_json.value("port", 0);
        info.connection_info.username = conn_json.value("username", "");
        info.connection_info.password = conn_json.value("password", "");
        info.connection_info.extra_params = conn_json.value("extra_params", 
            std::unordered_map<std::string, std::string>{});
    }
    
    if (j.contains("created_time")) {
        auto ms = j["created_time"].get<int64_t>();
        info.created_time = TimePoint(std::chrono::milliseconds(ms));
    }
    
    if (j.contains("last_connected_time")) {
        auto ms = j["last_connected_time"].get<int64_t>();
        info.last_connected_time = TimePoint(std::chrono::milliseconds(ms));
    }
    
    return info;
}

// BrokerStatus 实现
nlohmann::json BrokerStatus::to_json() const {
    nlohmann::json j;
    j["broker_id"] = broker_id;
    j["connection_status"] = to_string(connection_status);
    j["is_authenticated"] = is_authenticated;
    j["last_error"] = last_error;
    j["last_update_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        last_update_time.time_since_epoch()).count();
    
    j["statistics"] = {
        {"total_orders", total_orders},
        {"active_orders", active_orders},
        {"filled_orders", filled_orders},
        {"cancelled_orders", cancelled_orders},
        {"rejected_orders", rejected_orders}
    };
    
    j["performance"] = {
        {"avg_order_latency_ms", avg_order_latency_ms},
        {"success_rate", success_rate}
    };
    
    return j;
}

// BrokerService 实现
BrokerService::BrokerService() 
    : service_start_time_(std::chrono::system_clock::now()) {
    
    // 注册 Broker 工厂
    register_simulation_broker();
    register_ctp_broker();
    
    spdlog::info("BrokerService created");
}

BrokerService::~BrokerService() {
    stop();
    spdlog::info("BrokerService destroyed");
}

Result<void> BrokerService::initialize(const nlohmann::json& config) {
    try {
        service_config_ = config;
        
        // 加载基本配置
        auto_connect_on_start_ = config.value("auto_connect_on_start", false);
        enable_monitoring_ = config.value("enable_monitoring", true);
        
        if (config.contains("monitoring_interval_seconds")) {
            monitoring_interval_ = std::chrono::seconds(config["monitoring_interval_seconds"].get<int>());
        }
        
        config_file_path_ = config.value("config_file", "./config/brokers.json");
        
        // 加载 Broker 配置
        auto load_result = load_broker_configs(config);
        if (!load_result) {
            spdlog::warn("Failed to load broker configs: {}", load_result.error().message);
        }
        
        spdlog::info("BrokerService initialized with {} brokers", brokers_.size());
        return Result<void>::success();
        
    } catch (const std::exception& e) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Failed to initialize BrokerService: " + std::string(e.what()));
    }
}

Result<void> BrokerService::start() {
    if (is_running_.load()) {
        return Result<void>::success();
    }
    
    is_running_.store(true);
    service_start_time_ = std::chrono::system_clock::now();
    
    // 自动连接配置的 Broker
    if (auto_connect_on_start_) {
        std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
        for (const auto& [broker_id, instance] : brokers_) {
            if (instance->info.auto_connect && instance->info.is_active) {
                lock.unlock();
                auto connect_result = connect_broker(broker_id);
                if (!connect_result) {
                    spdlog::warn("Failed to auto-connect broker {}: {}", 
                               broker_id, connect_result.error().message);
                }
                lock.lock();
            }
        }
    }
    
    // 启动监控
    if (enable_monitoring_) {
        std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
        for (const auto& [broker_id, instance] : brokers_) {
            if (instance->info.is_active) {
                lock.unlock();
                start_broker_monitoring(broker_id);
                lock.lock();
            }
        }
    }
    
    spdlog::info("BrokerService started");
    return Result<void>::success();
}

Result<void> BrokerService::stop() {
    if (!is_running_.load()) {
        return Result<void>::success();
    }
    
    is_running_.store(false);
    
    // 停止所有 Broker 监控
    {
        std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
        for (const auto& [broker_id, instance] : brokers_) {
            instance->stop_monitoring();
        }
    }
    
    // 断开所有连接
    auto broker_ids = get_connected_brokers();
    for (const auto& broker_id : broker_ids) {
        auto disconnect_result = disconnect_broker(broker_id);
        if (!disconnect_result) {
            spdlog::warn("Failed to disconnect broker {}: {}", 
                       broker_id, disconnect_result.error().message);
        }
    }
    
    spdlog::info("BrokerService stopped");
    return Result<void>::success();
}

Result<void> BrokerService::restart() {
    auto stop_result = stop();
    if (!stop_result) {
        return stop_result;
    }
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    return start();
}

ServiceStatus BrokerService::get_status() const {
    if (!is_running_.load()) {
        return ServiceStatus::Stopped;
    }
    
    // 检查是否有活跃的 Broker
    auto connected_brokers = get_connected_brokers();
    if (connected_brokers.empty()) {
        return ServiceStatus::Warning;
    }
    
    return ServiceStatus::Running;
}

nlohmann::json BrokerService::get_status_details() const {
    nlohmann::json details;
    details["service_name"] = get_service_name();
    details["service_version"] = get_service_version();
    details["service_type"] = to_string(get_service_type());
    details["status"] = to_string(get_status());
    details["is_running"] = is_running_.load();
    
    auto uptime_seconds = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now() - service_start_time_).count();
    details["uptime_seconds"] = uptime_seconds;
    
    // Broker 统计
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    details["total_brokers"] = brokers_.size();
    
    int connected_count = 0;
    int authenticated_count = 0;
    for (const auto& [broker_id, instance] : brokers_) {
        if (instance->broker && instance->broker->is_connected()) {
            connected_count++;
            if (instance->broker->is_authenticated()) {
                authenticated_count++;
            }
        }
    }
    
    details["connected_brokers"] = connected_count;
    details["authenticated_brokers"] = authenticated_count;
    
    // 订单统计
    details["statistics"] = {
        {"total_orders_submitted", total_orders_submitted_.load()},
        {"total_orders_filled", total_orders_filled_.load()},
        {"total_orders_cancelled", total_orders_cancelled_.load()},
        {"total_orders_rejected", total_orders_rejected_.load()}
    };
    
    return details;
}

Result<void> BrokerService::configure(const nlohmann::json& config) {
    service_config_ = config;
    
    // 更新配置
    auto_connect_on_start_ = config.value("auto_connect_on_start", auto_connect_on_start_);
    enable_monitoring_ = config.value("enable_monitoring", enable_monitoring_);
    
    if (config.contains("monitoring_interval_seconds")) {
        monitoring_interval_ = std::chrono::seconds(config["monitoring_interval_seconds"].get<int>());
    }
    
    spdlog::info("BrokerService configuration updated");
    return Result<void>::success();
}

Result<std::string> BrokerService::add_broker(const BrokerConnectionInfo& broker_info) {
    if (broker_info.broker_id.empty()) {
        return Result<std::string>::failure(ErrorCode::InvalidParameter, "Broker ID cannot be empty");
    }
    
    std::unique_lock<std::shared_mutex> lock(brokers_mutex_);
    
    if (brokers_.find(broker_info.broker_id) != brokers_.end()) {
        return Result<std::string>::failure(ErrorCode::InvalidParameter, 
            "Broker with ID " + broker_info.broker_id + " already exists");
    }
    
    auto instance = std::make_unique<BrokerInstance>(broker_info);
    
    // 创建 Broker 实例
    auto broker_result = create_broker(broker_info);
    if (!broker_result) {
        return Result<std::string>::failure(broker_result.error().code, broker_result.error().message);
    }
    
    instance->broker = broker_result.value();
    setup_broker_callbacks(broker_info.broker_id, instance->broker);
    
    // 如果是 CTP Broker，创建优化器
    if (broker_info.broker_type == BrokerType::CTP) {
        auto ctp_broker = std::dynamic_pointer_cast<CtpBroker>(instance->broker);
        if (ctp_broker) {
            instance->optimizer = std::make_unique<CtpOptimizer>(ctp_broker);
            instance->optimizer->enable_optimization(true);
        }
    }
    
    brokers_[broker_info.broker_id] = std::move(instance);
    lock.unlock();
    
    // 启动监控
    if (enable_monitoring_ && is_running_.load()) {
        start_broker_monitoring(broker_info.broker_id);
    }
    
    spdlog::info("Broker added: {} ({})", broker_info.broker_id, to_string(broker_info.broker_type));
    return Result<std::string>::success(broker_info.broker_id);
}

Result<void> BrokerService::remove_broker(const std::string& broker_id) {
    std::unique_lock<std::shared_mutex> lock(brokers_mutex_);
    
    auto it = brokers_.find(broker_id);
    if (it == brokers_.end()) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Broker not found: " + broker_id);
    }
    
    // 停止监控和断开连接
    it->second->stop_monitoring();
    if (it->second->broker && it->second->broker->is_connected()) {
        it->second->broker->disconnect();
    }
    
    brokers_.erase(it);
    lock.unlock();
    
    spdlog::info("Broker removed: {}", broker_id);
    return Result<void>::success();
}

Result<void> BrokerService::connect_broker(const std::string& broker_id) {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    
    auto it = brokers_.find(broker_id);
    if (it == brokers_.end()) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Broker not found: " + broker_id);
    }
    
    if (!it->second->broker) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Broker instance not created: " + broker_id);
    }
    
    auto broker = it->second->broker;
    auto& info = it->second->info;
    lock.unlock();
    
    // 连接
    auto connect_result = broker->connect(info.connection_info);
    if (!connect_result) {
        spdlog::error("Failed to connect broker {}: {}", broker_id, connect_result.error().message);
        return connect_result;
    }
    
    // 更新连接时间
    {
        std::unique_lock<std::shared_mutex> update_lock(brokers_mutex_);
        auto update_it = brokers_.find(broker_id);
        if (update_it != brokers_.end()) {
            update_it->second->info.last_connected_time = std::chrono::system_clock::now();
            update_it->second->status.connection_status = ConnectionStatus::Connected;
        }
    }
    
    spdlog::info("Broker connected: {}", broker_id);
    return Result<void>::success();
}

Result<void> BrokerService::disconnect_broker(const std::string& broker_id) {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    
    auto it = brokers_.find(broker_id);
    if (it == brokers_.end()) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Broker not found: " + broker_id);
    }
    
    if (!it->second->broker) {
        return Result<void>::success(); // 已经断开
    }
    
    auto broker = it->second->broker;
    lock.unlock();
    
    auto disconnect_result = broker->disconnect();
    if (!disconnect_result) {
        spdlog::warn("Failed to disconnect broker {}: {}", broker_id, disconnect_result.error().message);
    }
    
    // 更新状态
    {
        std::unique_lock<std::shared_mutex> update_lock(brokers_mutex_);
        auto update_it = brokers_.find(broker_id);
        if (update_it != brokers_.end()) {
            update_it->second->status.connection_status = ConnectionStatus::Disconnected;
            update_it->second->status.is_authenticated = false;
        }
    }
    
    spdlog::info("Broker disconnected: {}", broker_id);
    return Result<void>::success();
}

Result<void> BrokerService::authenticate_broker(const std::string& broker_id, 
                                              const std::string& username, 
                                              const std::string& password) {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    
    auto it = brokers_.find(broker_id);
    if (it == brokers_.end()) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Broker not found: " + broker_id);
    }
    
    if (!it->second->broker) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Broker instance not created: " + broker_id);
    }
    
    auto broker = it->second->broker;
    lock.unlock();
    
    auto auth_result = broker->authenticate(username, password);
    if (!auth_result) {
        spdlog::error("Failed to authenticate broker {}: {}", broker_id, auth_result.error().message);
        return auth_result;
    }
    
    // 更新状态
    {
        std::unique_lock<std::shared_mutex> update_lock(brokers_mutex_);
        auto update_it = brokers_.find(broker_id);
        if (update_it != brokers_.end()) {
            update_it->second->status.is_authenticated = true;
            update_it->second->status.connection_status = ConnectionStatus::Authenticated;
        }
    }
    
    spdlog::info("Broker authenticated: {}", broker_id);
    return Result<void>::success();
}

// 查询方法实现
std::vector<BrokerConnectionInfo> BrokerService::get_all_brokers() const {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    std::vector<BrokerConnectionInfo> result;

    for (const auto& [broker_id, instance] : brokers_) {
        result.push_back(instance->info);
    }

    return result;
}

std::optional<BrokerConnectionInfo> BrokerService::get_broker_info(const std::string& broker_id) const {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);

    auto it = brokers_.find(broker_id);
    if (it != brokers_.end()) {
        return it->second->info;
    }

    return std::nullopt;
}

BrokerStatus BrokerService::get_broker_status(const std::string& broker_id) const {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);

    auto it = brokers_.find(broker_id);
    if (it != brokers_.end()) {
        return it->second->status;
    }

    // 返回默认状态
    BrokerStatus status;
    status.broker_id = broker_id;
    status.connection_status = ConnectionStatus::Disconnected;
    return status;
}

std::vector<BrokerStatus> BrokerService::get_all_broker_status() const {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    std::vector<BrokerStatus> result;

    for (const auto& [broker_id, instance] : brokers_) {
        result.push_back(instance->status);
    }

    return result;
}

std::shared_ptr<BrokerInterface> BrokerService::get_broker(const std::string& broker_id) const {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);

    auto it = brokers_.find(broker_id);
    if (it != brokers_.end()) {
        return it->second->broker;
    }

    return nullptr;
}

std::vector<std::string> BrokerService::get_connected_brokers() const {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    std::vector<std::string> result;

    for (const auto& [broker_id, instance] : brokers_) {
        if (instance->broker && instance->broker->is_connected()) {
            result.push_back(broker_id);
        }
    }

    return result;
}

std::vector<std::string> BrokerService::get_authenticated_brokers() const {
    std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
    std::vector<std::string> result;

    for (const auto& [broker_id, instance] : brokers_) {
        if (instance->broker && instance->broker->is_authenticated()) {
            result.push_back(broker_id);
        }
    }

    return result;
}

// 订单操作实现
Result<std::string> BrokerService::submit_order(const std::string& broker_id, const OrderRequest& request) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<std::string>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    if (!broker->is_authenticated()) {
        return Result<std::string>::failure(ErrorCode::ConnectionError,
            "Broker not authenticated: " + broker_id);
    }

    Order order(request);
    auto submit_result = broker->submit_order(order);
    if (!submit_result) {
        return Result<std::string>::failure(submit_result.error().code, submit_result.error().message);
    }

    total_orders_submitted_.fetch_add(1);

    // 更新 Broker 统计
    {
        std::unique_lock<std::shared_mutex> lock(brokers_mutex_);
        auto it = brokers_.find(broker_id);
        if (it != brokers_.end()) {
            it->second->status.total_orders++;
            it->second->status.active_orders++;
        }
    }

    spdlog::info("Order submitted to broker {}: {}", broker_id, order.order_id());
    return Result<std::string>::success(order.order_id());
}

Result<void> BrokerService::cancel_order(const std::string& broker_id, const std::string& order_id) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<void>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    // 查询订单
    auto order_result = broker->query_order(order_id);
    if (!order_result) {
        return Result<void>::failure(order_result.error().code, order_result.error().message);
    }

    auto cancel_result = broker->cancel_order(order_result.value());
    if (!cancel_result) {
        return cancel_result;
    }

    spdlog::info("Order cancelled in broker {}: {}", broker_id, order_id);
    return Result<void>::success();
}

Result<void> BrokerService::modify_order(const std::string& broker_id, const std::string& order_id,
                                        const OrderRequest& new_request) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<void>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    // 查询订单
    auto order_result = broker->query_order(order_id);
    if (!order_result) {
        return Result<void>::failure(order_result.error().code, order_result.error().message);
    }

    auto modify_result = broker->modify_order(order_result.value(), new_request);
    if (!modify_result) {
        return modify_result;
    }

    spdlog::info("Order modified in broker {}: {}", broker_id, order_id);
    return Result<void>::success();
}

// 查询操作实现
Result<std::vector<Order>> BrokerService::query_orders(const std::string& broker_id,
                                                      const std::string& account_id) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<std::vector<Order>>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    return broker->query_orders(account_id);
}

Result<Order> BrokerService::query_order(const std::string& broker_id, const std::string& order_id) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<Order>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    return broker->query_order(order_id);
}

Result<std::vector<Account>> BrokerService::query_accounts(const std::string& broker_id) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<std::vector<Account>>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    return broker->query_accounts();
}

Result<std::vector<Position>> BrokerService::query_positions(const std::string& broker_id,
                                                           const std::string& account_id) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<std::vector<Position>>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    return broker->query_positions(account_id);
}

Result<std::vector<Trade>> BrokerService::query_trades(const std::string& broker_id,
                                                      const std::string& account_id,
                                                      const TimePoint& from,
                                                      const TimePoint& to) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<std::vector<Trade>>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    return broker->query_trades(account_id, from, to);
}

// 行情订阅实现
Result<void> BrokerService::subscribe_market_data(const std::string& broker_id, const std::string& asset_id) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<void>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    auto subscribe_result = broker->subscribe_market_data(asset_id);
    if (subscribe_result) {
        spdlog::info("Market data subscribed in broker {}: {}", broker_id, asset_id);
    }

    return subscribe_result;
}

Result<void> BrokerService::unsubscribe_market_data(const std::string& broker_id, const std::string& asset_id) {
    auto broker = get_broker(broker_id);
    if (!broker) {
        return Result<void>::failure(ErrorCode::InvalidParameter,
            "Broker not found: " + broker_id);
    }

    auto unsubscribe_result = broker->unsubscribe_market_data(asset_id);
    if (unsubscribe_result) {
        spdlog::info("Market data unsubscribed in broker {}: {}", broker_id, asset_id);
    }

    return unsubscribe_result;
}

// 内部方法实现
Result<std::shared_ptr<BrokerInterface>> BrokerService::create_broker(const BrokerConnectionInfo& info) {
    try {
        std::shared_ptr<BrokerInterface> broker;

        switch (info.broker_type) {
            case BrokerType::Simulation: {
                broker = std::make_shared<SimulationBroker>();
                break;
            }
            case BrokerType::CTP: {
                // 创建 CTP 配置
                CtpConfig ctp_config;
                ctp_config.front_address = info.connection_info.server_address + ":" +
                                          std::to_string(info.connection_info.port);
                ctp_config.user_id = info.connection_info.username;
                ctp_config.password = info.connection_info.password;

                // 从额外参数获取 CTP 特有配置
                auto& params = info.connection_info.extra_params;
                if (params.find("broker_id") != params.end()) {
                    ctp_config.broker_id = params.at("broker_id");
                }
                if (params.find("app_id") != params.end()) {
                    ctp_config.app_id = params.at("app_id");
                }
                if (params.find("auth_code") != params.end()) {
                    ctp_config.auth_code = params.at("auth_code");
                }

                broker = std::make_shared<CtpBroker>(ctp_config);
                break;
            }
            default:
                return Result<std::shared_ptr<BrokerInterface>>::failure(ErrorCode::InvalidParameter,
                    "Unsupported broker type: " + to_string(info.broker_type));
        }

        return Result<std::shared_ptr<BrokerInterface>>::success(broker);

    } catch (const std::exception& e) {
        return Result<std::shared_ptr<BrokerInterface>>::failure(ErrorCode::InvalidParameter,
            "Failed to create broker: " + std::string(e.what()));
    }
}

void BrokerService::setup_broker_callbacks(const std::string& broker_id, std::shared_ptr<BrokerInterface> broker) {
    // 设置订单回调
    broker->set_order_callback([this, broker_id](const Order& order) {
        on_broker_order_update(broker_id, order);
    });

    // 设置交易回调
    broker->set_trade_callback([this, broker_id](const Trade& trade) {
        on_broker_trade_update(broker_id, trade);
    });

    // 设置账户回调
    broker->set_account_callback([this, broker_id](const Account& account) {
        on_broker_account_update(broker_id, account);
    });

    // 设置持仓回调
    broker->set_position_callback([this, broker_id](const Position& position) {
        on_broker_position_update(broker_id, position);
    });

    // 设置错误回调
    broker->set_error_callback([this, broker_id](const std::string& error) {
        on_broker_error(broker_id, error);
    });
}

void BrokerService::start_broker_monitoring(const std::string& broker_id) {
    std::unique_lock<std::shared_mutex> lock(brokers_mutex_);

    auto it = brokers_.find(broker_id);
    if (it == brokers_.end() || it->second->is_monitoring.load()) {
        return;
    }

    it->second->is_monitoring.store(true);
    it->second->monitor_thread = std::thread(&BrokerService::broker_monitor_worker, this, broker_id);

    spdlog::debug("Started monitoring for broker: {}", broker_id);
}

void BrokerService::broker_monitor_worker(const std::string& broker_id) {
    while (true) {
        {
            std::shared_lock<std::shared_mutex> lock(brokers_mutex_);
            auto it = brokers_.find(broker_id);
            if (it == brokers_.end() || !it->second->is_monitoring.load()) {
                break;
            }
        }

        try {
            update_broker_status(broker_id);

            // 健康检查
            auto broker = get_broker(broker_id);
            if (broker) {
                auto health_result = broker->health_check();
                if (!health_result) {
                    spdlog::warn("Health check failed for broker {}: {}",
                               broker_id, health_result.error().message);
                }
            }

        } catch (const std::exception& e) {
            spdlog::error("Exception in broker monitor for {}: {}", broker_id, e.what());
        }

        std::this_thread::sleep_for(monitoring_interval_);
    }

    spdlog::debug("Stopped monitoring for broker: {}", broker_id);
}

void BrokerService::update_broker_status(const std::string& broker_id) {
    std::unique_lock<std::shared_mutex> lock(brokers_mutex_);

    auto it = brokers_.find(broker_id);
    if (it == brokers_.end()) {
        return;
    }

    auto& status = it->second->status;
    auto broker = it->second->broker;

    if (broker) {
        status.connection_status = broker->get_connection_status();
        status.is_authenticated = broker->is_authenticated();
        status.last_update_time = std::chrono::system_clock::now();

        // 查询订单统计
        auto orders_result = broker->query_orders();
        if (orders_result) {
            auto orders = orders_result.value();
            status.total_orders = orders.size();

            status.active_orders = 0;
            status.filled_orders = 0;
            status.cancelled_orders = 0;
            status.rejected_orders = 0;

            for (const auto& order : orders) {
                if (order.is_active()) {
                    status.active_orders++;
                } else if (order.is_filled()) {
                    status.filled_orders++;
                } else if (order.status() == OrderStatus::Cancelled) {
                    status.cancelled_orders++;
                } else if (order.status() == OrderStatus::Rejected) {
                    status.rejected_orders++;
                }
            }

            // 计算成功率
            if (status.total_orders > 0) {
                status.success_rate = static_cast<double>(status.filled_orders) / status.total_orders;
            }
        }
    }
}

// 事件处理方法
void BrokerService::on_broker_order_update(const std::string& broker_id, const Order& order) {
    // 更新统计
    if (order.is_filled()) {
        total_orders_filled_.fetch_add(1);
    } else if (order.status() == OrderStatus::Cancelled) {
        total_orders_cancelled_.fetch_add(1);
    } else if (order.status() == OrderStatus::Rejected) {
        total_orders_rejected_.fetch_add(1);
    }

    // 调用用户回调
    if (order_callback_) {
        try {
            order_callback_(broker_id, order);
        } catch (const std::exception& e) {
            spdlog::error("Exception in order callback: {}", e.what());
        }
    }

    spdlog::debug("Order update from broker {}: {} status {}",
                 broker_id, order.order_id(), to_string(order.status()));
}

void BrokerService::on_broker_trade_update(const std::string& broker_id, const Trade& trade) {
    if (trade_callback_) {
        try {
            trade_callback_(broker_id, trade);
        } catch (const std::exception& e) {
            spdlog::error("Exception in trade callback: {}", e.what());
        }
    }

    spdlog::debug("Trade update from broker {}: {} {} @ {}",
                 broker_id, trade.trade_id(), trade.quantity(), trade.price());
}

void BrokerService::on_broker_account_update(const std::string& broker_id, const Account& account) {
    if (account_callback_) {
        try {
            account_callback_(broker_id, account);
        } catch (const std::exception& e) {
            spdlog::error("Exception in account callback: {}", e.what());
        }
    }

    spdlog::debug("Account update from broker {}: {} balance {}",
                 broker_id, account.account_id(), account.balance().total_balance);
}

void BrokerService::on_broker_position_update(const std::string& broker_id, const Position& position) {
    if (position_callback_) {
        try {
            position_callback_(broker_id, position);
        } catch (const std::exception& e) {
            spdlog::error("Exception in position callback: {}", e.what());
        }
    }

    spdlog::debug("Position update from broker {}: {} {} qty {}",
                 broker_id, position.account_id(), position.asset_id(), position.quantity());
}

void BrokerService::on_broker_error(const std::string& broker_id, const std::string& error) {
    // 更新错误状态
    {
        std::unique_lock<std::shared_mutex> lock(brokers_mutex_);
        auto it = brokers_.find(broker_id);
        if (it != brokers_.end()) {
            it->second->status.last_error = error;
            it->second->status.last_update_time = std::chrono::system_clock::now();
        }
    }

    if (error_callback_) {
        try {
            error_callback_(broker_id, error);
        } catch (const std::exception& e) {
            spdlog::error("Exception in error callback: {}", e.what());
        }
    }

    spdlog::warn("Error from broker {}: {}", broker_id, error);
}
