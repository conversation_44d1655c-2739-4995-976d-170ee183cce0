#include <iostream>
#include <thread>
#include <chrono>
#include <curl/curl.h>
#include <nlohmann/json.hpp>

// HTTP 客户端示例，演示如何使用 Broker REST API

class BrokerAPIClient {
public:
    BrokerAPIClient(const std::string& base_url) : base_url_(base_url) {
        curl_global_init(CURL_GLOBAL_DEFAULT);
    }
    
    ~BrokerAPIClient() {
        curl_global_cleanup();
    }
    
    // 获取所有 Broker
    nlohmann::json get_brokers() {
        return make_request("GET", "/api/v1/brokers");
    }
    
    // 添加 Broker
    nlohmann::json add_broker(const nlohmann::json& broker_info) {
        return make_request("POST", "/api/v1/brokers", broker_info);
    }
    
    // 连接 Broker
    nlohmann::json connect_broker(const std::string& broker_id) {
        return make_request("POST", "/api/v1/brokers/" + broker_id + "/connect");
    }
    
    // 认证 Broker
    nlohmann::json authenticate_broker(const std::string& broker_id, 
                                     const std::string& username, 
                                     const std::string& password) {
        nlohmann::json auth_data;
        auth_data["username"] = username;
        auth_data["password"] = password;
        return make_request("POST", "/api/v1/brokers/" + broker_id + "/authenticate", auth_data);
    }
    
    // 提交订单
    nlohmann::json submit_order(const std::string& broker_id, const nlohmann::json& order) {
        return make_request("POST", "/api/v1/brokers/" + broker_id + "/orders", order);
    }
    
    // 获取订单
    nlohmann::json get_orders(const std::string& broker_id) {
        return make_request("GET", "/api/v1/brokers/" + broker_id + "/orders");
    }
    
    // 撤销订单
    nlohmann::json cancel_order(const std::string& broker_id, const std::string& order_id) {
        return make_request("DELETE", "/api/v1/brokers/" + broker_id + "/orders/" + order_id);
    }
    
    // 获取账户
    nlohmann::json get_accounts(const std::string& broker_id) {
        return make_request("GET", "/api/v1/brokers/" + broker_id + "/accounts");
    }
    
    // 获取持仓
    nlohmann::json get_positions(const std::string& broker_id) {
        return make_request("GET", "/api/v1/brokers/" + broker_id + "/positions");
    }
    
    // 获取交易记录
    nlohmann::json get_trades(const std::string& broker_id) {
        return make_request("GET", "/api/v1/brokers/" + broker_id + "/trades");
    }
    
    // 获取 Broker 状态
    nlohmann::json get_broker_status(const std::string& broker_id) {
        return make_request("GET", "/api/v1/brokers/" + broker_id + "/status");
    }

private:
    std::string base_url_;
    
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
        userp->append((char*)contents, size * nmemb);
        return size * nmemb;
    }
    
    nlohmann::json make_request(const std::string& method, const std::string& endpoint, 
                               const nlohmann::json& data = nlohmann::json::object()) {
        CURL* curl = curl_easy_init();
        if (!curl) {
            throw std::runtime_error("Failed to initialize CURL");
        }
        
        std::string response_string;
        std::string url = base_url_ + endpoint;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response_string);
        
        struct curl_slist* headers = nullptr;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        
        std::string post_data;
        if (method == "POST" || method == "PUT") {
            post_data = data.dump();
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data.c_str());
            
            if (method == "POST") {
                curl_easy_setopt(curl, CURLOPT_POST, 1L);
            } else {
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
            }
        } else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
        }
        
        CURLcode res = curl_easy_perform(curl);
        
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        
        if (res != CURLE_OK) {
            throw std::runtime_error("CURL request failed: " + std::string(curl_easy_strerror(res)));
        }
        
        try {
            return nlohmann::json::parse(response_string);
        } catch (const std::exception& e) {
            throw std::runtime_error("Failed to parse JSON response: " + std::string(e.what()));
        }
    }
};

int main() {
    std::cout << "=== Broker API 客户端示例 ===\n\n";
    
    try {
        // 创建 API 客户端
        BrokerAPIClient client("http://localhost:8080");
        
        // 1. 获取现有 Broker 列表
        std::cout << "1. 获取 Broker 列表...\n";
        auto brokers = client.get_brokers();
        std::cout << "现有 Brokers: " << brokers.dump(2) << "\n\n";
        
        // 2. 添加模拟 Broker
        std::cout << "2. 添加模拟 Broker...\n";
        nlohmann::json broker_info;
        broker_info["broker_id"] = "demo_simulation";
        broker_info["broker_type"] = "Simulation";
        broker_info["name"] = "Demo Simulation Broker";
        broker_info["description"] = "演示用模拟 Broker";
        broker_info["is_active"] = true;
        broker_info["auto_connect"] = false;
        
        broker_info["connection_info"]["server_address"] = "localhost";
        broker_info["connection_info"]["port"] = 0;
        broker_info["connection_info"]["username"] = "demo_user";
        broker_info["connection_info"]["password"] = "demo_pass";
        broker_info["connection_info"]["extra_params"]["initial_balance"] = "1000000.0";
        
        auto add_result = client.add_broker(broker_info);
        std::cout << "添加结果: " << add_result.dump(2) << "\n\n";
        
        // 3. 连接 Broker
        std::cout << "3. 连接 Broker...\n";
        auto connect_result = client.connect_broker("demo_simulation");
        std::cout << "连接结果: " << connect_result.dump(2) << "\n\n";
        
        // 等待连接完成
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 4. 认证 Broker
        std::cout << "4. 认证 Broker...\n";
        auto auth_result = client.authenticate_broker("demo_simulation", "demo_user", "demo_pass");
        std::cout << "认证结果: " << auth_result.dump(2) << "\n\n";
        
        // 等待认证完成
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 5. 查询账户信息
        std::cout << "5. 查询账户信息...\n";
        auto accounts = client.get_accounts("demo_simulation");
        std::cout << "账户信息: " << accounts.dump(2) << "\n\n";
        
        // 6. 查询持仓信息
        std::cout << "6. 查询持仓信息...\n";
        auto positions = client.get_positions("demo_simulation");
        std::cout << "持仓信息: " << positions.dump(2) << "\n\n";
        
        // 7. 提交买入订单
        std::cout << "7. 提交买入订单...\n";
        nlohmann::json buy_order;
        buy_order["asset_id"] = "DEMO_STOCK";
        buy_order["side"] = "Buy";
        buy_order["type"] = "Limit";
        buy_order["quantity"] = 100;
        buy_order["price"] = 50.0;
        buy_order["account_id"] = "demo_account";
        buy_order["strategy_id"] = "demo_strategy";
        
        auto buy_result = client.submit_order("demo_simulation", buy_order);
        std::cout << "买入订单结果: " << buy_result.dump(2) << "\n\n";
        
        std::string buy_order_id;
        if (buy_result.contains("data") && buy_result["data"].contains("order_id")) {
            buy_order_id = buy_result["data"]["order_id"];
        }
        
        // 8. 提交卖出订单
        std::cout << "8. 提交卖出订单...\n";
        nlohmann::json sell_order;
        sell_order["asset_id"] = "DEMO_STOCK";
        sell_order["side"] = "Sell";
        sell_order["type"] = "Limit";
        sell_order["quantity"] = 50;
        sell_order["price"] = 55.0;
        sell_order["account_id"] = "demo_account";
        sell_order["strategy_id"] = "demo_strategy";
        
        auto sell_result = client.submit_order("demo_simulation", sell_order);
        std::cout << "卖出订单结果: " << sell_result.dump(2) << "\n\n";
        
        std::string sell_order_id;
        if (sell_result.contains("data") && sell_result["data"].contains("order_id")) {
            sell_order_id = sell_result["data"]["order_id"];
        }
        
        // 等待订单处理
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        // 9. 查询所有订单
        std::cout << "9. 查询所有订单...\n";
        auto orders = client.get_orders("demo_simulation");
        std::cout << "订单列表: " << orders.dump(2) << "\n\n";
        
        // 10. 查询交易记录
        std::cout << "10. 查询交易记录...\n";
        auto trades = client.get_trades("demo_simulation");
        std::cout << "交易记录: " << trades.dump(2) << "\n\n";
        
        // 11. 撤销买入订单（如果还未成交）
        if (!buy_order_id.empty()) {
            std::cout << "11. 撤销买入订单...\n";
            auto cancel_result = client.cancel_order("demo_simulation", buy_order_id);
            std::cout << "撤单结果: " << cancel_result.dump(2) << "\n\n";
        }
        
        // 等待撤单处理
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 12. 再次查询订单状态
        std::cout << "12. 再次查询订单状态...\n";
        auto updated_orders = client.get_orders("demo_simulation");
        std::cout << "更新后的订单列表: " << updated_orders.dump(2) << "\n\n";
        
        // 13. 查询 Broker 状态
        std::cout << "13. 查询 Broker 状态...\n";
        auto broker_status = client.get_broker_status("demo_simulation");
        std::cout << "Broker 状态: " << broker_status.dump(2) << "\n\n";
        
        // 14. 再次查询账户信息（查看余额变化）
        std::cout << "14. 查询最终账户信息...\n";
        auto final_accounts = client.get_accounts("demo_simulation");
        std::cout << "最终账户信息: " << final_accounts.dump(2) << "\n\n";
        
        // 15. 查询最终持仓信息
        std::cout << "15. 查询最终持仓信息...\n";
        auto final_positions = client.get_positions("demo_simulation");
        std::cout << "最终持仓信息: " << final_positions.dump(2) << "\n\n";
        
        std::cout << "? Broker API 演示完成！\n";
        
    } catch (const std::exception& e) {
        std::cerr << "? 错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
