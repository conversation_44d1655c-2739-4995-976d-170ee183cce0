# Broker API 参考文档

## 概述

Broker API 提供了完整的交易接口管理功能，支持多种交易接口类型（模拟、CTP 等），提供统一的 REST API 和 WebSocket 实时数据推送。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **认证**: 暂无（开发阶段）

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": { ... },
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "error": true,
  "message": "错误描述",
  "timestamp": 1640995200000
}
```

## Broker 管理 API

### 获取所有 Broker

**GET** `/brokers`

获取系统中所有已配置的 Broker 列表。

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "broker_id": "simulation_001",
      "broker_type": "Simulation",
      "name": "Simulation Broker",
      "description": "Default simulation broker",
      "is_active": true,
      "auto_connect": true,
      "created_time": 1640995200000,
      "last_connected_time": 1640995300000,
      "connection_info": {
        "server_address": "localhost",
        "port": 0,
        "username": "simulation_user"
      }
    }
  ]
}
```

### 获取单个 Broker

**GET** `/brokers/{broker_id}`

获取指定 Broker 的详细信息。

**路径参数:**
- `broker_id` (string): Broker ID

### 添加 Broker

**POST** `/brokers`

添加新的 Broker 配置。

**请求体:**
```json
{
  "broker_id": "my_broker",
  "broker_type": "CTP",
  "name": "My CTP Broker",
  "description": "My trading broker",
  "is_active": true,
  "auto_connect": false,
  "connection_info": {
    "server_address": "***************",
    "port": 10130,
    "username": "your_username",
    "password": "your_password",
    "extra_params": {
      "broker_id": "9999",
      "app_id": "your_app_id",
      "auth_code": "your_auth_code"
    }
  }
}
```

### 删除 Broker

**DELETE** `/brokers/{broker_id}`

删除指定的 Broker 配置。

### 连接 Broker

**POST** `/brokers/{broker_id}/connect`

连接到指定的 Broker。

### 断开 Broker

**POST** `/brokers/{broker_id}/disconnect`

断开与指定 Broker 的连接。

### 认证 Broker

**POST** `/brokers/{broker_id}/authenticate`

对指定 Broker 进行认证。

**请求体:**
```json
{
  "username": "your_username",
  "password": "your_password"
}
```

## Broker 状态 API

### 获取 Broker 状态

**GET** `/brokers/{broker_id}/status`

获取指定 Broker 的状态信息。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "broker_id": "simulation_001",
    "connection_status": "Connected",
    "is_authenticated": true,
    "last_error": "",
    "last_update_time": 1640995400000,
    "statistics": {
      "total_orders": 150,
      "active_orders": 5,
      "filled_orders": 120,
      "cancelled_orders": 25,
      "rejected_orders": 0
    },
    "performance": {
      "avg_order_latency_ms": 45.2,
      "success_rate": 0.98
    }
  }
}
```

### 获取所有 Broker 状态

**GET** `/brokers/status`

获取所有 Broker 的状态信息。

### Broker 健康检查

**GET** `/brokers/{broker_id}/health`

检查指定 Broker 的健康状态。

## 订单管理 API

### 提交订单

**POST** `/brokers/{broker_id}/orders`

向指定 Broker 提交新订单。

**请求体:**
```json
{
  "asset_id": "rb2501",
  "side": "Buy",
  "type": "Limit",
  "quantity": 1,
  "price": 3500.0,
  "account_id": "your_account",
  "strategy_id": "my_strategy",
  "time_in_force": "GTC",
  "metadata": {
    "custom_field": "custom_value"
  }
}
```

**订单类型 (type):**
- `Market`: 市价单
- `Limit`: 限价单
- `Stop`: 止损单
- `StopLimit`: 止损限价单

**订单方向 (side):**
- `Buy`: 买入
- `Sell`: 卖出

**时效性 (time_in_force):**
- `GTC`: Good Till Cancel (撤销前有效)
- `IOC`: Immediate Or Cancel (立即成交或撤销)
- `FOK`: Fill Or Kill (全部成交或撤销)
- `DAY`: 当日有效

### 获取订单列表

**GET** `/brokers/{broker_id}/orders`

获取指定 Broker 的订单列表。

**查询参数:**
- `account_id` (string, 可选): 账户 ID 过滤

### 获取单个订单

**GET** `/brokers/{broker_id}/orders/{order_id}`

获取指定订单的详细信息。

### 撤销订单

**DELETE** `/brokers/{broker_id}/orders/{order_id}`

撤销指定的订单。

### 修改订单

**PUT** `/brokers/{broker_id}/orders/{order_id}`

修改指定订单的参数。

**请求体:**
```json
{
  "quantity": 2,
  "price": 3550.0
}
```

## 账户查询 API

### 获取账户列表

**GET** `/brokers/{broker_id}/accounts`

获取指定 Broker 的账户列表。

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "account_id": "123456",
      "broker_type": "CTP",
      "account_type": "Future",
      "balance": {
        "total_balance": 1000000.0,
        "available_balance": 800000.0,
        "frozen_balance": 200000.0,
        "currency": "CNY"
      },
      "last_update_time": *************
    }
  ]
}
```

### 获取持仓列表

**GET** `/brokers/{broker_id}/positions`

获取指定 Broker 的持仓列表。

**查询参数:**
- `account_id` (string, 可选): 账户 ID 过滤

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "account_id": "123456",
      "asset_id": "rb2501",
      "side": "Long",
      "quantity": 5,
      "available_quantity": 3,
      "frozen_quantity": 2,
      "average_price": 3480.0,
      "market_value": 17400.0,
      "unrealized_pnl": 100.0,
      "realized_pnl": 50.0,
      "last_update_time": *************
    }
  ]
}
```

### 获取交易记录

**GET** `/brokers/{broker_id}/trades`

获取指定 Broker 的交易记录。

**查询参数:**
- `account_id` (string, 可选): 账户 ID 过滤
- `from` (number, 可选): 开始时间戳
- `to` (number, 可选): 结束时间戳

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "trade_id": "T20240101001",
      "order_id": "O20240101001",
      "account_id": "123456",
      "asset_id": "rb2501",
      "side": "Buy",
      "quantity": 1,
      "price": 3500.0,
      "amount": 3500.0,
      "trade_time": *************
    }
  ]
}
```

## 行情订阅 API

### 订阅行情

**POST** `/brokers/{broker_id}/market_data/subscribe`

订阅指定资产的行情数据。

**请求体:**
```json
{
  "asset_id": "rb2501"
}
```

### 退订行情

**POST** `/brokers/{broker_id}/market_data/unsubscribe`

退订指定资产的行情数据。

**请求体:**
```json
{
  "asset_id": "rb2501"
}
```

## 配置管理 API

### 获取 Broker 配置

**GET** `/brokers/{broker_id}/config`

获取指定 Broker 的配置信息。

### 更新 Broker 配置

**PUT** `/brokers/{broker_id}/config`

更新指定 Broker 的配置信息。

## 性能监控 API

### 获取性能统计

**GET** `/performance/stats`

获取整体性能统计信息。

### 获取 Broker 性能

**GET** `/brokers/{broker_id}/performance`

获取指定 Broker 的性能统计信息。

## WebSocket API

### 连接

连接到 WebSocket 端点：`ws://localhost:8080/ws/broker`

### 订阅消息格式

```json
{
  "type": "subscribe",
  "data_type": "orders",
  "broker_id": "simulation_001"
}
```

**数据类型 (data_type):**
- `orders`: 订单更新
- `trades`: 交易回报
- `accounts`: 账户更新
- `positions`: 持仓更新

### 推送消息格式

#### 订单更新
```json
{
  "type": "order_update",
  "broker_id": "simulation_001",
  "data": {
    "order_id": "O20240101001",
    "asset_id": "rb2501",
    "side": "Buy",
    "type": "Limit",
    "quantity": 1,
    "filled_quantity": 0,
    "status": "New",
    "account_id": "123456",
    "price": 3500.0
  },
  "timestamp": *************
}
```

#### 交易回报
```json
{
  "type": "trade_update",
  "broker_id": "simulation_001",
  "data": {
    "trade_id": "T20240101001",
    "order_id": "O20240101001",
    "asset_id": "rb2501",
    "side": "Buy",
    "quantity": 1,
    "price": 3500.0,
    "amount": 3500.0,
    "account_id": "123456"
  },
  "timestamp": *************
}
```

## 错误代码

| 错误代码 | HTTP 状态码 | 描述 |
|---------|------------|------|
| INVALID_PARAMETER | 400 | 无效参数 |
| BROKER_NOT_FOUND | 404 | Broker 不存在 |
| CONNECTION_ERROR | 503 | 连接错误 |
| AUTHENTICATION_ERROR | 401 | 认证失败 |
| ORDER_REJECTED | 400 | 订单被拒绝 |
| INSUFFICIENT_FUNDS | 400 | 资金不足 |
| MARKET_CLOSED | 400 | 市场关闭 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |

## 使用示例

### Python 示例

```python
import requests
import json

# 基础 URL
base_url = "http://localhost:8080/api/v1"

# 添加模拟 Broker
broker_data = {
    "broker_id": "my_simulation",
    "broker_type": "Simulation",
    "name": "My Simulation Broker",
    "is_active": True,
    "connection_info": {
        "server_address": "localhost",
        "port": 0,
        "username": "test_user",
        "password": "test_pass"
    }
}

response = requests.post(f"{base_url}/brokers", json=broker_data)
print("Add broker:", response.json())

# 连接 Broker
response = requests.post(f"{base_url}/brokers/my_simulation/connect")
print("Connect:", response.json())

# 认证 Broker
auth_data = {"username": "test_user", "password": "test_pass"}
response = requests.post(f"{base_url}/brokers/my_simulation/authenticate", json=auth_data)
print("Authenticate:", response.json())

# 提交订单
order_data = {
    "asset_id": "TEST_STOCK",
    "side": "Buy",
    "type": "Limit",
    "quantity": 100,
    "price": 50.0,
    "account_id": "test_account"
}

response = requests.post(f"{base_url}/brokers/my_simulation/orders", json=order_data)
print("Submit order:", response.json())
```

### JavaScript 示例

```javascript
const baseUrl = 'http://localhost:8080/api/v1';

// 获取所有 Broker
fetch(`${baseUrl}/brokers`)
  .then(response => response.json())
  .then(data => console.log('Brokers:', data));

// WebSocket 连接
const ws = new WebSocket('ws://localhost:8080/ws/broker');

ws.onopen = function() {
  // 订阅订单更新
  ws.send(JSON.stringify({
    type: 'subscribe',
    data_type: 'orders',
    broker_id: 'my_simulation'
  }));
};

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('WebSocket message:', data);
};
```
