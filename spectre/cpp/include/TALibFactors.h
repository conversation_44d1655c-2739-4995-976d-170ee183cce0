#pragma once

#include "Factor.h"
#include "TALibIntegration.h"
#include <torch/torch.h>
#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <chrono>

namespace Spectre {

// ============================================================================
// TA-Lib Factor Base Classes
// ============================================================================

/**
 * TALibFactor: TA-Lib 函数的基础包装类
 * 提供统一的接口来调用 TA-Lib 函数并处理批量计算
 */
class TALibFactor : public CustomFactor {
public:
    TALibFactor(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs,
               const std::string& function_name);
    
    // 实现 CustomFactor 的纯虚函数
    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override;
    
protected:
    // 子类需要实现的核心计算函数
    virtual TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) = 0;
    
    // 多输出函数的支持
    struct MultiOutputResult {
        std::vector<std::vector<double>> outputs;
        int begin_index;
        int nb_elements;
        bool success;
    };
    virtual MultiOutputResult compute_multi_output(const std::vector<std::vector<double>>& inputs) {
        // 默认实现：调用单输出版本
        auto result = compute_single(inputs);
        MultiOutputResult multi_result;
        multi_result.outputs = {result.output};
        multi_result.begin_index = result.begin_index;
        multi_result.nb_elements = result.nb_elements;
        multi_result.success = result.success();
        return multi_result;
    }
    
    // 获取输出数量（默认为1）
    virtual int get_output_count() const { return 1; }
    
    // 参数验证
    virtual void validate_parameters() const {}
    
    // 输入验证
    virtual void validate_inputs(const std::vector<torch::Tensor>& inputs) const;
    
    std::string function_name_;
    TALibWrapper& wrapper_;
};

/**
 * SingleInputTALibFactor: 单输入 TA-Lib 函数的基类
 * 适用于大多数技术指标（如 SMA, EMA, RSI 等）
 */
class SingleInputTALibFactor : public TALibFactor {
public:
    SingleInputTALibFactor(const std::shared_ptr<BaseFactor>& input, int period,
                          const std::string& function_name);

protected:
    void validate_inputs(const std::vector<torch::Tensor>& inputs) const override;
    
    int period_;
};

/**
 * MultiInputTALibFactor: 多输入 TA-Lib 函数的基类
 * 适用于需要 OHLC 或 OHLCV 数据的指标
 */
class MultiInputTALibFactor : public TALibFactor {
public:
    MultiInputTALibFactor(const std::vector<std::shared_ptr<BaseFactor>>& inputs,
                         const std::string& function_name, int expected_input_count);

protected:
    void validate_inputs(const std::vector<torch::Tensor>& inputs) const override;
    
    int expected_input_count_;
};

// ============================================================================
// 具体的 TA-Lib Factor 实现
// ============================================================================

// 移动平均类
class TALib_SMA : public SingleInputTALibFactor {
public:
    TALib_SMA(const std::shared_ptr<BaseFactor>& input, int period = 30)
        : SingleInputTALibFactor(input, period, "SMA") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.SMA(inputs[0], period_);
    }
};

class TALib_EMA : public SingleInputTALibFactor {
public:
    TALib_EMA(const std::shared_ptr<BaseFactor>& input, int period = 30)
        : SingleInputTALibFactor(input, period, "EMA") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.EMA(inputs[0], period_);
    }
};

class TALib_WMA : public SingleInputTALibFactor {
public:
    TALib_WMA(const std::shared_ptr<BaseFactor>& input, int period = 30)
        : SingleInputTALibFactor(input, period, "WMA") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.WMA(inputs[0], period_);
    }
};

class TALib_DEMA : public SingleInputTALibFactor {
public:
    TALib_DEMA(const std::shared_ptr<BaseFactor>& input, int period = 30)
        : SingleInputTALibFactor(input, period, "DEMA") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.DEMA(inputs[0], period_);
    }
};

class TALib_TEMA : public SingleInputTALibFactor {
public:
    TALib_TEMA(const std::shared_ptr<BaseFactor>& input, int period = 30)
        : SingleInputTALibFactor(input, period, "TEMA") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.TEMA(inputs[0], period_);
    }
};

class TALib_TRIMA : public SingleInputTALibFactor {
public:
    TALib_TRIMA(const std::shared_ptr<BaseFactor>& input, int period = 30)
        : SingleInputTALibFactor(input, period, "TRIMA") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.TRIMA(inputs[0], period_);
    }
};

class TALib_KAMA : public SingleInputTALibFactor {
public:
    TALib_KAMA(const std::shared_ptr<BaseFactor>& input, int period = 30)
        : SingleInputTALibFactor(input, period, "KAMA") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.KAMA(inputs[0], period_);
    }
};

class TALib_T3 : public SingleInputTALibFactor {
public:
    TALib_T3(const std::shared_ptr<BaseFactor>& input, int period = 5, double volume_factor = 0.7)
        : SingleInputTALibFactor(input, period, "T3"), volume_factor_(volume_factor) {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.T3(inputs[0], period_, volume_factor_);
    }

private:
    double volume_factor_;
};

// 动量指标
class TALib_RSI : public SingleInputTALibFactor {
public:
    TALib_RSI(const std::shared_ptr<BaseFactor>& input, int period = 14)
        : SingleInputTALibFactor(input, period, "RSI") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.RSI(inputs[0], period_);
    }
};

class TALib_MOM : public SingleInputTALibFactor {
public:
    TALib_MOM(const std::shared_ptr<BaseFactor>& input, int period = 10)
        : SingleInputTALibFactor(input, period, "MOM") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.MOM(inputs[0], period_);
    }
};

class TALib_ROC : public SingleInputTALibFactor {
public:
    TALib_ROC(const std::shared_ptr<BaseFactor>& input, int period = 10)
        : SingleInputTALibFactor(input, period, "ROC") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.ROC(inputs[0], period_);
    }
};

class TALib_CMO : public SingleInputTALibFactor {
public:
    TALib_CMO(const std::shared_ptr<BaseFactor>& input, int period = 14)
        : SingleInputTALibFactor(input, period, "CMO") {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.CMO(inputs[0], period_);
    }
};

// 波动率指标
class TALib_ATR : public MultiInputTALibFactor {
public:
    TALib_ATR(const std::shared_ptr<BaseFactor>& high, const std::shared_ptr<BaseFactor>& low,
             const std::shared_ptr<BaseFactor>& close, int period = 14)
        : MultiInputTALibFactor({high, low, close}, "ATR", 3), period_(period) {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.ATR(inputs[0], inputs[1], inputs[2], period_);
    }

private:
    int period_;
};

class TALib_NATR : public MultiInputTALibFactor {
public:
    TALib_NATR(const std::shared_ptr<BaseFactor>& high, const std::shared_ptr<BaseFactor>& low,
              const std::shared_ptr<BaseFactor>& close, int period = 14)
        : MultiInputTALibFactor({high, low, close}, "NATR", 3), period_(period) {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.NATR(inputs[0], inputs[1], inputs[2], period_);
    }

private:
    int period_;
};

class TALib_TRANGE : public MultiInputTALibFactor {
public:
    TALib_TRANGE(const std::shared_ptr<BaseFactor>& high, const std::shared_ptr<BaseFactor>& low,
                const std::shared_ptr<BaseFactor>& close)
        : MultiInputTALibFactor({high, low, close}, "TRANGE", 3) {}

protected:
    TALibWrapper::TALibResult compute_single(const std::vector<std::vector<double>>& inputs) override {
        return wrapper_.TRANGE(inputs[0], inputs[1], inputs[2]);
    }
};

// 多输出指标示例
class TALib_BBANDS : public SingleInputTALibFactor {
public:
    TALib_BBANDS(const std::shared_ptr<BaseFactor>& input, int period = 5,
                double nb_dev_up = 2.0, double nb_dev_dn = 2.0)
        : SingleInputTALibFactor(input, period, "BBANDS"), 
          nb_dev_up_(nb_dev_up), nb_dev_dn_(nb_dev_dn) {}

protected:
    int get_output_count() const override { return 3; }  // upper, middle, lower
    
    MultiOutputResult compute_multi_output(const std::vector<std::vector<double>>& inputs) override {
        auto result = wrapper_.BBANDS(inputs[0], period_, nb_dev_up_, nb_dev_dn_);
        MultiOutputResult multi_result;
        multi_result.outputs = {result.upper, result.middle, result.lower};
        multi_result.begin_index = result.begin_index;
        multi_result.nb_elements = result.nb_elements;
        multi_result.success = result.success();
        return multi_result;
    }

private:
    double nb_dev_up_;
    double nb_dev_dn_;
};

class TALib_MACD : public SingleInputTALibFactor {
public:
    TALib_MACD(const std::shared_ptr<BaseFactor>& input, int fast_period = 12,
              int slow_period = 26, int signal_period = 9)
        : SingleInputTALibFactor(input, fast_period, "MACD"),
          slow_period_(slow_period), signal_period_(signal_period) {}

protected:
    int get_output_count() const override { return 3; }  // macd, signal, histogram
    
    MultiOutputResult compute_multi_output(const std::vector<std::vector<double>>& inputs) override {
        auto result = wrapper_.MACD(inputs[0], period_, slow_period_, signal_period_);
        MultiOutputResult multi_result;
        multi_result.outputs = {result.macd, result.signal, result.histogram};
        multi_result.begin_index = result.begin_index;
        multi_result.nb_elements = result.nb_elements;
        multi_result.success = result.success();
        return multi_result;
    }

private:
    int slow_period_;
    int signal_period_;
};

} // namespace Spectre
