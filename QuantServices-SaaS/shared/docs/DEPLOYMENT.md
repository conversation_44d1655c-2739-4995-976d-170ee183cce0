# QuantServices-Saa<PERSON> 部署指南

## 概述

本文档详细介绍了 QuantServices-SaaS 项目的部署方法，包括开发环境、测试环境和生产环境的配置。

## 系统要求

### 硬件要求

**最低配置**:
- CPU: 4核心 2.0GHz
- 内存: 8GB RAM
- 存储: 50GB 可用空间
- 网络: 100Mbps 带宽

**推荐配置**:
- CPU: 8核心 3.0GHz
- 内存: 16GB RAM
- 存储: 200GB SSD
- 网络: 1Gbps 带宽

### 软件要求

**操作系统**:
- Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- Windows Server 2019+ (仅开发环境)
- macOS 11+ (仅开发环境)

**依赖软件**:
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.30+
- Node.js 18+ (开发环境)
- CMake 3.20+ (开发环境)

## 快速部署

### 使用 Docker Compose (推荐)

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd QuantServices-SaaS
   ```

2. **配置环境变量**
   ```bash
   cp shared/config/saas-config.json.example shared/config/saas-config.json
   # 编辑配置文件，修改必要的参数
   ```

3. **启动服务**
   ```bash
   cd docker
   docker-compose up -d
   ```

4. **验证部署**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 检查健康状态
   curl http://localhost/health
   curl http://localhost/api/v1/system/health
   ```

5. **访问应用**
   - 前端应用: http://localhost
   - API 文档: http://localhost/api/v1/docs
   - 监控面板: http://localhost:3001 (Grafana)

## 详细部署步骤

### 1. 环境准备

#### 安装 Docker

**Ubuntu/Debian**:
```bash
# 更新包索引
sudo apt-get update

# 安装依赖
sudo apt-get install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加 Docker GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加 Docker 仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装 Docker
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker
```

**CentOS/RHEL**:
```bash
# 安装依赖
sudo yum install -y yum-utils

# 添加 Docker 仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装 Docker
sudo yum install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker
```

#### 配置 Docker

```bash
# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker

# 验证安装
docker --version
docker compose version
```

### 2. 项目配置

#### 配置文件设置

1. **主配置文件**
   ```bash
   cp shared/config/saas-config.json.example shared/config/saas-config.json
   ```

2. **编辑配置文件**
   ```json
   {
     "application": {
       "name": "QuantServices-SaaS",
       "version": "1.0.0",
       "environment": "production"
     },
     "backend": {
       "server": {
         "host": "0.0.0.0",
         "port": 9080
       }
     },
     "authentication": {
       "secret_key": "your-production-secret-key",
       "token_expiry_hours": 24
     },
     "database": {
       "type": "postgresql",
       "host": "postgres",
       "port": 5432,
       "database": "quantservices_saas",
       "username": "quantservices",
       "password": "your-secure-password"
     }
   }
   ```

3. **环境变量文件**
   ```bash
   # 创建 .env 文件
   cat > docker/.env << EOF
   # Database
   POSTGRES_PASSWORD=your-secure-password
   
   # JWT Secret
   JWT_SECRET=your-jwt-secret-key
   
   # API URLs
   VITE_API_BASE_URL=http://localhost:9080/api/v1
   VITE_WEBSOCKET_URL=ws://localhost:9081
   EOF
   ```

### 3. SSL/TLS 配置 (生产环境)

#### 生成 SSL 证书

**使用 Let's Encrypt**:
```bash
# 安装 certbot
sudo apt-get install certbot

# 生成证书
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem docker/nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem docker/nginx/ssl/key.pem
sudo chown $USER:$USER docker/nginx/ssl/*.pem
```

**使用自签名证书** (仅测试):
```bash
# 创建 SSL 目录
mkdir -p docker/nginx/ssl

# 生成自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout docker/nginx/ssl/key.pem \
  -out docker/nginx/ssl/cert.pem \
  -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
```

#### 启用 HTTPS

编辑 `docker/nginx/nginx.conf`，取消注释 HTTPS 服务器配置块。

### 4. 数据库初始化

#### PostgreSQL 初始化脚本

创建 `docker/postgres/init.sql`:
```sql
-- 创建数据库
CREATE DATABASE quantservices_saas;

-- 创建用户表
CREATE TABLE users (
    user_id VARCHAR(64) PRIMARY KEY,
    username VARCHAR(32) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(32) NOT NULL DEFAULT 'user',
    is_active BOOLEAN NOT NULL DEFAULT true,
    email_verified BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建会话表
CREATE TABLE sessions (
    session_id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL REFERENCES users(user_id),
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    remote_address INET,
    user_agent TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
```

### 5. 服务启动

#### 生产环境部署

```bash
cd docker

# 拉取最新镜像
docker-compose pull

# 构建自定义镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 开发环境部署

```bash
cd docker

# 使用开发配置启动
docker-compose -f docker-compose.dev.yml up -d

# 启动文件监控
docker-compose -f docker-compose.dev.yml --profile watcher up -d
```

### 6. 服务验证

#### 健康检查

```bash
# 检查前端服务
curl -f http://localhost/health

# 检查后端 API
curl -f http://localhost/api/v1/system/health

# 检查 WebSocket 连接
wscat -c ws://localhost/ws/

# 检查数据库连接
docker exec -it quantservices-saas-postgres psql -U quantservices -d quantservices_saas -c "SELECT 1;"
```

#### 功能测试

```bash
# 注册用户
curl -X POST http://localhost/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'

# 用户登录
curl -X POST http://localhost/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'

# 获取市场数据
curl -H "Authorization: Bearer <token>" \
  http://localhost/api/v1/market-data/quotes/latest
```

## 监控和维护

### 日志管理

#### 查看日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs frontend

# 实时跟踪日志
docker-compose logs -f --tail=100
```

#### 日志轮转

编辑 `/etc/docker/daemon.json`:
```json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
```

### 性能监控

#### Prometheus 指标

访问 http://localhost:9090 查看 Prometheus 监控面板。

#### Grafana 仪表板

1. 访问 http://localhost:3001
2. 使用默认凭据登录 (admin/admin)
3. 导入预配置的仪表板

### 备份和恢复

#### 数据库备份

```bash
# 创建备份
docker exec quantservices-saas-postgres pg_dump -U quantservices quantservices_saas > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
docker exec -i quantservices-saas-postgres psql -U quantservices quantservices_saas < backup_file.sql
```

#### 配置备份

```bash
# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz shared/config/
```

### 更新和升级

#### 应用更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build

# 滚动更新服务
docker-compose up -d --no-deps backend
docker-compose up -d --no-deps frontend
```

#### 数据库迁移

```bash
# 运行数据库迁移脚本
docker exec -i quantservices-saas-postgres psql -U quantservices quantservices_saas < migrations/migration_v1.1.0.sql
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :9080
   
   # 检查 Docker 状态
   docker system df
   docker system prune
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose logs postgres
   
   # 测试连接
   docker exec -it quantservices-saas-postgres psql -U quantservices -d quantservices_saas
   ```

3. **前端无法访问后端**
   ```bash
   # 检查网络连接
   docker network ls
   docker network inspect quantservices-network
   
   # 检查防火墙设置
   sudo ufw status
   ```

### 性能优化

1. **调整资源限制**
   ```yaml
   # 在 docker-compose.yml 中添加
   deploy:
     resources:
       limits:
         cpus: '2.0'
         memory: 4G
       reservations:
         cpus: '1.0'
         memory: 2G
   ```

2. **优化数据库配置**
   ```sql
   -- 调整 PostgreSQL 配置
   ALTER SYSTEM SET shared_buffers = '256MB';
   ALTER SYSTEM SET effective_cache_size = '1GB';
   ALTER SYSTEM SET maintenance_work_mem = '64MB';
   SELECT pg_reload_conf();
   ```

## 安全配置

### 防火墙设置

```bash
# 配置 UFW 防火墙
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 5432/tcp   # 禁止外部访问数据库
sudo ufw deny 6379/tcp   # 禁止外部访问 Redis
```

### 安全加固

1. **更改默认密码**
2. **启用 HTTPS**
3. **配置访问控制**
4. **定期更新系统**
5. **监控异常访问**

## 支持和联系

如需技术支持，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.quantservices.com
- 社区: https://community.quantservices.com
