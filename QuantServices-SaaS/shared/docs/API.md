# QuantServices-SaaS API 文档

## 概述

QuantServices-SaaS 提供完整的 RESTful API 和 WebSocket 接口，用于访问量化交易平台的所有功能。

### 基础信息

- **基础 URL**: `http://localhost:9080/api/v1`
- **WebSocket URL**: `ws://localhost:9081`
- **API 版本**: v1.0.0
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON

### 响应格式

所有 API 响应都遵循统一格式：

```json
{
  "success": true,
  "data": { ... },
  "timestamp": 1640995200000
}
```

错误响应格式：

```json
{
  "success": false,
  "error": {
    "message": "错误描述",
    "details": "详细错误信息",
    "code": 400
  },
  "timestamp": 1640995200000
}
```

## 认证 API

### 用户注册

**POST** `/auth/register`

注册新用户账户。

**请求体**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user_id": "user_1234567890abcdef",
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "user",
    "created_at": 1640995200000
  }
}
```

### 用户登录

**POST** `/auth/login`

用户身份验证。

**请求体**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 86400,
    "user": {
      "user_id": "user_1234567890abcdef",
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "user",
      "permissions": ["market_data:read", "portfolio:read"]
    }
  }
}
```

### 用户登出

**POST** `/auth/logout`

**Headers**: `Authorization: Bearer <access_token>`

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "登出成功"
  }
}
```

### 刷新令牌

**POST** `/auth/refresh`

**请求体**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 获取用户信息

**GET** `/auth/profile`

**Headers**: `Authorization: Bearer <access_token>`

**响应**:
```json
{
  "success": true,
  "data": {
    "user_id": "user_1234567890abcdef",
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "user",
    "permissions": ["market_data:read", "portfolio:read"],
    "is_active": true,
    "email_verified": false,
    "created_at": 1640995200000,
    "last_login": 1640995200000
  }
}
```

## 市场数据 API

### 获取实时报价

**GET** `/market-data/quote/{symbol}`

**Headers**: `Authorization: Bearer <access_token>`

**路径参数**:
- `symbol`: 股票代码 (例如: AAPL, 000001.SZ)

**响应**:
```json
{
  "success": true,
  "data": {
    "symbol": "AAPL",
    "price": 150.25,
    "bid": 150.20,
    "ask": 150.30,
    "volume": 1000000,
    "timestamp": 1640995200000,
    "change": 2.50,
    "change_percent": 1.69,
    "high": 151.00,
    "low": 148.50,
    "open": 149.00,
    "close": 147.75
  }
}
```

### 获取多个报价

**GET** `/market-data/quotes?symbols=AAPL,MSFT,GOOGL`

**查询参数**:
- `symbols`: 逗号分隔的股票代码列表

### 获取历史K线数据

**GET** `/market-data/bars/{symbol}`

**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `interval`: 时间间隔 (1m, 5m, 15m, 30m, 1h, 1d)
- `limit`: 返回条数限制 (最大 10000)

**响应**:
```json
{
  "success": true,
  "data": {
    "symbol": "AAPL",
    "bars": [
      {
        "timestamp": 1640995200000,
        "open": 149.00,
        "high": 151.00,
        "low": 148.50,
        "close": 150.25,
        "volume": 1000000,
        "interval": "1d"
      }
    ],
    "count": 1
  }
}
```

### 获取可用股票列表

**GET** `/market-data/symbols`

### 获取市场状态

**GET** `/market-data/market-status`

## 交易 API

### 获取订单列表

**GET** `/trading/orders`

**Headers**: `Authorization: Bearer <access_token>`

**查询参数**:
- `status`: 订单状态 (pending, filled, cancelled)
- `symbol`: 股票代码
- `limit`: 返回条数限制

**响应**:
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "order_id": "order_1234567890",
        "symbol": "AAPL",
        "side": "buy",
        "type": "limit",
        "quantity": 100,
        "price": 150.00,
        "status": "filled",
        "filled_quantity": 100,
        "average_price": 150.25,
        "created_at": 1640995200000,
        "updated_at": 1640995200000
      }
    ],
    "count": 1
  }
}
```

### 下单

**POST** `/trading/orders`

**Headers**: `Authorization: Bearer <access_token>`

**请求体**:
```json
{
  "symbol": "AAPL",
  "side": "buy",
  "type": "limit",
  "quantity": 100,
  "price": 150.00
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "order_id": "order_1234567890",
    "symbol": "AAPL",
    "side": "buy",
    "type": "limit",
    "quantity": 100,
    "price": 150.00,
    "status": "pending",
    "created_at": 1640995200000
  }
}
```

### 取消订单

**DELETE** `/trading/orders/{order_id}`

### 获取持仓

**GET** `/trading/positions`

**响应**:
```json
{
  "success": true,
  "data": {
    "positions": [
      {
        "symbol": "AAPL",
        "quantity": 100,
        "average_price": 150.25,
        "market_value": 15025.00,
        "unrealized_pnl": 25.00,
        "realized_pnl": 0.00,
        "side": "long",
        "created_at": 1640995200000,
        "updated_at": 1640995200000
      }
    ],
    "count": 1
  }
}
```

## 投资组合 API

### 获取投资组合

**GET** `/portfolio`

**Headers**: `Authorization: Bearer <access_token>`

**响应**:
```json
{
  "success": true,
  "data": {
    "total_value": 100000.00,
    "cash": 50000.00,
    "equity": 50000.00,
    "buying_power": 75000.00,
    "day_pnl": 250.00,
    "total_pnl": 5000.00,
    "currency": "USD",
    "updated_at": 1640995200000,
    "positions": [...]
  }
}
```

### 获取投资组合表现

**GET** `/portfolio/performance`

**查询参数**:
- `period`: 时间周期 (1d, 1w, 1m, 3m, 6m, 1y)
- `start_date`: 开始日期
- `end_date`: 结束日期

## 策略 API

### 获取策略列表

**GET** `/strategies`

### 创建策略

**POST** `/strategies`

**请求体**:
```json
{
  "name": "均线策略",
  "description": "基于移动平均线的交易策略",
  "type": "algorithmic",
  "parameters": {
    "short_period": 5,
    "long_period": 20,
    "symbols": ["AAPL", "MSFT"]
  },
  "symbols": ["AAPL", "MSFT"]
}
```

### 启动策略

**POST** `/strategies/{strategy_id}/start`

### 停止策略

**POST** `/strategies/{strategy_id}/stop`

## WebSocket API

### 连接

连接到 WebSocket 服务器：

```javascript
const ws = new WebSocket('ws://localhost:9081');

// 认证
ws.send(JSON.stringify({
  type: 'auth',
  data: {
    token: 'your_access_token'
  }
}));
```

### 订阅市场数据

```javascript
// 订阅报价
ws.send(JSON.stringify({
  type: 'subscribe_quotes',
  data: {
    symbols: ['AAPL', 'MSFT', 'GOOGL']
  }
}));

// 接收报价更新
ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  if (message.type === 'market_data') {
    console.log('报价更新:', message.data.quotes);
  }
};
```

### 订阅交易更新

```javascript
// 订阅订单更新
ws.send(JSON.stringify({
  type: 'subscribe_orders'
}));

// 订阅持仓更新
ws.send(JSON.stringify({
  type: 'subscribe_positions'
}));
```

### 消息格式

WebSocket 消息统一格式：

```json
{
  "type": "message_type",
  "data": { ... },
  "timestamp": 1640995200000
}
```

## 错误代码

| 代码 | 说明 |
|------|------|
| 400 | 请求参数错误 |
| 401 | 未认证或令牌无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |

## 权限系统

### 角色权限

| 角色 | 权限 |
|------|------|
| admin | 所有权限 (*) |
| trader | 市场数据读取、交易、投资组合、策略 |
| viewer | 市场数据读取、交易读取、投资组合读取、策略读取 |
| user | 市场数据读取、投资组合读取 |

### 权限格式

权限采用 `resource:action` 格式：

- `market_data:read` - 读取市场数据
- `trading:read` - 读取交易信息
- `trading:write` - 执行交易操作
- `portfolio:read` - 读取投资组合
- `portfolio:write` - 修改投资组合
- `strategies:read` - 读取策略
- `strategies:write` - 创建和修改策略

## 限制和配额

- API 请求频率：1000 次/分钟
- WebSocket 连接：每用户最多 5 个
- 历史数据查询：最多 10000 条记录
- 订单数量：每秒最多 100 个订单

## SDK 和示例

详细的 SDK 和代码示例请参考：
- JavaScript SDK: `/examples/javascript/`
- Python SDK: `/examples/python/`
- 完整示例: `/examples/complete/`
