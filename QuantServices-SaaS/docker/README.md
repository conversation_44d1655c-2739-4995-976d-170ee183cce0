# QuantServices-<PERSON>a<PERSON> Docker 部署指南

本目录包含 QuantServices-SaaS 项目的 Docker 容器化配置，支持开发和生产环境的部署。

## 目录结构

```
docker/
├── docker-compose.yml          # 生产环境配置
├── docker-compose.dev.yml      # 开发环境配置
├── backend/
│   ├── Dockerfile              # 后端生产环境镜像
│   └── Dockerfile.dev          # 后端开发环境镜像
├── frontend/
│   ├── Dockerfile              # 前端生产环境镜像
│   ├── Dockerfile.dev          # 前端开发环境镜像
│   └── nginx.conf              # 前端 Nginx 配置
├── nginx/
│   └── nginx.conf              # 主 Nginx 反向代理配置
├── postgres/
│   ├── init.sql                # 生产数据库初始化
│   └── init-dev.sql            # 开发数据库初始化
├── prometheus/
│   └── prometheus.yml          # 监控配置
└── grafana/
    └── provisioning/           # Grafana 配置
```

## 快速开始

### 生产环境部署

1. **构建和启动所有服务**
   ```bash
   cd docker
   docker-compose up -d
   ```

2. **查看服务状态**
   ```bash
   docker-compose ps
   ```

3. **查看日志**
   ```bash
   docker-compose logs -f backend
   docker-compose logs -f frontend
   ```

4. **停止服务**
   ```bash
   docker-compose down
   ```

### 开发环境部署

1. **启动开发环境**
   ```bash
   cd docker
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. **启动文件监控（可选）**
   ```bash
   docker-compose -f docker-compose.dev.yml --profile watcher up -d
   ```

3. **使用开发工具容器**
   ```bash
   docker-compose -f docker-compose.dev.yml --profile tools up -d
   docker exec -it quantservices-dev-tools bash
   ```

## 服务说明

### 核心服务

- **backend**: C++ 后端 API 服务
  - 端口: 9080 (HTTP), 9081 (WebSocket)
  - 健康检查: `http://localhost:9080/api/v1/system/health`

- **frontend**: React 前端应用
  - 端口: 3000
  - 健康检查: `http://localhost:3000/health`

- **quantservices**: 原有 QuantServices 后端
  - 端口: 8080
  - 提供市场数据和交易功能

- **nginx**: 反向代理和负载均衡
  - 端口: 80 (HTTP), 443 (HTTPS)
  - 统一入口点

### 可选服务

- **redis**: 缓存服务
  - 端口: 6379
  - 用于会话存储和数据缓存

- **postgres**: PostgreSQL 数据库
  - 端口: 5432
  - 用户数据和配置存储

- **prometheus**: 监控数据收集
  - 端口: 9090
  - 系统指标监控

- **grafana**: 可视化仪表板
  - 端口: 3001
  - 默认用户: admin/admin

## 配置说明

### 环境变量

#### 后端服务
- `CONFIG_FILE`: 配置文件路径
- `LOG_LEVEL`: 日志级别 (debug, info, warn, error)
- `ENVIRONMENT`: 运行环境 (development, production)

#### 前端服务
- `VITE_API_BASE_URL`: 后端 API 基础 URL
- `VITE_WEBSOCKET_URL`: WebSocket 连接 URL
- `NODE_ENV`: Node.js 环境

#### 数据库服务
- `POSTGRES_DB`: 数据库名称
- `POSTGRES_USER`: 数据库用户
- `POSTGRES_PASSWORD`: 数据库密码

### 网络配置

- **生产网络**: `172.20.0.0/16`
- **开发网络**: `172.21.0.0/16`

### 数据卷

- `backend_logs`: 后端日志
- `backend_data`: 后端数据
- `postgres_data`: 数据库数据
- `redis_data`: Redis 数据
- `prometheus_data`: 监控数据
- `grafana_data`: Grafana 配置

## 开发指南

### 本地开发

1. **修改代码**
   - 后端代码修改后需要重新构建镜像
   - 前端代码支持热重载

2. **重新构建服务**
   ```bash
   docker-compose -f docker-compose.dev.yml build backend-dev
   docker-compose -f docker-compose.dev.yml up -d backend-dev
   ```

3. **调试**
   - 后端调试端口: 9999
   - 前端 HMR 端口: 3001

### 数据库管理

1. **连接数据库**
   ```bash
   docker exec -it quantservices-saas-postgres-dev psql -U quantservices -d quantservices_saas_dev
   ```

2. **备份数据库**
   ```bash
   docker exec quantservices-saas-postgres-dev pg_dump -U quantservices quantservices_saas_dev > backup.sql
   ```

3. **恢复数据库**
   ```bash
   docker exec -i quantservices-saas-postgres-dev psql -U quantservices quantservices_saas_dev < backup.sql
   ```

## 生产部署

### 安全配置

1. **更改默认密码**
   - 修改 `docker-compose.yml` 中的数据库密码
   - 更新 JWT 密钥

2. **启用 HTTPS**
   - 取消注释 `nginx.conf` 中的 HTTPS 配置
   - 配置 SSL 证书

3. **防火墙设置**
   - 只开放必要端口 (80, 443)
   - 限制数据库端口访问

### 监控和日志

1. **查看系统指标**
   - Prometheus: `http://localhost:9090`
   - Grafana: `http://localhost:3001`

2. **日志聚合**
   ```bash
   docker-compose logs --tail=100 -f
   ```

3. **健康检查**
   ```bash
   curl http://localhost/health
   curl http://localhost/api/v1/system/health
   ```

### 扩展和优化

1. **水平扩展**
   ```bash
   docker-compose up -d --scale backend=3
   ```

2. **资源限制**
   - 在 `docker-compose.yml` 中添加 `deploy.resources` 配置

3. **性能调优**
   - 调整 Nginx worker 进程数
   - 优化数据库连接池
   - 配置 Redis 内存策略

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   docker-compose logs service-name
   ```

2. **网络连接问题**
   ```bash
   docker network ls
   docker network inspect quantservices-network
   ```

3. **数据卷问题**
   ```bash
   docker volume ls
   docker volume inspect volume-name
   ```

### 重置环境

1. **完全重置**
   ```bash
   docker-compose down -v
   docker system prune -a
   docker-compose up -d
   ```

2. **重置数据库**
   ```bash
   docker-compose stop postgres
   docker volume rm quantservices-saas_postgres_data
   docker-compose up -d postgres
   ```

## 支持

如有问题，请查看：
- 项目文档: `../README.md`
- API 文档: `../shared/docs/API.md`
- 日志文件: 各服务的日志输出
