# QuantServices-SaaS

一个完整的 SaaS 产品架构，为现有的 QuantServices 项目提供现代化的 Web 服务接口。

## 项目概述

QuantServices-SaaS 是基于现有 QuantServices 项目构建的完整 SaaS 解决方案，提供：

- **现代化 Web API 服务**：使用 C++20 和 Boost.Beast 构建的高性能后端
- **响应式前端应用**：基于 React + Vite 的现代化 Web 界面
- **实时数据推送**：WebSocket 支持的实时市场数据和交易信息
- **用户认证系统**：完整的用户管理和权限控制
- **容器化部署**：Docker 支持的一键部署方案

## 项目架构

```
QuantServices-SaaS/
├── backend/          # C++ Web API 服务
│   ├── src/          # 源代码
│   ├── include/      # 头文件
│   ├── config/       # 配置文件
│   ├── tests/        # 测试代码
│   └── CMakeLists.txt
├── frontend/         # React 前端应用
│   ├── src/          # 源代码
│   ├── public/       # 静态资源
│   ├── package.json  # 依赖配置
│   └── vite.config.ts
├── shared/           # 共享配置和文档
│   ├── config/       # 共享配置
│   ├── docs/         # API 文档
│   └── schemas/      # 数据模式定义
└── docker/           # 容器化配置
    ├── backend/      # 后端 Docker 配置
    ├── frontend/     # 前端 Docker 配置
    └── docker-compose.yml
```

## 核心功能

### 后端服务 (C++20)
- RESTful API 接口
- WebSocket 实时通信
- 用户认证和权限管理
- 异步处理和线程安全
- 与现有 QuantServices 集成

### 前端应用 (React + TypeScript)
- 用户登录/注册界面
- 实时市场数据展示
- 交易策略管理
- 历史数据查询和分析
- 交易执行和监控面板
- 系统状态和日志查看

### 技术特性
- UTF-8 编码支持
- 微服务架构
- 配置管理
- 错误处理和日志记录
- 性能监控和指标收集

## 快速开始

### 开发环境要求
- C++20 兼容编译器 (MSVC 2022, GCC 11+, Clang 13+)
- Node.js 18+ 和 npm/yarn
- Docker 和 Docker Compose
- CMake 3.20+

### 构建和运行

#### 后端服务
```bash
cd backend
mkdir build && cd build
cmake ..
cmake --build . --config Release
./QuantServices-SaaS-Backend
```

#### 前端应用
```bash
cd frontend
npm install
npm run dev
```

#### Docker 部署
```bash
docker-compose up -d
```

## API 文档

详细的 API 文档请参考 [shared/docs/API.md](shared/docs/API.md)

## 配置说明

配置文件位于 `shared/config/` 目录，支持：
- 服务器配置
- 数据库连接
- 认证设置
- 日志配置
- 性能参数

## 开发指南

### 后端开发
- 遵循 C++20 现代标准
- 使用异步编程模式
- 实现线程安全
- 集成现有 QuantServices 模块

### 前端开发
- 使用 TypeScript 提供类型安全
- 实现响应式设计
- 集成图表库进行数据可视化
- 实现实时数据更新

## 部署说明

支持多种部署方式：
- 本地开发部署
- Docker 容器化部署
- 云平台部署
- 负载均衡配置

## 许可证

本项目遵循与 QuantServices 相同的许可证。

## 贡献指南

欢迎提交 Issue 和 Pull Request。请确保：
- 代码符合项目规范
- 包含适当的测试
- 更新相关文档

## 联系方式

如有问题或建议，请联系 RoboQuant 团队。
