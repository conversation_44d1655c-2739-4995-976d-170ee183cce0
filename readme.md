# RoboQuant Experiments - 现代化量化交易系统

[![C++20](https://img.shields.io/badge/C%2B%2B-20-blue.svg)](https://en.cppreference.com/w/cpp/20)
[![CMake](https://img.shields.io/badge/CMake-3.20%2B-green.svg)](https://cmake.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

## 🚀 项目概述

RoboQuant Experiments 是一个基于现代 C++20 标准的高性能量化交易系统实验项目。该项目包含了完整的量化交易基础设施，从数据管理、策略执行到交易接口，提供了一套完整的现代化解决方案。

### 🎯 核心目标

- **现代化重构**: 将传统 C++ 代码升级到 C++20 标准
- **高性能架构**: 基于事件驱动和异步编程的高性能设计
- **模块化设计**: 清晰的模块边界和可插拔的组件架构
- **类型安全**: 强类型系统和编译时检查
- **可扩展性**: 支持多种交易接口和数据源

## 📁 项目结构

```
RoboQuant/Experiments/
├── 📊 DataHub/              # 现代化数据管理系统
├── 🏦 Broker/               # 现代化交易接口模块
├── 📈 TradingSvr/           # 现代化交易服务器
├── 🔧 QuantServices/        # 统一服务平台
├── 🌐 QuantServices-SaaS/   # SaaS 产品架构
├── 🔬 spectre/              # 算法和工具库
├── 📚 docs/                 # 项目文档
├── 🏗️ build/                # 构建输出目录
└── 📋 CMakeLists.txt        # 主构建配置
```

## 🏗️ 核心模块

### 📊 DataHub - 数据管理系统

现代化的金融数据管理和处理系统，提供高性能的数据存储、查询和分析功能。

**主要特性:**
- 🔄 实时市场数据处理
- 📈 历史数据管理和分析
- 🏢 证券信息和板块管理
- 📊 技术指标计算
- 🚀 高性能流处理

**技术栈:**
- C++20 标准
- SQLite/LevelDB 存储
- 异步 I/O 处理
- 多级缓存机制

### 🏦 Broker - 交易接口模块

现代化的交易接口抽象层，支持多种交易接口的统一管理。

**主要特性:**
- 🔌 插件式架构
- 🛡️ 统一的风险控制
- 📋 订单生命周期管理
- 💰 多账户支持
- ⚡ 异步事件处理

**支持的接口:**
- CTP (期货)
- Interactive Brokers
- FIX 协议
- 模拟交易

### 📈 TradingSvr - 交易服务器

高性能的量化交易执行引擎，支持策略管理和投资组合管理。

**主要特性:**
- 🎯 策略管理系统
- 📊 投资组合管理
- 🤖 模型管理 (LightGBM, PyTorch, ONNX)
- 🌐 网络通信 (WebSocket, HTTP)
- 📝 表达式引擎

### 🔧 QuantServices - 统一服务平台

集成 DataHub 和 Trading 服务的统一 REST API 服务器。

**主要特性:**
- 🌐 RESTful API 设计
- 🔄 实时数据推送
- 🏥 健康监控
- ⚙️ 配置管理
- 🔒 CORS 支持

### 🌐 QuantServices-SaaS - SaaS 产品

完整的 SaaS 产品架构，提供现代化的 Web 服务接口。

**主要特性:**
- 🖥️ React + TypeScript 前端
- 🔐 用户认证和权限管理
- 📱 响应式 Web 界面
- 🐳 Docker 容器化部署
- 🔄 实时数据可视化

## 🛠️ 技术栈

### 核心技术
- **C++20**: 现代 C++ 标准，使用 concepts、ranges、coroutines
- **CMake 3.20+**: 跨平台构建系统
- **Boost 1.75+**: 网络、线程、文件系统库
- **nlohmann/json**: JSON 处理
- **spdlog**: 高性能日志库

### 数据存储
- **SQLite**: 轻量级关系数据库
- **LevelDB**: 高性能键值存储
- **HDF5**: 科学数据格式

### 网络通信
- **Boost.Beast**: HTTP/WebSocket 服务器
- **WebSocket**: 实时数据推送
- **REST API**: 标准化接口

### 前端技术
- **React 18**: 现代化前端框架
- **TypeScript**: 类型安全的 JavaScript
- **Vite**: 快速构建工具
- **Chart.js**: 数据可视化

## 🚀 快速开始

### 环境要求

- **编译器**: 支持 C++20 的编译器
  - Windows: MSVC 2022
  - Linux: GCC 11+ 或 Clang 13+
- **CMake**: 3.20 或更高版本
- **Node.js**: 18+ (用于前端开发)
- **Docker**: 可选，用于容器化部署

### 构建步骤

#### Windows (PowerShell)

```powershell
# 克隆项目
git clone <repository-url>
cd RoboQuant/Experiments

# 创建构建目录
mkdir build
cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
cmake --build . --config Release

# 运行测试
ctest --output-on-failure
```

#### Linux/macOS

```bash
# 克隆项目
git clone <repository-url>
cd RoboQuant/Experiments

# 创建构建目录
mkdir build && cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
make -j$(nproc)

# 运行测试
ctest --parallel
```

### Docker 部署

```bash
# 构建并启动所有服务
cd QuantServices-SaaS
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📖 使用示例

### DataHub API 使用

```cpp
#include "datahub/DataHub.h"

// 初始化 DataHub
auto datahub = DataHub::Services::DataHubInstance::get();
DataHub::API::DataHubAPI api(datahub);

// 获取实时行情
auto quote_result = api.get_quote("AAPL");
if (quote_result.success) {
    std::cout << "价格: " << quote_result.data.last_price << std::endl;
}

// 获取历史数据
auto bars_result = api.get_bars("AAPL", "1m",
    "2024-01-01T09:30:00", "2024-01-01T16:00:00");
```

### 交易服务器使用

```cpp
#include "trading/Trading.h"

// 创建交易服务器
auto server = RoboQuant::Trading::Factory::create_default_server(8080);

// 创建策略
auto strategy = RoboQuant::Trading::Factory::create_futures_strategy(
    "momentum_strategy", {
        {"entry_threshold", 0.02},
        {"stop_loss_pct", 0.05}
    });

// 启动服务器
auto init_future = server->initialize();
if (init_future.get()) {
    server->start().wait();
}
```

### REST API 调用

```bash
# 获取行情数据
curl http://localhost:8080/api/v1/datahub/quote/AAPL

# 创建交易订单
curl -X POST http://localhost:8080/api/v1/trading/orders \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "AAPL",
    "side": "buy",
    "quantity": 100,
    "order_type": "market"
  }'

# 检查系统健康状态
curl http://localhost:8080/api/v1/system/health
```

## 🏗️ 架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API 接口层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  REST API   │  │ Python API  │  │  C++ API    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   服务层 (Service Layer)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 行情服务     │  │ 交易服务     │  │ 策略服务     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Layer)                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 数据仓库     │  │ 缓存管理     │  │ 交易接口     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   存储层 (Storage Layer)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   SQLite    │  │   LevelDB   │  │    HDF5     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 设计原则

- **SOLID 原则**: 单一职责、开闭原则、里氏替换、接口隔离、依赖倒置
- **现代 C++**: 使用 C++20 特性（概念、协程、模块、范围等）
- **异步编程**: 基于协程的异步 I/O 和并发处理
- **内存安全**: 智能指针、RAII、异常安全
- **可测试性**: 依赖注入、接口抽象、模拟对象

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
cd build
ctest --parallel --output-on-failure

# 运行特定模块测试
ctest -R DataHub

# 生成测试报告
ctest --output-junit test_results.xml
```

### 测试覆盖率

```bash
# 启用覆盖率构建
cmake .. -DENABLE_COVERAGE=ON

# 生成覆盖率报告
make coverage
```

## 📊 性能特性

### 高性能设计

- **零拷贝**: 使用 `std::span` 和移动语义
- **内存池**: 预分配内存减少动态分配
- **SIMD 优化**: 向量化计算
- **无锁编程**: 原子操作和无锁数据结构
- **异步 I/O**: 非阻塞网络和文件操作

### 性能指标

- **延迟**: 微秒级订单处理延迟
- **吞吐量**: 支持每秒数万笔交易
- **并发**: 支持数千并发连接
- **内存**: 优化的内存使用模式

## 🔧 配置管理

### 配置文件结构

```json
{
  "server": {
    "port": 8080,
    "threads": 4
  },
  "datahub": {
    "database_path": "data/market.db",
    "cache_size": 1000000
  },
  "trading": {
    "risk_limits": {
      "max_position_value": 10000000.0,
      "max_daily_loss": 500000.0
    }
  }
}
```

### 环境变量支持

```bash
export ROBOQUANT_CONFIG_PATH=/path/to/config
export ROBOQUANT_LOG_LEVEL=INFO
export ROBOQUANT_DATA_PATH=/path/to/data
```

## 🚀 部署指南

### 本地部署

1. 编译项目
2. 配置数据库和数据源
3. 启动服务

### Docker 部署

```yaml
version: '3.8'
services:
  quantservices:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./config:/app/config
      - ./data:/app/data
```

### 云平台部署

支持部署到：
- AWS ECS/EKS
- Azure Container Instances
- Google Cloud Run
- Kubernetes 集群

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范

- 遵循现代 C++20 最佳实践
- 使用统一的代码格式化工具
- 添加充分的单元测试
- 更新相关文档

### 提交规范

```
type(scope): description

[optional body]

[optional footer]
```

类型：
- `feat`: 新功能
- `fix`: 修复
- `docs`: 文档
- `style`: 格式
- `refactor`: 重构
- `test`: 测试
- `chore`: 构建

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 📞 联系方式

- **项目主页**: https://github.com/roboquant/experiments
- **问题反馈**: https://github.com/roboquant/experiments/issues
- **邮箱**: <EMAIL>
- **文档**: https://docs.roboquant.com

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和社区成员。

---

**RoboQuant Team** - 构建下一代量化交易基础设施