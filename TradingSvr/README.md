# TradingSvr Modern - 现代化交易服务器

## 概述

TradingSvr Modern 是对原有 TradingSvr 模块的完整现代化重构，采用 C++20 标准和现代软件架构设计模式，提供高性能、可扩展的量化交易平台。

## 主要特性

### ? 现代化架构
- **C++20 标准**: 使用最新的 C++ 特性，包括 concepts、ranges、coroutines
- **RAII 设计**: 自动资源管理，避免内存泄漏
- **类型安全**: 强类型系统和编译时检查
- **异步编程**: 基于 std::future 和协程的异步操作

### ? 核心功能
- **策略管理**: 支持期货和股票策略，可插拔架构
- **投资组合管理**: 多投资组合支持，实时风险监控
- **订单管理**: 统一订单接口，支持多种订单类型
- **数据管理**: 统一数据接口，支持多数据源
- **模型管理**: 支持 LightGBM、PyTorch、ONNX 等模型

### ? 技术特性
- **事件驱动**: 基于事件总线的松耦合架构
- **网络通信**: 现代化的 WebSocket 和 HTTP 通信
- **表达式引擎**: 灵活的表达式解析和计算
- **风险管理**: 多层次风险控制和监控
- **性能监控**: 实时性能指标和报告

## 项目结构

```
TradingSvr_Modern/
├── include/trading/          # 头文件
│   ├── Types.h              # 核心类型定义
│   ├── Events.h             # 事件系统
│   ├── Strategy.h           # 策略基类
│   ├── Portfolio.h          # 投资组合管理
│   ├── Position.h           # 持仓管理
│   ├── OrderManager.h       # 订单管理
│   ├── DataProvider.h       # 数据提供者
│   ├── ModelManager.h       # 模型管理
│   ├── ExpressionEngine.h   # 表达式引擎
│   ├── NetworkManager.h     # 网络管理
│   ├── TradingServer.h      # 主服务器
│   ├── Trading.h            # 主头文件
│   └── strategies/          # 策略实现
│       ├── FuturesStrategy.h
│       └── StockStrategy.h
├── src/                     # 源文件
├── tests/                   # 单元测试
├── examples/                # 示例程序
├── config/                  # 配置文件
│   ├── server_config.json
│   ├── strategies.json
│   ├── portfolios.json
│   └── models.json
├── CMakeLists.txt          # 构建配置
└── README.md               # 项目文档
```

## 快速开始

### 环境要求

- **编译器**: 支持 C++20 的编译器 (GCC 10+, Clang 12+, MSVC 2019+)
- **CMake**: 3.20 或更高版本
- **依赖库**:
  - Boost 1.75+
  - Google Test (用于测试)
  - spdlog (日志)
  - nlohmann/json (JSON 处理)

### 编译构建

```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
cmake --build . --config Release

# 运行测试
ctest --output-on-failure
```

### 运行示例

```bash
# 运行主程序
./bin/trading_server_example

# 或指定配置文件
./bin/trading_server_example config/server_config.json
```

## 配置说明

### 服务器配置 (server_config.json)

```json
{
  "server": {
    "id": "trading_server_001",
    "name": "RoboQuant Trading Server",
    "port": 8080,
    "network_threads": 4
  },
  "risk": {
    "global_limits": {
      "max_position_value": 10000000.0,
      "max_daily_loss": 500000.0,
      "max_leverage": 3.0
    }
  }
}
```

### 策略配置 (strategies.json)

```json
{
  "strategies": [
    {
      "id": "futures_momentum_001",
      "name": "Futures Momentum Strategy",
      "type": "FuturesStrategy",
      "enabled": true,
      "parameters": {
        "entry_threshold": 0.02,
        "stop_loss_pct": 0.05,
        "max_position_size": 1000000.0
      }
    }
  ]
}
```

## API 使用

### 创建交易服务器

```cpp
#include "trading/Trading.h"

using namespace RoboQuant::Trading;

// 使用工厂函数创建默认服务器
auto server = Factory::create_default_server(8080);

// 或使用构建器模式
auto server = TradingServerBuilder()
    .with_server_port(8080)
    .with_strategy_config("config/strategies.json")
    .with_risk_limits(risk_limits)
    .build();

// 初始化并启动
auto init_future = server->initialize();
if (init_future.get()) {
    auto start_future = server->start();
    start_future.wait();
}
```

### 创建策略

```cpp
// 创建期货策略
auto strategy = Factory::create_futures_strategy("my_strategy", {
    {"entry_threshold", 0.02},
    {"stop_loss_pct", 0.05},
    {"max_position_size", 1000000.0}
});

// 加载到服务器
StrategyConfig config;
config.id = "my_strategy";
config.name = "My Futures Strategy";
// ... 设置其他参数

auto load_future = server->load_strategy(config);
```

### 创建投资组合

```cpp
// 创建投资组合
auto portfolio = Factory::create_default_portfolio("my_portfolio", 1000000.0);

// 或使用配置
PortfolioConfig config;
config.id = "my_portfolio";
config.name = "My Portfolio";
config.initial_capital = 1000000.0;
config.base_currency = "CNY";

auto create_future = server->create_portfolio(config);
```

## 架构设计

### 分层架构

1. **应用层**: 策略和算法实现
2. **服务层**: 投资组合、订单、风险管理
3. **基础设施层**: 数据访问、网络、模型
4. **核心层**: 通用类型和工具

### 设计模式

- **工厂模式**: 组件创建和配置
- **观察者模式**: 事件通知机制
- **策略模式**: 可插拔的策略实现
- **建造者模式**: 复杂对象构建
- **单例模式**: 全局资源管理

### 并发模型

- **异步操作**: 基于 std::future 的异步编程
- **事件驱动**: 非阻塞的事件处理
- **线程安全**: 使用 std::shared_mutex 保护共享资源
- **协程支持**: 为高并发场景准备

## 测试

### 运行测试

```bash
# 运行所有测试
ctest

# 运行特定测试
./tests/TradingSvr_Modern_Tests --gtest_filter="PortfolioTest.*"

# 生成测试报告
ctest --output-junit test_results.xml
```

### 测试覆盖率

```bash
# 启用覆盖率
cmake .. -DENABLE_COVERAGE=ON

# 生成覆盖率报告
make coverage
```

## 性能优化

### 编译优化

- 使用 Release 模式编译
- 启用 LTO (Link Time Optimization)
- 使用 PGO (Profile Guided Optimization)

### 运行时优化

- 合理设置线程池大小
- 调整事件队列容量
- 优化内存分配策略

## 部署

### Docker 部署

```dockerfile
FROM ubuntu:22.04
RUN apt-get update && apt-get install -y \
    build-essential cmake libboost-all-dev
COPY . /app
WORKDIR /app
RUN mkdir build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release && \
    make -j$(nproc)
CMD ["./build/bin/trading_server_example"]
```

### 系统服务

```ini
[Unit]
Description=RoboQuant Trading Server
After=network.target

[Service]
Type=simple
User=trading
WorkingDirectory=/opt/roboquant
ExecStart=/opt/roboquant/bin/trading_server_example
Restart=always

[Install]
WantedBy=multi-user.target
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目主页: https://github.com/roboquant/TradingSvr_Modern
- 问题反馈: https://github.com/roboquant/TradingSvr_Modern/issues
- 邮箱: <EMAIL>
